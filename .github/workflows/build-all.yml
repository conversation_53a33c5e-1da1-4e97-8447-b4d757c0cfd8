name: Build - Remarq All

on:
  workflow_dispatch:

  workflow_call:

jobs:
  build-uinspect-Development:
      if: ${{ github.ref_name == 'Development' }}
      uses: remarqUK/Trading.Frontend/.github/workflows/build-uinspect.yml@Development

  build-uinspect-Master:
    if: ${{ github.ref_name == 'Master' || github.ref_name == 'Main' }}
    uses: remarqUK/Trading.Frontend/.github/workflows/build-uinspect.yml@Master

  build-uinspect-Demo:
    if: ${{ github.ref_name == 'Demo' }}
    uses: remarqUK/Trading.Frontend/.github/workflows/build-uinspect.yml@Master

  build-crm-Development:
    if: ${{ github.ref_name == 'Development' }}
    uses: remarqUK/Trading.Frontend/.github/workflows/build-crm.yml@Development

  build-crm-Master:
    if: ${{ github.ref_name == 'Master' || github.ref_name == 'Main' }}
    uses: remarqUK/Trading.Frontend/.github/workflows/build-crm.yml@Master

  build-crm-Demo:
    if: ${{ github.ref_name == 'Demo' }}
    uses: remarqUK/Trading.Frontend/.github/workflows/build-crm.yml@Master

  build-remarq-Development:
      if: ${{ github.ref_name == 'Development' }}
      uses: remarqUK/Trading.Frontend/.github/workflows/build-remarq.yml@Development

  build-remarq-Master:
    if: ${{ github.ref_name == 'Master' || github.ref_name == 'Main' }}
    uses: remarqUK/Trading.Frontend/.github/workflows/build-remarq.yml@Master

  build-remarq-Demo:
    if: ${{ github.ref_name == 'Demo' }}
    uses: remarqUK/Trading.Frontend/.github/workflows/build-remarq.yml@Master
