name: 1 - Deploy - <PERSON> Motio to S3

on:
  workflow_dispatch:

  workflow_call:

jobs:
  buildRemarq:
    name: B<PERSON><PERSON>emar<PERSON>
    uses: remarqUK/Trading.Frontend/.github/workflows/build-motiox-dev.yml@InspectCollect2

  deploy:
    needs: buildRemarq
    uses: remarqUK/Trading.Frontend/.github/workflows/deploy-reusable-s3.yml@InspectCollect2
    with:
      AWS_REGION: "eu-west-2"
      PRODUCT_NAME: "remarq"
      S3_BUCKET_NAME: "motiox-dev"
      CLOUDFRONT_DISTRIBUTION_ID: "E311KHA1UYM9FF"
    secrets:
      AWS_SECRET_ACCESS_KEY: "${{ secrets.AWS_SECRET_ACCESS_KEY }}"
      AWS_ACCESS_KEY_ID: "${{ secrets.AWS_ACCESS_KEY_ID }}"
