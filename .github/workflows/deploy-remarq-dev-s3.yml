name: 1 - Deploy - <PERSON> Remarq to S3

on:
  workflow_dispatch:

  workflow_call:

jobs:
  buildRemarq:
    name: B<PERSON>Remar<PERSON>
    uses: remarqUK/Trading.Frontend/.github/workflows/build-remarq-dev.yml@Development

  deploy:
    needs: buildRemarq
    uses: remarqUK/Trading.Frontend/.github/workflows/deploy-reusable-s3.yml@Development
    with:
      AWS_REGION: "eu-west-2"
      PRODUCT_NAME: "remarq"
      S3_BUCKET_NAME: "tradesales-dev"
      CLOUDFRONT_DISTRIBUTION_ID: "E2X2WNP87JZ4DT"
    secrets:
      AWS_SECRET_ACCESS_KEY: "${{ secrets.AWS_SECRET_ACCESS_KEY }}"
      AWS_ACCESS_KEY_ID: "${{ secrets.AWS_ACCESS_KEY_ID }}"
