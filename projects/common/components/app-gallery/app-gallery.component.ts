import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  Input, OnDestroy,
  OnInit,
  ViewChild
} from '@angular/core';
import {FullscreenDirective, FullscreenTransition} from "../../directives";
import {SlickCarouselComponent} from "ngx-slick-carousel";
import {Observable, Subscription} from "rxjs";
import {DomSanitizer} from "@angular/platform-browser";

@Component({
  selector: 'app-gallery',
  templateUrl: './app-gallery.component.html',
  styleUrls: ['./app-gallery.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AppGalleryComponent implements OnInit, OnDestroy, AfterViewInit {

  @ViewChild(FullscreenDirective, {static: true})
  fullscreen!: FullscreenDirective;

  TriggerType = TriggerType;
  trigger: TriggerType = TriggerType.ELEMENT;

  @ViewChild("slickMainCarousel") slickMainCarousel: SlickCarouselComponent;
  @ViewChild("slickThumbCarousel") slickThumbCarousel: SlickCarouselComponent;
  @ViewChild("slickContainer") slickContainer: ElementRef;
  @ViewChild("slickThumbContainer") slickThumbContainer: ElementRef;

  @Input() galleryImages: any[] = [];
  @Input() showThumbnails = true;
  @Input() showMainImage = false;
  @Input() disableFullscreen = false;
  @Input() mainSlidesToShow = 1;

  @Input() thumbnailStyle: { [p: string]: any } | null | undefined;
  @Input() mainImageStyle: { [p: string]: any } | null | undefined;
  @Input() thumbnailContainerStyle: { [p: string]: any } | null | undefined;

  /* IMPORTANT !! Don't use the responsive / breakpoint feature of ngx-slick-carousel, it's not working properly
  responsive: [
    { breakpoint: 500, settings: { slidesToShow: 2, slidesToScroll: 2 } },
    { breakpoint: 600, settings: { slidesToShow: 3, slidesToScroll: 3 } },
    { breakpoint: 700, settings: { slidesToShow: 4, slidesToScroll: 4 } },
    { breakpoint: 1200, settings: { slidesToShow: 6, slidesToScroll: 6 } },
    { breakpoint: 9999, settings: { slidesToShow: 8, slidesToScroll: 8 } },
  ]
  */

  thumbnailConfig = {
    asNavFor: null,
    slidesToShow: 4,
    slidesToScroll: 1,
    initialSlide: 0,
    lazyLoading: 'ondemand',
    useTransform: false,
    focusOnSelect: true,
    respondTo: 'slider',
  };
  currentIndex = 0;

  mainConfig = {
    speed: 50,
    slidesToShow: 4,
    slidesToScroll: 1,
    initialSlide: 0,
    arrows: false,
    useTransform: false,
    focusOnSelect: true,
    asNavFor: null,
    lazyLoading: 'ondemand',
    respondTo: 'slider'
  };
  private previousSlidesToShow = null;
  private sliderInitialised = false;
  sliderWidth: number;
  private refreshSubscription: Subscription;
  isFullscreen = false;
  loadingMainSlide = 0;


  constructor(private cdr: ChangeDetectorRef, private sanitizer: DomSanitizer) {
  }

  ngAfterViewInit(): void {
    if (this.showThumbnails === true && this.slickThumbCarousel?.$instance && this.slickMainCarousel?.$instance) {
      this.slickThumbCarousel.$instance.slick('slickSetOption', 'asNavFor', this.slickMainCarousel.$instance, true);
      this.slickMainCarousel.$instance.slick('slickSetOption', 'asNavFor', this.slickThumbCarousel.$instance, true);
    }
  }

  ngOnInit(): void {

    this.mainConfig.slidesToShow = this.mainSlidesToShow;

    this.initResizeObserver();
  }

  trackByFn(index: number, item: any): any {
    return item.id; // Assuming each item has a unique 'id' property
  }

  ngOnDestroy() {
    if (this.refreshSubscription) {
      this.refreshSubscription.unsubscribe();
    }
  }

  initResizeObserver = () => {

    const observer = new ResizeObserver(entries => {

      entries.forEach(entry => {

        this.sliderWidth = entry.contentRect.width;

        let marginTop = "0px";

        let slidesToShow = 2;
        let slidesToScroll = 1;

        if (this.sliderWidth >= 1200) {
          const thumbHeight = this.slickThumbContainer.nativeElement.clientHeight;
          const browserHeight = window.innerHeight;
          const windowHeight = this.slickContainer.nativeElement.clientHeight;

          marginTop = (-1 * ((windowHeight - browserHeight) + thumbHeight)) + "px";

          slidesToShow = 10;
          slidesToScroll = 5;
        } else if (this.sliderWidth >= 1200) {
          slidesToShow = 6;
        } else if (this.sliderWidth >= 993) {
          slidesToShow = 4;
        } else if (this.sliderWidth >= 550) {
          slidesToShow = 4;
        } else if (this.sliderWidth >= 460) {
          slidesToShow = 3;
        } else {
          slidesToShow = 2;
        }

        this.thumbnailConfig.slidesToShow = slidesToShow;
        this.thumbnailConfig.slidesToScroll = slidesToScroll;
        this.slickThumbContainer.nativeElement.style.marginTop = marginTop;

        // We've resized, so we have to reinit the slides bu unslick + reslicking
        if (this.previousSlidesToShow !== this.thumbnailConfig.slidesToShow) {

          if (this.sliderInitialised) {
            this.refreshSlides();
          }

          this.previousSlidesToShow = this.thumbnailConfig.slidesToShow;
          this.sliderInitialised = true;
        }

      });
    });

    observer.observe(document.getElementById('thumb-carousel'));
  }

  refreshSlides() {

    this.slickThumbCarousel.unslick();
    this.slickMainCarousel.unslick();
    this.slickThumbCarousel.initSlick();
    this.slickMainCarousel.initSlick();

    if (this.showThumbnails === true) {
      this.slickThumbCarousel.$instance.slick('slickSetOption', 'asNavFor', this.slickMainCarousel.$instance, true);
      this.slickMainCarousel.$instance.slick('slickSetOption', 'asNavFor', this.slickThumbCarousel.$instance, true);
    }
  }

  toggleFullScreen(trigger: TriggerType) {

    if (!this.disableFullscreen) {
      this.trigger = trigger;
      this.cdr.detectChanges();
      this.fullscreen.toggle();
    }
  }

  onFullscreenChange(event: FullscreenTransition) {
    this.isFullscreen = event.isFullscreen;
  }

  mainChange(e) {
    this.mainConfig.initialSlide = e.currentSlide;
    this.thumbnailConfig.initialSlide = e.currentSlide;
    this.currentIndex = e.currentSlide;
  }

  openUrl(url: string) {
    window.open(url);
  }

  thumbChange(event: { event: any; slick: any; currentSlide: number; first: boolean; last: boolean }) {
  }

  beforeChange(event: { event: any; slick: any; currentSlide: number; nextSlide: number }) {

    if (!this.mainImageStyle) {
      this.mainImageStyle = {};
    }

    this.mainImageStyle['background-image'] = `url(${this.galleryImages[event.nextSlide]?.small})`;
  }
}

export enum TriggerType {
  ELEMENT = 'ELEMENT',
  DOCUMENT = 'DOCUMENT',
}
