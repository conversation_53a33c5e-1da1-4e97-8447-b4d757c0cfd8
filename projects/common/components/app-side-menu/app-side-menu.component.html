<links>
  <li>
    <ul class="collapsible collapsible-accordion">
      <mdb-accordion [multiple]="false" aria-multiselectable="false">

        <div *ngFor="let menuItem of menuItems">

          <div class="nav-item" [class]="menuItem.itemRoute == router.url ? 'selected' : ''">

            <mdb-accordion-item [ngClass]="menuItem.subMenu?'':'no-collase'">
              <mdb-accordion-item-head mdbWavesEffect [ngClass]="menuItem.line?'nav-line':''">
                <div class="d-flex">
                  <div class="flex-grow-1">
                    <a *ngIf="menuItem.itemRoute" routerLink="{{ menuItem.itemRoute }}" routerLinkActive="active"
                       href="#"
                       [class]="menuItem.class"
                       class="nav-item-link"
                       mdbWavesEffect>{{ menuItem.itemLabel }}</a>
                    <div *ngIf="!menuItem.itemRoute">{{ menuItem.itemLabel }}</div>
                  </div>
                  <div class="flex-shrink-1" *ngIf="menuItem.badge" class="menu-badge">
                    <div class="badge-value {{ menuItem.badgeClass }}">
                      {{ menuItem.badge }}
                    </div>
                  </div>
                </div>
              </mdb-accordion-item-head>
              <mdb-accordion-item-body *ngIf="menuItem.subMenu">
                <ul>
                  <li *ngFor="let subMenuItem of menuItem.subMenu">
                    <a routerLink="{{ subMenuItem.itemRoute }}"
                       class="nav-item-link"
                       routerLinkActive="active" href="#"
                       mdbWavesEffect>{{ subMenuItem.itemLabel }}</a>
                  </li>
                </ul>
              </mdb-accordion-item-body>
            </mdb-accordion-item>
          </div>
        </div>
      </mdb-accordion>
    </ul>
</links>
<div class="sidenav-bg mask-strong"></div>
