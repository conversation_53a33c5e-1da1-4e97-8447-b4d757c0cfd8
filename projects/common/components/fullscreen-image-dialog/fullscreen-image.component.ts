import { Component, OnInit } from '@angular/core';
import { MDBModalRef } from 'ng-uikit-pro-standard';
import {Subject} from 'rxjs';

@Component({
  selector: 'app-fullscreen-image-dialog',
  templateUrl: './fullscreen-image-dialog.component.html',
  styleUrls: ['./fullscreen-image-dialog.component.scss']
})
export class FullscreenImageComponent implements OnInit {

  subject: Subject<boolean>;

  public title: string;
  public body: string;
  public buttonActions = [{ label: 'Stay', action: false}, { label: 'Leave', action: true }];

  constructor(public modalRef: MDBModalRef) {}

  ngOnInit(): void {
  }

  action(index: number) {
    this.modalRef.hide();
    this.subject.next(this.buttonActions[index].action);
    this.subject.complete();
  }

  falseAction() {
    this.modalRef.hide();
    this.subject.next(false);
    this.subject.complete();
  }
}
