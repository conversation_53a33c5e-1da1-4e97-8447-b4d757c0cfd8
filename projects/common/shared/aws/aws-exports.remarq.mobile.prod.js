const baseUrl = "https://app.tradesales.com";

const awsmobile = {
  "aws_project_region": "eu-west-2",
  "aws_cognito_identity_pool_id": "eu-west-2:919a960b-05aa-4815-9882-5ff64fb34198",
  "aws_cognito_region": "eu-west-2",
  "aws_user_pools_id": "eu-west-2_XCgDowdx7",
  "aws_user_pools_web_client_id": "1627ico7f8365h57ag3953oc5g",
  "oauth": {
    "domain": "auth.tradesales.com", // Should we use cognito itself as a domain ?
    "scope": [
      "phone",
      "email",
      "openid",
      "profile",
      "aws.cognito.signin.user.admin"
    ],
    "redirectSignIn": baseUrl + "/login",
    "redirectSignOut": baseUrl,
    "responseType": "code"
  },
  "federationTarget": "COGNITO_USER_POOLS"
}
export default awsmobile;
