<div id="site-{{ theme }}" class="h-100 d-flex" style="flex-direction: column"> <!-- DONT USE CLASS flex-column -->
  <div class="flex-grow-1" style="overflow-x: hidden; overflow-y: auto;">
    <div [id]="isMobile ? 'mobile' : 'desktop'" [class]="isDevice ? 'device' : 'computer'"
         class="h-100 d-flex" style="flex-direction: column"> <!-- DONT USE CLASS flex-column -->
      <div id="main-content" style="flex: 1 0 auto;">
        <app-topbar></app-topbar>

        <router-outlet></router-outlet>
      </div>
      <div style="flex-shrink: 0">
        <app-footer></app-footer>
      </div>
    </div>
  </div>
</div>
