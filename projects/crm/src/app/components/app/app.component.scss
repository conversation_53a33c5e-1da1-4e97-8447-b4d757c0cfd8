.close {
  color: var(--dialogCloseIconColour) !important;
}

.btn-primary, .bg-primary, .btn.btn-primary {

  background-color: var(--primaryButtonColour) !important;
  border-color: var(--primaryButtonColour) !important;
  color: var(--colour3);
  font-weight: 700 !important;

  &:focus {
    background-color: var(--primaryButtonColour) !important;
  }
}

.btn-success {
  &:focus {
    background-color: var(--successColour) !important;
  }
}

.btn-primary-outline, .btn-outline-primary, .bg-primary-outline {

  border: 1px solid var(--primaryColour) !important;
  border-radius: 3px;
  color: var(--primaryColour) !important;

  &:hover, &:active, &:focus {

    background-color: var(--primaryColour) !important;
    color: var(--colour3) !important;

  }
}

.btn-secondary, .bg-secondary, .badge.bg-secondary {
  background-color: var(--secondaryButtonColour) !important;
  border-color: var(--secondaryButtonColour) !important;
  color: var(--colour3);
}

.btn-link, .btn-tertiary {
  background-color: var(--linkColour) !important;
  border-color: var(--linkColour) !important;
  color: var(--colour3);

  &:hover, &:visited, &:active {
    color: var(--colour3) !important;
    opacity: 0.9 !important;
  }
}

.btn-secondary-outline, .btn-outline-secondary, .bg-secondary-outline {
  border: 1px solid var(--secondaryColour) !important;
  border-radius: 3px;
  color: var(--secondaryColour) !important;

  &:hover, &:active, &:focus {
    background-color: var(--secondaryColour) !important;
    opacity: 0.9 !important;
    color: var(--colour3) !important;
  }
}

.btn-tertiary-outline, .btn-outline-tertiary, .bg-tertiary-outline {
  border: 1px solid var(--linkColour) !important;
  color: var(--linkColour) !important;
  border-radius: 3px;

  &:hover, &:active, &:focus {
    background-color: var(--linkColour) !important;
    color: var(--colour3) !important;
  }
}

// Allows badges in mdb-tabs to be vertically aligned
.badger {

  &.bg-badge-color {
    background-color: var(--primaryButtonColour);
    color: #fff;

    &.active {
      background-color: var(--secondaryButtonColour);
    }
  }

  margin-top: 1px !important;
  line-height: 22px;
  border-radius: 11px !important;
  min-width: 22px !important;
  min-height: 22px !important;
  padding: 0 7px !important;
  text-align: center !important;
  vertical-align: top !important;


}

.hideZero0 {
  display: none !important;
}


.btn-danger {
  background-color: var(--danger) !important;
  border-color: var(--danger) !important;
}

.btn-danger-outline, .btn-outline-danger {
  border: 1px solid var(--danger) !important;
  color: var(--danger);
  border-radius: 3px;

  &:hover, &:active, &:focus {

    background-color: var(--danger) !important;
    color: var(--colour3) !important;

  }
}

.btn-inline-colour {

  background-color: #e9ecef;
  border: 1px solid var(--softInputBorderColour);
  color: var(--textColour);

  &:hover {
    background-color: rgba(0,0,64,0.1);

  }

}

.action-fa-colour {

  color: var(--secondaryButtonColour);
}

.form-control.input-feint {
  border: 1px solid #ced4da !important;
}

.btn-feint {
  background-color: var(--feintColour);
  border-color: var(--feintColour);
  color: var(--colour23);
}

.btn-xs {
  padding: 0.1rem 0.25rem !important;
  font-size: 0.8rem;
}

/* TABLES */

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(240, 241, 250, 0.5) !important;
}

.table-striped tbody tr:hover {
  background-color: rgba(230, 231, 240, 0.5) !important;
}

table td, table.table-compressed td {

  font-size: 0.8rem;
  line-height: 1.5rem;
  cursor: pointer;
  font-weight: 400;

  .table-line-1 {
    font-weight: 500;
    line-height: 0.9rem;
    font-size: 0.8rem;
  }

  .table-line-2 {
    font-weight: 300;
    line-height: 0.9rem;
    font-size: 0.75rem;
    color: #666;
  }
}

/* FONTS */



@font-face {
  font-family: MontSerrat;
  src: url(/assets/fonts/montserrat/Montserrat-VariableFont_wght.ttf) format("truetype");
  font-weight: 100 900;
  font-display: swap;
}

@font-face {
  font-family: Inter;
  src: url(/assets/sites/bigauction/fonts/Inter-VariableFont_slnt,wght.ttf) format("truetype");
  font-weight: 100 900;
  font-display: swap;
}

@font-face {
  font-family: roc-grotesk;
  src: url(/assets/sites/bigauction/fonts/font1.woff2) format("woff2");
  font-weight: 300;
  font-display: swap;
}

@font-face {
  font-family: roc-grotesk;
  src: url(/assets/sites/bigauction/fonts/font2.woff2) format("woff2");
  font-weight: 600;
  font-display: swap;
}

@font-face {
  font-family: roc-grotesk;
  src: url(/assets/sites/bigauction/fonts/font3.woff2) format("woff2");
  font-weight: 900;
  font-display: swap;
}

@font-face {
  font-family: "Toyota";
  src: url(/assets/sites/toyotagb/fonts/ToyotaType-Regular.woff2) format("woff2");
  font-weight: 100 200 300;
  font-display: swap;
}

@font-face {
  font-family: "Toyota";
  src: url(/assets/sites/toyotagb/fonts/ToyotaType-SemiBold.woff2) format("woff2");
  font-weight: 400 500 600;
  font-display: swap;
}

@font-face {
  font-family: "Toyota";
  src: url(/assets/sites/toyotagb/fonts/ToyotaType-Book.woff2) format("woff2");
  font-weight: 700 800 900;
  font-display: swap;
}


@font-face {
  font-family: "Poppins";
  src: url(/assets/sites/inchcapecrm/fonts/Poppins-ExtraLight.ttf) format("truetype");
  font-weight: 100;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src: url(/assets/sites/inchcapecrm/fonts/Poppins-Thin.ttf) format("truetype");
  font-weight: 200;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src: url(/assets/sites/inchcapecrm/fonts/Poppins-Light.ttf) format("truetype");
  font-weight: 300;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src: url(/assets/sites/inchcapecrm/fonts/Poppins-Medium.ttf) format("truetype");
  font-weight: 400;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src: url(/assets/sites/inchcapecrm/fonts/Poppins-Regular.ttf) format("truetype");
  font-weight: 500;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src: url(/assets/sites/inchcapecrm/fonts/Poppins-SemiBold.ttf) format("truetype");
  font-weight: 600;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src: url(/assets/sites/inchcapecrm/fonts/Poppins-Bold.ttf) format("truetype");
  font-weight: 700;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src: url(/assets/sites/inchcapecrm/fonts/Poppins-ExtraBold.ttf) format("truetype");
  font-weight: 800;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src: url(/assets/sites/inchcapecrm/fonts/Poppins-Black.ttf) format("truetype");
  font-weight: 900;
  font-display: swap;
}

h1 {
  letter-spacing: -1px;
  font-size: 18px;
  font-weight: 600;
  color: var(--headerColour);
}

h2, .sub-header {
  font-size: 14px;
  font-weight: 500;
}

h3 {
  font-size: 14px;
  font-weight: 400;
}

.round-button {

  color: var(--colour7);
  cursor: pointer;

  &:hover {
    opacity: 0.9;
  }
}

.modal-background {
  background-color: #f8f8f8;
}

.modal-body .form-control {
  background-color: var(--colour3) !important;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
  -webkit-text-fill-color: var(--inputTextColour) !important;
  -webkit-box-shadow: 0 0 0 1000px var(--inputBackgroundColour) inset !important;
  transition: background-color 5000s ease-in-out 0s !important;
}

.custom-control-input:focus:not(:checked) ~ .custom-control-label::before {
  border-width: 0 !important;
  border-color: transparent !important;
}

input.form-control::placeholder {

  color: var(--placeholderColour) !important;
  font-weight: var(--placeholderWeight) !important;

}


body, table, .table {
  color: var(--textColour);
}

.vrm-style {
  background-color: var(--vrmBackgroundColour) !important;
  font-weight: 700;
}

.table th {
  border-top: 0 !important;
  text-transform: uppercase;
  font-weight: 600;
  font-size: 0.6rem;
  color: var(--textLabelColour);
}

html,body,app-root {
  height: 100%;
  width: 100%;
  margin: 0;
}

body {
  background-color: var(--bgColour) !important;
  font-family: var(--defaultFont);
  font-weight: 300;
}

a, a:visited, .link-style {
  color: var(--linkColour) !important;
  cursor: pointer;

  &.link-style-danger {
    color: var(--dangerColour) !important;
  }

  &.link-style-basic {
    color: var(--textColour) !important;
  }

  &.link-style-action {
    color: var(--actionColour) !important;
  }
}

.form-control {

  border: 1px solid var(--inputBorderColour) !important;
  border-radius: 3px !important;

  &:focus {
    border: 1px solid var(--colour7) !important;
    box-shadow: none !important;
    -webkit-box-shadow: none;
  }
}


/* FOR AUTO FILL FUNNY BUSINESS */
.md-form > input[type]:-webkit-autofill:not(.browser-default):not([type=search]) + label, .md-form > input[type=time]:not(.browser-default) + label {
  display: inline-block !important;
  width: auto !important;
  //transform: translateY(-7px) translateX(6px) scale(0.8) !important;
  position: relative;
  top: var(--floatLabelTop);
  left: var(--floatLabelleft);
  font-size: var(--floatLabelFontSize);
  font-weight: var(--floatLabelWeight);
}


.pair-label {

  text-transform: uppercase;
  font-weight: 500;
  font-size: 0.7rem;
  color: var(--textLabelColour);
}

app-vehicle-valuation {

  .pair-value {

    font-size: 0.75rem;

  }

}

.ticklist {
  &:before {
    content: "\f00c"; /* FontAwesome Unicode */
    font-family: var(--fontAwesome), serif;
    display: inline-block;
    margin-left: -1.3em; /* same as padding-left set on li */
    width: 1.3em; /* same as padding-left set on li */
  }
}

.cursor-pointer {
  cursor: pointer;
}

.widget {


  background-color: var(--widgetBgColour);
  border-radius: var(--widgetBorderRadius);
  border: 1px solid var(--widgetBorderColour);

  &.widget-no-border {

    border: 0;
  }

  &.inline {
    display: inline-block;
  }

  &.padding {
    padding: 0.5rem 0.5rem;
  }

  &.form-padding {
    padding: 1.5rem 1rem;
  }

  &.widget-padding {
    padding: 1.5rem 1.5rem;
  }

  &.side-padding {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .small-header {

    font-size: 0.9rem;
    font-weight: 400;
  }

  .header {

    font-size: 1.1rem;
    font-weight: 600;

    &.padding-top {

      padding-top: 0.50rem;
    }

    &.padding {

      padding-bottom: 0.75rem;

    }
  }

  &.margin {
    margin-bottom: 1rem;
  }
}

.widget-label {
  font-size: 1rem;
  font-weight: 500;
}

.page-header, h1.page-header {
  line-height: 38px;
  margin-bottom: 0 !important;
}

.header-margin {

  margin-bottom: 10px;

}


/* Style "standard" bootstrap checkboxes */
.custom-checkbox .custom-control-input:checked ~ .custom-control-label::before {
  border-color: var(--smallCheckboxColour) !important;
  background-color: var(--smallCheckboxColour) !important;
  box-shadow: none !important;
}

.btn:focus {
  box-shadow: none !important;
}

/* BOOTSTRAP 4 MDB */

.form-check .form-check-input {
  width: 1rem;
  height: 1rem;
}



