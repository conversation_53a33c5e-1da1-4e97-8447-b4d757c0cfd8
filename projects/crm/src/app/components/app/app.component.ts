import {Component, Inject, OnInit, NgZone, ViewEncapsulation} from '@angular/core';
import {Router} from "@angular/router";
import {DOCUMENT} from "@angular/common";
import {Meta, Title} from "@angular/platform-browser";
import {App, URLOpenListener} from '@capacitor/app';
import {Capacitor} from "@capacitor/core";
import {DomainData, GlobalConstants} from '../../global/shared';
import {LoggerService} from "../../global/services";
import {} from "../../global/interfaces";
import {} from "../../global/enums";

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  encapsulation: ViewEncapsulation.None
})

export class AppComponent implements OnInit {

  public static globals: DomainData;
  public isMobile = GlobalConstants.IsMobile;
  public isDevice = GlobalConstants.IsDevice;

  constructor(private router: Router,
              @Inject(DOCUMENT) private document: Document,
              private titleService: Title,
              private metaService: Meta,
              private zone: NgZone,
              private logService: LoggerService
  ) {

    AppComponent.globals = GlobalConstants.getPlatformDetails(GlobalConstants.CompanyProductCode.CRM, window.location.host);

    if (AppComponent.globals == null) {
      console.log("NAVIGATING TO NOT CONFIGURED " + window.location.host);
      this.router.navigate(["/assets/html/notConfigured.html"]);
    }

    this.theme = AppComponent.globals.theme;
    this.platformName = AppComponent.globals.platformName;
    this.loadCSSForTheme();
    this.setTitle();
    this.setMeta();
    this.setFavicon();

    if (Capacitor.isNativePlatform) {
      this.addMobileListeners();
    }
  }

  logger = this.logService.taggedLogger(this.constructor?.name);

  public showChatWidget = false;
  public theme = "";
  platformName: string;

  async ngOnInit() {
  }

  loadCSSForTheme() {

    const head = this.document.getElementsByTagName('head')[0];

    this.appendStyle(head, 'global-style', "global-styles");
    this.appendStyle(head, 'global-mobile', "global-mobile");
    this.appendStyle(head, 'global-variables', "global-variables");
    this.appendStyle(head, 'global-app-style', "app.component");
    this.appendStyle(head, 'client-theme', AppComponent.globals.theme + "-theme");
    this.appendStyle(head, 'client-variables', AppComponent.globals.theme + "-variables");
  }

  appendStyle(head, id, cssPath) {

    const themeLink = this.document.getElementById(id) as HTMLLinkElement;

    if (themeLink) {
      themeLink.href = cssPath;
    } else {
      const style = this.document.createElement('link');
      style.id = id;
      style.rel = 'stylesheet';
      style.href = `${cssPath}.css`;

      head.appendChild(style);
    }

  }

  setTitle() {
    this.titleService.setTitle(this.platformName + " - Trading Platform");
  }

  setMeta() {
    this.metaService.addTags([
      {name: 'keywords', content: this.platformName + ",Trading,Vehicle Remarketing,Car Auction"},
      {name: 'description', content: this.platformName + " Online Vehicle Remarketing - Trading Platform"}
    ]);
  }

  setFavicon() {
    const faviconId = this.document.getElementById("favicon") as HTMLLinkElement;
    if (faviconId != null) {
      faviconId.href = "/assets/sites/" + this.theme + "/images/favicon.ico";
    }

    const appleTouchIcon = this.document.getElementById("apple-touch-icon") as HTMLLinkElement;
    if (appleTouchIcon != null) {
      appleTouchIcon.href = "/assets/sites/" + this.theme + "/images/apple-touch-icon.png";
    }

    const icon16 = this.document.getElementById("png16") as HTMLLinkElement;
    if (icon16 != null) {
      icon16.href = "/assets/sites/" + this.theme + "/images/favicon-16x16.png";
    }

    const icon32 = this.document.getElementById("png32") as HTMLLinkElement;
    if (icon32 != null) {
      icon32.href = "/assets/sites/" + this.theme + "/images/favicon-32x32.png";
    }
  }

  addMobileListeners() {

    App.addListener('appStateChange', ({isActive}) => {
      this.logger.warn('App state changed. Is active?', isActive);
    });

    App.addListener('appUrlOpen', event => {
      this.zone.run(() => {

        this.logger.debug('App opened with URL:', JSON.stringify(event));

        const domain = 'https://app.bigauction.uk';
        const pathArray = event.url.split(domain);

        // Get the last element with pop()
        const appPath = pathArray.pop();

        this.logger.debug('AppPath', appPath);


        /* DB Redirect on mobile - FIX
        (Auth as any)._handleAuthResponse(appPath).then(() => {
          this.router.navigateByUrl(appPath);
        });
         */
      });
    });

    App.addListener('appRestoredResult', data => {
      this.logger.debug('Restored state:', JSON.stringify(data));
    });
  }
}
