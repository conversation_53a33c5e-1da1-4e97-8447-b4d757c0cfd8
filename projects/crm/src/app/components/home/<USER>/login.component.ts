import {AfterViewInit, Component, OnInit} from '@angular/core';
import {NavigationEnd, Router} from "@angular/router";
import {Subscription} from "rxjs";
import {Capacitor} from "@capacitor/core";
import {AuthenticatorService} from "@aws-amplify/ui-angular";
import {URLService} from "../../../services/url.service";
import {User} from '../../../global/interfaces';
import {CognitoService, LoggerService, UserService} from '../../../global/services';
import {fetchAuthSession} from "aws-amplify/auth";

@Component({
  // tslint:disable-next-line:component-selector
  selector: 'login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent implements OnInit, AfterViewInit {

  logorsign: any;
  user: User;
  private readonly urlSub: Subscription;

  logger = this.logService.taggedLogger(this.constructor?.name);

  public formFields = {
    signUp: {
      name: {
        placeholder: "Name",
        order: 1,
      },
      email: {
        order: 2,
        placeholder: "E-mail",
      },
      password: {
        label: "Password",
        order: 3
      },
      confirm_password: {
        label: "Confirm Password",
        order: 4
      }
    },
  };


  constructor(
    private cognitoService: CognitoService,
    private userService: UserService,
    private redirect: URLService,
    public authenticated: AuthenticatorService,
    private logService: LoggerService,
    private router: Router
  ) {


    this.logorsign = (this.router.url) === "/login" ? "" : "signUp";

    if (!this.urlSub) {

      this.urlSub = this.router.events.subscribe(async (event) => {

        this.logger.debug("LOGIN: RECEIVED ROUTER EVENT ", event);

        if (event instanceof NavigationEnd && event.url.startsWith("/login")) {

          fetchAuthSession({forceRefresh: true}).then((x) => {

            this.logger.debug("LOGIN: CURRENT USER ", x);

            if (!Capacitor.isNativePlatform()) {
              console.log("REDIRECTING TO DASHBOARD");
              this.redirect.dashboard();
            }

          }).catch((x) => {
            this.logger.error("LOGIN: USER NOT LOGGED IN", x);
          });
        }
      });
    }
  }

  ngOnInit() {


    this.authenticated.subscribe(message => {
      if (this.authenticated.authStatus === "authenticated") {

        this.logger.info("USER IS NOW AUTHENTICATED");
        this.redirect.crmDashboard();
      }
    });
  }

  async ngAfterViewInit() {
  }

  async OnDestroy() {

    this.urlSub.unsubscribe();
  }
}

