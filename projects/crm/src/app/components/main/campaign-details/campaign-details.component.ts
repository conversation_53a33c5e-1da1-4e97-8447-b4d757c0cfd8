import {Component, OnD<PERSON>roy, OnInit, ViewChild} from '@angular/core';
import {ActivatedRoute} from '@angular/router';
import {IndividualConfig, ToastService} from 'ng-uikit-pro-standard';
import {UntypedFormBuilder} from '@angular/forms';
import {CampaignDTO} from "../../../interfaces/index";
import {URLService} from "../../../services/url.service";
import {CampaignService} from "../../../services/campaign.service";
import {EventService} from "../../../services/event.service";

@Component({
  selector: 'app-campaign-details',
  templateUrl: './campaign-details.component.html',
  styleUrls: ['./campaign-details.component.scss']
})
export class CampaignDetailsComponent implements OnInit, OnDestroy {

  campaignId: number;
  campaign: CampaignDTO;
  private toastOpts: IndividualConfig;

  constructor(
    private activeRoute: ActivatedRoute,
    private campaignService: CampaignService,
    private toast: ToastService,
    private formBuilder: UntypedFormBuilder,
    private eventService: EventService,
    public adminUrl: URLService,
  ) {
  }

  ngOnInit(): void {

    this.campaignId = this.activeRoute.snapshot.params.campaignId;

    // load lead data with all notes
    this.campaignService.getCampaign(this.campaignId, {component: 'campaign-details'}).then(result => {
      this.campaign = result;
    });
  }

  ngOnDestroy() {
  }

  editCampaign($event: MouseEvent, campaign: CampaignDTO) {

  }
}
