import {Component, OnInit} from '@angular/core';
import {SphLeadSourceEnum} from '../../../global/enums/index';
import {UserService} from '../../../global/services';
import {LeadCRMService} from "../../../services";

@Component({
  selector: 'app-crm-dashboard',
  templateUrl: './crm-dashboard.component.html',
  styleUrls: ['./crm-dashboard.component.scss']
})
export class CRMDashboardComponent implements OnInit {

  public leadsGenerated;
  public salesClosed;
  public leadsGenerated2;
  public salesClosed2;

  colors = [
    'rgb(248, 177, 149)',  // F8B195
    'rgb(246, 114, 128)',  // F67280
    'rgb(192, 108, 132)',  // C06C84
    'rgb(108, 91, 123)',   // 6C5B7B
    'rgb(53, 92, 125)'     // 355C7D
  ];

  sphLeadSourceEnum = SphLeadSourceEnum;

  public chartOptions: any = {
    responsive: true,
    maintainAspectRatio: false,
    elements: {
      line: {tension: 0.1}
    },
    plugins: {
      datalabels: {
        anchor: 'center',
        align: 'end',
        font: {
          size: 14,
        },
        backgroundColor: 'rgba(51, 51, 51, 0.5)',
        borderRadius: 4,
        color: 'white'
      },
      legend: {
        display: true,
        labels: {
          color: 'rgb(255, 99, 132)'
        },
        title: {
          padding: 20
        }
      }
    }
  };


  constructor(private userService: UserService, private leadCRMService: LeadCRMService) {
  }

  ngOnInit(): void {

    this.userService.loadCurrentUser().then((x) => {
    });

    this.leadCRMService.getChartData().then(res => {
      console.log("Chart Data: ", res)

      const leadsBySource = res.find(x => x.id == 1);
      const leadsByDate = res.find(x => x.id == 2);
      const salesBySource = res.find(x => x.id == 3);
      const salesByDate = res.find(x => x.id == 4);

      console.log("SALES BY DATE: ", salesByDate)

      this.leadsGenerated = {
        labels: leadsBySource.datas.map(l => l.label),
        datasets: [
          {
            label: leadsBySource.chartName,
            data: leadsBySource.datas.map(l => l.value),
            backgroundColor: this.colors,
          }
        ]
      };

      this.leadsGenerated2 = {
        labels: leadsByDate.datas.map(l => l.label),
        datasets: [
          {
            label: leadsByDate.chartName,
            data: leadsByDate.datas.map(l => l.value),
            backgroundColor: this.colors,
          }
        ]
      };

      this.salesClosed = {
        labels: salesBySource.datas.map(l => l.label),
        datasets: [
          {
            label: salesBySource.chartName,
            data: salesBySource.datas.map(l => l.value),
            backgroundColor: this.colors,
          }
        ]
      };

      this.salesClosed2 = {
        labels: salesByDate.datas.map(l => l.label),
        datasets: [
          {
            label: salesByDate.chartName,
            data: salesByDate.datas.map(l => l.value),
            backgroundColor: this.colors,
          }
        ]
      };
    });

    // create dummy data
    // this.leadsGenerated = {
    //   labels: ['Customer Referral', 'Website Enquiry', 'Press Advert', 'Dealer Auction', 'CarWow', 'MotorWay', 'Radio'],
    //   datasets: [
    //     {
    //       label: 'Leads Generated',
    //       data: [25, 7, 12, 25, 37, 29, 52],
    //       backgroundColor: this.colors,
    //     }
    //   ]
    // };
    // this.leadsGenerated2 = {
    //   labels: ['22/09', '23/09', '24/09', '25/09', '26/09', '27/09', '28/09'],
    //   datasets: [
    //     {
    //       label: 'Leads Generated',
    //       data: [25, 7, 12, 25, 37, 29, 52],
    //       backgroundColor: this.colors,
    //     }
    //   ]
    // };

    // this.salesClosed = {
    //   labels: ['Customer Referral', 'Website Enquiry', 'Press Advert', 'Dealer Auction', 'CarWow', 'MotorWay', 'Radio'],
    //   datasets: [
    //     {
    //       label: 'Sales Closed',
    //       data: [9, 4, 10, 15, 17, 22, 32],
    //       backgroundColor: this.colors,
    //     }
    //   ]
    // };
    //
    // this.salesClosed2 = {
    //   labels: ['22/09', '23/09', '24/09', '25/09', '26/09', '27/09', '28/09'],
    //   datasets: [
    //     {
    //       label: 'Sales Closed',
    //       data: [9, 4, 10, 15, 17, 22, 32],
    //       backgroundColor: this.colors,
    //     }
    //   ]
    // };
  }

  public chartClicked(e: any): void {
  }

  public chartHovered(e: any): void {
  }
}
