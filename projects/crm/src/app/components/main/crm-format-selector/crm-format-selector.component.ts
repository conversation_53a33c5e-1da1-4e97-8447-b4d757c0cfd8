import {Component, EventEmitter, OnInit, Output} from '@angular/core';
import {IOption, User} from "../../../global/interfaces/index";
import {UserService} from '../../../global/services/index';

@Component({
  selector: 'app-crm-format-selector',
  templateUrl: './crm-format-selector.component.html',
  styleUrls: ['./crm-format-selector.component.scss']
})
export class CRMFormatSelectorComponent implements OnInit {

  private currentUser: User;
  showDisplayFormats = false;
  displayFormats: IOption[] = [];

  // tslint:disable-next-line:variable-name
  _displayFormat: string;

  @Output() displayFormatEvent: EventEmitter<string> = new EventEmitter<string>();

  set displayFormat(value) {
    this._displayFormat = value;
    this.displayFormatEvent.emit(value);
    this.saveDisplayFormat(value);
  }

  get displayFormat() {
    return this._displayFormat;
  }

  constructor(private userService: UserService) {

    if (!this.displayFormat) {
      this.displayFormat = localStorage.getItem("displayFormat") || "CarBuying";
    }

    this.displayFormats = [
      {value: "CarBuying", label: "Car Buying"},
      {value: "CRM", label: "Remarq"}
    ];
  }


  async ngOnInit() {
    await this.userService.loadCurrentUser();
    this.currentUser = this.userService.CurrentUser;
  }

  saveDisplayFormat(format) {
    localStorage.setItem("displayFormat", format);
  }
}
