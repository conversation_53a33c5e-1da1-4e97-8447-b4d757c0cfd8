<app-lead-screen [title]="'CRM Users'"></app-lead-screen>


<div class="widget padding">

  <div class="d-flex">
    <div class="flex-grow-1">
      <div class="btn-group btn-xs" *ngIf="allocationAttrib">
        <label class="btn btn-primary-outline btn-xs" [(ngModel)]="allocationAttrib.attribChar" (click)="setAllocationType()"
               mdbRadio="none">No Lead Allocation</label>
        <label class="btn btn-primary-outline btn-xs" [(ngModel)]="allocationAttrib.attribChar" (click)="setAllocationType()"
               mdbRadio="robin">Round Robin</label>
        <label class="btn btn-primary-outline btn-xs" [(ngModel)]="allocationAttrib.attribChar" (click)="setAllocationType()"
               mdbRadio="fewest">Fewest Leads</label>
      </div>
    </div>
    <div class="flex-shrink-1">
      <div class="switch green-white-switch pr-2">
        <label>
          <span class="switch-label" style="font-size: 12px; font-weight: bold;">Active Only &nbsp;</span>
          <label>
            <input type="checkbox" [(ngModel)]="activeOnly" (ngModelChange)="activeOnlyChange()">
            <span class="lever"></span>
          </label>
        </label>
      </div>
    </div>
  </div>

  <div *ngIf="loading" class="text-center pt-5 pb-5">

    <i class="fa fa-spin fa-spinner fa-2x"></i>
    <h1 class="mt-2">Loading</h1>

  </div>
  <table class="w-100 table table-narrow table-striped crm-user-table" *ngIf="!loading">
    <thead>
    <tr>
      <th>Contact Name</th>
      <th>Short Name</th>
      <th>Phone</th>
      <th>Roles</th>
      <th class="crm-status-column">CRM Status</th>
      <th class="crm-status-column">Live Leads</th>
      <th class="account-column">Account</th>
    </tr>
    </thead>
    <tbody>
    <tr *ngFor="let crmUser of filtered(crmUsers)">
      <td>
        <div class="table-line-1">{{ crmUser?.contact?.contactName}}</div>
      </td>
      <td>
        <span (click)="setShortName(crmUser)">
        <span *ngIf="crmUser?.shortName?.length > 0" class="short-name">
          {{ crmUser?.shortName }}
        </span>
        <span *ngIf="(! crmUser?.shortName) || crmUser?.shortName?.length == 0">
          <span class="btn btn-xs btn-primary-outline">Set</span>
        </span>
        </span>
      </td>
      <td>{{ crmUser?.contact?.phone1 }}</td>
      <td>
        <span *ngFor="let role of crmUser?.contact?.roles" class="badge badge-primary mr-1">{{ role.roleName }}</span>
      </td>
      <td class="crm-status-column">
        <div class="switch green-white-switch">
          <label>
            <input type="checkbox" [(ngModel)]="crmUser.isAvailable" (ngModelChange)="changeAvailable(crmUser)">
            <span class="lever" style="margin-left: 0.5rem; margin-right: 0.6rem;"></span>
            <span *ngIf="crmUser.isAvailable" class="avail-status">Available</span>
            <span *ngIf="!crmUser.isAvailable" class="avail-status">Not Available</span>
          </label>
        </div>
      </td>
      <td>
        <span
          (click)="adminUrl.crmLeads({ onlyMe: false, activeOnly: true, ownerId: crmUser.contactId })">
          <strong>{{ ownerLeadCount(crmUser) }}</strong>
          leads</span>
      </td>
      <td class="account-column">{{ statusName[crmUser?.contact?.statusId] }}</td>
    </tr>
    </tbody>
  </table>
</div>

<div mdbModal #setShortNameModal="mdbModal" class="modal fade"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true" [config]="{backdrop: 'static'}">
  <div class="modal-dialog modal-md modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">

        <div>
          <button type="button" class="close pull-right" aria-label="Close" (click)="setShortNameModal.hide()"><span
            aria-hidden="true">×</span></button>
          Set Short Name for {{ shortNameUser?.contact?.contactName }}
        </div>

      </div>
      <div class="modal-body">

        <div class="d-flex" *ngIf="shortNameUser">
          <div class="flex-grow-1 narrow-select">
            <input class="form-control" [(ngModel)]="shortNameUser.shortName" [maxLength]="5">
            <span style="font-size: 0.8rem; font-weight: bold;">Max. length 5 letters</span>
          </div>
          <div class="flex-shrink-1">
            <div class="pl-2">
              <span class="btn btn-primary-outline" (click)="setShortNameModal.hide()">Cancel</span>
            </div>
          </div>
          <div class="flex-shrink-1">
            <div class="pl-2">
              <button
                [disabled]="shortNameUser != null && shortNameUser?.shortName?.length < 2"
                class="btn btn-primary" (click)="confirmSetShortName()">
                <span *ngIf="settingName"><i class="fa fa-spin fa-spinner"></i></span>
                <span *ngIf="!settingName">OK</span>
              </button>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>

