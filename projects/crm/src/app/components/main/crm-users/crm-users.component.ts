import {Component, OnInit, ViewChild} from '@angular/core';
import {CRMUserDTO, CRMUserMaintDTO} from "../../../interfaces/index";
import {CRMUserService} from "../../../services/crm-user.service";
import {ModalDirective} from "ng-uikit-pro-standard";
import {CRMAttribDTO} from "../../../interfaces/index";
import {LeadCRMService} from "../../../services/lead-crm.service";
import {URLService} from "../../../services/url.service";
import {ContactDTO, ContactSearchDTO, User} from '../../../global/interfaces/index';
import {AttribEnum, CRMUserStatusEnum, StatusEnum, UserRolesEnum} from '../../../global/enums/index';
import {CustomerService, HelpersService, UserService} from '../../../global/services/index';

@Component({
  selector: 'app-crm-users',
  templateUrl: './crm-users.component.html',
  styleUrls: ['./crm-users.component.scss']
})
export class CRMUsersComponent implements OnInit {

  public crmUsers: CRMUserMaintDTO[] = [];
  private currentUser: User;
  contacts: ContactDTO[] = [];
  private existingUsers: CRMUserDTO[] = [];
  statusName: {} = {};
  crmUserStatusEnum = CRMUserStatusEnum;
  statusOptions: { value: number, label: string }[];
  private crmUser: CRMUserDTO;

  @ViewChild("updateUserStatusModal") updateUserStatusModal: ModalDirective;
  @ViewChild("setShortNameModal") setShortNameModal: ModalDirective;
  loading = true;
  activeOnly = true;
  shortNameUser: CRMUserMaintDTO;
  settingName = false;
  newShortName: string;
  private ownerLeads: any;
  allocationAttrib: CRMAttribDTO;

  constructor(
    private customerService: CustomerService,
    private leadService: LeadCRMService,
    private crmUserService: CRMUserService,
    private userService: UserService,
    private helpersService: HelpersService,
    public adminUrl: URLService,
  ) {
  }

  ngOnInit(): void {

    const statuses = this.helpersService.getNamesAndValues(StatusEnum);

    this.statusOptions = statuses.map(status => {
      this.statusName[status.value] = status.name;
      return {label: status.name, value: status.value};
    });

    this.userService.loadCurrentUser()
      .then(() => {
        this.currentUser = this.userService.CurrentUser;
        return this.fetchContacts();
      }).then(() => {
      return this.fetchCRMUsers();
    }).then(() => {
      return this.mergeContacts();
    });

    this.getLeadCounts();
    this.getCRMAttribs();

  }

  getLeadCounts() {

    this.leadService.getLeadsByOwner({component: "crm-users", countOnly: true}).then((result) => {

      this.ownerLeads = result.results;
    });
  }

  getCRMAttribs() {

    this.leadService.getCRMAttribs().then((x) => {

      if (x.totalItems > 0) {
        this.allocationAttrib = x.results.find(z => z.attribId == AttribEnum.crm_allocation_type);
      } else {
        this.allocationAttrib = {
          attribId: AttribEnum.crm_allocation_type,
          attribChar: 'none',
          statusId: StatusEnum.Active
        };
      }
    });
  }

  setAllocationType() {

    if (this.allocationAttrib.id == null) {
      this.leadService.createCRMAttribs(this.allocationAttrib);
    } else {

      this.leadService.patchCRMAttribs(AttribEnum.crm_allocation_type, {attribChar: this.allocationAttrib.attribChar});
    }
  }

  ownerLeadCount(crmUser) {

    const exists = this.ownerLeads.find(x => x.ownerId === crmUser.contactId);

    if (!exists) {
      return 0;
    }

    return exists.leadCount;

  }

  fetchContacts(searchDTO: ContactSearchDTO = {filters: {}}): Promise<any> {

    searchDTO.component = "crm-users-generic";
    searchDTO.filters.hasRole = UserRolesEnum.CRMUser;

    return this.leadService.getCRMUserContacts(this.currentUser.customerId, searchDTO).then((x) => {
      this.contacts = x.results;
    });
  }

  fetchCRMUsers(): Promise<any> {

    return this.crmUserService.search({component: "crm-users"}).then((x) => {
      this.existingUsers = x.results;
    });
  }

  crmUserStatusString(x) {

    return CRMUserStatusEnum[x] as string;

  }

  private mergeContacts() {

    this.crmUsers = this.existingUsers;

    this.crmUsers.map(x => x.isAvailable = (x.userStatus === CRMUserStatusEnum.Available));

    if (this.contacts.length > 0) {

      this.contacts.forEach(x => {

        let crmUser = this.crmUsers.find(z => z.contactId === x.id);

        if (crmUser == null) {
          this.crmUsers.push({contact: x, userStatus: CRMUserStatusEnum.Undefined, isAvailable: false});
        } else {
          crmUser.contact = x;
        }
      });
    }

    this.loading = false;
  }

  setCRMUserStatus(crmUser: CRMUserDTO) {

    this.crmUser = crmUser;
    this.updateUserStatusModal.show();
  }

  changeAvailable(crmUser: CRMUserMaintDTO) {

    // We don't have a CRM record for this contact, create one
    if (crmUser.id == null) {
      this.crmUserService.create({contactId: crmUser.contact.id, userStatus: CRMUserStatusEnum.Available})
        .then((result) => {
          if (result.isValid) {
            crmUser.id = result.dto.id;
          }
        });
    } else {
      this.crmUserService.patch(crmUser.id, {
        userStatus: (crmUser.isAvailable === true ?
          CRMUserStatusEnum.Available : CRMUserStatusEnum.Unavailable)
      });
    }
  }

  activeOnlyChange() {

  }

  filtered(crmUsers: CRMUserMaintDTO[]) {

    if (this.activeOnly) {

      return crmUsers.filter(x => x.contact.statusId == StatusEnum.Active || x.contact.statusId == StatusEnum.Pending);
    }

    return crmUsers;
  }

  setShortName(crmUser: CRMUserDTO) {

    this.shortNameUser = crmUser;
    this.setShortNameModal.show();
  }

  confirmSetShortName() {

    // TODO: ENSURE TWO PEOPLE DON'T HAVE THE SAME SHORTNAME ??

    this.crmUserService.patch(this.shortNameUser.id, {shortName: this.shortNameUser.shortName}).then(() => {
      this.setShortNameModal.hide();
    });
  }
}
