import {Component, EventEmitter, Input, OnInit, Output, ViewChild} from '@angular/core';
import {ModalDirective} from "ng-uikit-pro-standard";
import {LeadVehicleAppraisalDTO} from "../../../interfaces";

@Component({
  selector: 'app-lead-appraisal-details-modal',
  templateUrl: './lead-appraisal-details-modal.component.html',
  styleUrls: ['./lead-appraisal-details-modal.component.scss']
})
export class LeadAppraisalDetailsModalComponent implements OnInit {

  constructor() { }

  @ViewChild('appraisalModal') appraisalModal: ModalDirective;
  @Input() leadVehicleAppraisal: LeadVehicleAppraisalDTO;

  private _showDialog: boolean;
  @Input('showDialog') set showDialog(value: boolean) {
    if (value && !this._showDialog) {
      // show modal
      this.appraisalModal.show();
    }
    if (!value && this._showDialog) {
      // hide modal
      this.appraisalModal.hide();
      this.modalClosed.emit(true);
    }

    this._showDialog = value;
  }

  @Output() modalClosed: EventEmitter<boolean> = new EventEmitter<boolean>();

  ngOnInit(): void {

  }

  closeDialog() {
    this.showDialog = false;
  }
}
