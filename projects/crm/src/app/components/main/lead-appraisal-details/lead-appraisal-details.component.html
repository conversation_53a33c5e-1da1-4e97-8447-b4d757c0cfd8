<div *ngFor="let section of sortedSections(sections)">
  <div style="background-color: #eee; padding: 4px;" class="d-flex flex-wrap mb-1">
    <div style="font-weight: 500; font-size: 0.875rem;" class="flex-grow-1">
      {{ section.internalLabel }}
    </div>
    <div class="flex-shrink-1" style="font-size: 0.7rem; line-height: 1.2rem;">
      <div *ngIf="section.sectionComplete?.complete; else notComplete">
        {{ section.sectionComplete?.updated | date: 'dd/MM/yyyy HH:mm' }}
        <i class="fa fa-check" style="color: forestgreen; margin-left: 5px;"></i>
      </div>
    </div>
  </div>

  <div *ngFor="let question of section.questions">

    <div *ngFor="let media of question.medias">
      QM
    </div>
  </div>

  <div *ngFor="let media of section.medias">
    <img src="{{ media.mediaURL }}" style="width: 100%;">
  </div>
</div>

<ng-template #notComplete>
  Incomplete <i class="fa fa-times" style="margin-left: 5px; color: #c00; font-size: 0.8rem"></i>
  <ng-template>
