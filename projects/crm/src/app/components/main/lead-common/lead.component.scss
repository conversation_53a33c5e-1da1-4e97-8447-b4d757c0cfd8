.remove-contact-inline {
  float: right !important;
  display: inline-block;
  font-size: 12px;
  cursor: pointer;
}

h1.page-header {

  line-height: 1.1rem;

  .title {
    font-weight: 700;
  }

  .subtitle {
    margin-top: 3px;
    font-weight: 600;
    letter-spacing: 0;
    opacity: 0.7;
    font-size: 14px;
    line-height: 14px;
  }
}

.choice-container {

  flex: 1 0 200px;

}

.row-selected {
  td {
    background-color: #007bff !important;

    .table-line-1, .table-line-2 {
      color: #fff;
    }
  }
}

.error-message.under-select {
  left: 19px !important;
  top: 46px;
  color: var(--errorColour);
  font-weight: 400;
  font-size: 0.65rem;
  line-height: 0.65rem;
  padding-left: var(--floatLabelPadding);
  padding-right: var(--floatLabelPadding);
  background-color: var(--floatLabelBackgroundColour);
}

.custom-control-label {

  line-height: 20px;
  font-size: 0.875rem;
  font-weight: 600;

}

.divider {
  margin-top: 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid #666;
}

.overflow-y-auto {

  overflow-y: auto;

}

.create-label {
  text-transform: uppercase;
  font-size: 0.7rem;
}

.create-value {
  font-size: 0.825rem;
  font-weight: 600;
}

table.table-striped {

  tr:nth-of-type(odd) {
    background-color: #f0f1fa80;
  }

}

.scroll-table {
  max-height: 125px;
  overflow-y: auto;
}

.summary-box {
  height: 145px;
}

#address {
  height: 110px;
}


.autoCompleteBox {
  pointer-events: auto;
  max-height: 180px;
  top: 40px;
  left: 0;
  width: 432px;
  background-attachment: scroll;
  background-clip: border-box;
  background-color: rgb(255, 255, 255);
  background-origin: padding-box;
  box-shadow: rgba(0, 0, 0, 0.25) 0px 2px 5px;
  box-sizing: border-box;
  color: rgb(41, 41, 41);
  display: block;
  font-size: 16px;
  font-weight: 300;
  height: 154px;
  line-height: 24px;
  overflow-x: hidden;
  overflow-y: auto;
  position: absolute;
  right: 0;
  text-align: left;
  z-index: 110;

}

.result-item {

  padding: 3px 5px 3px 10px;

  &:hover {
    background-color: var(--colour12) !important;
    color: var(--colour3) !important;
    cursor: pointer;

    a {
      color: var(--colour3) !important;
    }
  }

  &:nth-of-type(even) {
    background-color: #eee;
  }

  .phone {
    font-size: 0.75rem;
    line-height: 0.875rem
  }

  .added {
    color: #888;
    font-size: 0.7rem;
    line-height: 0.85rem
  }

  .title {
    font-weight: 500;
  }

  .line-1 {
    font-size: 0.75rem;
    line-height: 0.875rem;
  }
}


.titleIcon {
  display: inline-block;
  border-radius: 13px;
  font-size: 18px;
  line-height: 26px;
  font-weight: 600;
  padding: 1px 8px 4px 8px;
  margin-bottom: 2px;
  margin-top: 2px;
  vertical-align: middle;
  margin-right: 10px;

  &.lead {
    border: 2px solid #4285F4;
    color: #4285F4;
  }

  &.contact {
    border: 2px solid #34A853;
    color: #34A853;
  }

  &.account {
    border: 2px solid #d4af37;
    color: #d4af37;
  }

  &.campaign {
    border: 2px solid purple;
    color: purple;
  }
}


.customer-info {
  display: grid;
  grid-gap: 5px;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));

  .md-form.mb-1 {
    margin-bottom: 5px !important;
  }
}
