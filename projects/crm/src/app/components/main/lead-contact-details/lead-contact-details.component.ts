import {Component, OnInit} from '@angular/core';
import {ActivatedRoute} from '@angular/router';
import {CampaignDTO, LeadContactDTO, LeadContactLinkDTO, LeadNoteDTO} from '../../../interfaces';
import {ToastService} from 'ng-uikit-pro-standard';
import {LeadDTO} from '../../../interfaces/index';
import {LeadCRMService} from "../../../services/lead-crm.service";
import {URLService} from "../../../services/url.service";
import {EventService} from "../../../services/event.service";
import {LoggerService} from '../../../global/services/index';
import {CRMTypeEnum, LeadEventTypeEnum, SphLeadSourceEnum} from '../../../global/enums';
import {DatePipe} from "@angular/common";
import {Subscription} from "rxjs";
import {DomainData} from "../../../global/shared";
import {DataServiceInterface} from "../../../global/services/interfaces";

@Component({
  selector: 'app-lead-contact-details',
  templateUrl: './lead-contact-details.component.html',
  styleUrls: ['./lead-contact-details.component.scss', '../../lead-crm.scss'],
  providers: [DatePipe]
})
export class LeadContactDetailsComponent implements OnInit {

  leadContactLinks: LeadContactLinkDTO[] = [];
  private crmEventSub: Subscription;
  globals: DomainData;
  crmTypeEnum = CRMTypeEnum;
  loadingLeadContactLinks = false;
  loadingLeadContact = false;

  constructor(private activeRoute: ActivatedRoute,
              private leadService: LeadCRMService,
              private eventService: EventService,
              private toast: ToastService,
              private urlService: URLService,
              private logService: LoggerService,
              private data: DataServiceInterface) {

    this.globals = data.globals;

    this.crmEventSub = this.eventService.LeadCRMEvent.subscribe((data) => {
      if (data.eventType === LeadEventTypeEnum.LeadContactAdded) {
        this.fetchLeadContact(this.leadContactId);
      }
    });
  }

  logger = this.logService.taggedLogger(this.constructor?.name);

  leadContactId: string;
  leadContact: LeadContactDTO;
  leadContactLink: LeadContactLinkDTO;
  leads: LeadDTO[];
  sphLeadSourceEnum = SphLeadSourceEnum;

  toastOpts: { opacity: 0.98 };

  campaigns: CampaignDTO[] = [];
  notes: LeadNoteDTO[] = [];

  async ngOnInit() {

    this.leadContactId = this.activeRoute.snapshot.params.contactId;

    this.fetchLeadContact(this.leadContactId);
    this.fetchleadContactLinks(this.leadContactId);
  }

  fetchLeadContact(leadContactId) {

    this.loadingLeadContact = true;

    this.leadService.getLeadContact(this.leadContactId, {component: 'lead-contact-details'}).then(result => {

      this.loadingLeadContact = false;

      if (result.isValid) {

        this.leadContact = result.dto as LeadContactDTO;

        this.logger.log("Loaded lead contact: ", this.leadContact);
      } else {
        this.toast.error(result.message, "Error", this.toastOpts);
      }
    });
  }

  fetchleadContactLinks(leadContactId) {

    this.loadingLeadContactLinks = true;

    this.leadService.getContactLinks(leadContactId, {component: 'lead-contact-details'}).then((result) => {

      this.loadingLeadContactLinks = false;

      this.leadContactLinks = result.results as LeadContactLinkDTO[];

      // Sort leadContactLinks by date
      this.leadContactLinks.sort((a, b) => {
        return new Date(b.added).getTime() - new Date(a.added).getTime();
      });

      this.leadContactLinks.forEach((link) => {
        link.lead.leadNotes.forEach((note) => {

          const tmpNote = note as LeadNoteDTO;
          tmpNote.lead = link.lead;

          this.notes.push(tmpNote);
        });
      });

      // Sort notes by date
      this.notes.sort((a, b) => {
        return new Date(b.added).getTime() - new Date(a.added).getTime();
      });
    });
  }

  leadDetails(lead: LeadDTO) {
    this.urlService.crmLeadView(lead.id);
  }

  editLeadContact(leadContact: any) {
    event.stopPropagation();
    this.eventService.LeadCRMEvent.emit({eventType: LeadEventTypeEnum.ShowLeadContactModal, object: {leadContact}});
  }
}
