import {AfterViewInit, Component, Input, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators} from '@angular/forms';
import {ToastService} from 'ng-uikit-pro-standard';
import {ModalDirective} from "ng-uikit-pro-standard";
import {Subscription} from 'rxjs';
import {compare} from "fast-json-patch";
import {EventService} from "../../../services/event.service";
import {LeadCRMService} from "../../../services/lead-crm.service";
import {LoggerService, UserService} from "../../../global/services/index";
import {User} from '../../../global/interfaces/index';
import {LeadEventTypeEnum, SphLeadSourceEnum} from "../../../global/enums/index";
import {LeadContactDTO, LeadContactLinkDTO, LeadDTO} from '../../../interfaces';

@Component({
  selector: 'app-lead-contact-modal',
  templateUrl: './lead-contact-modal.component.html',
  styleUrls: ['./lead-contact-modal.component.scss']
})
export class LeadContactModalComponent implements OnInit, OnDestroy, AfterViewInit {

  lead: LeadDTO;

  private user: User;
  private preEdit: LeadContactDTO;
  leadContactLink: LeadContactLinkDTO;

  constructor(private eventService: EventService,
              private formBuilder: UntypedFormBuilder,
              private userService: UserService,
              private leadService: LeadCRMService,
              private toast: ToastService,
              private logService: LoggerService) {
  }

  logger = this.logService.taggedLogger(this.constructor?.name);

  @ViewChild('contactModal') contactModal: ModalDirective;

  isEdit = false;
  leadContact: LeadContactDTO;
  crmEventSub: Subscription;

  form: UntypedFormGroup;
  submitted = false;
  sphLeadSourceEnum = SphLeadSourceEnum;
  leadContactId = null;

  toastOpts: { opacity: 0.98 };

  ngOnInit(): void {

    this.user = this.userService.CurrentUser;

    this.form = this.formBuilder.group({
      id: new UntypedFormControl(),
      leadId: new UntypedFormControl(),
      forename: new UntypedFormControl('', [Validators.required]),
      surname: new UntypedFormControl('', [Validators.required]),
      email: new UntypedFormControl('', [Validators.required]),
      phone: new UntypedFormControl('', []),
      mobile: new UntypedFormControl('', [Validators.required]),
      contactRole: new UntypedFormControl('')
    });

    this.crmEventSub = this.eventService.LeadCRMEvent.subscribe((data) => {

      if (data.eventType === LeadEventTypeEnum.ShowLeadContactModal) {
        this.initShowLeadContactModal(data);
      }
    });
  }

  initShowLeadContactModal(data) {

    this.leadContact = data.object?.leadContact;
    this.lead = data.object?.lead;
    this.leadContactLink = data.object?.leadContactLink;

    this.isEdit = (this.leadContact?.id != null);

    this.logger.log("LEAD ", this.lead);

    if (this.isEdit) {
      this.setFormControlsEdit();
      this.preEdit = this.form.value;
    }

    this.contactModal.show();
  }

  ngAfterViewInit() {
  }

  ngOnDestroy() {
    if (this.crmEventSub) {
      this.crmEventSub.unsubscribe();
    }
  }

  setFormControlsEdit() {
    this.form.patchValue(this.leadContact);
    this.form.patchValue({contactRole: this.leadContactLink?.contactRole});
  }

  get f() {
    return this.form.controls;
  }

  saveContact() {

    this.submitted = true;
    this.form.markAllAsTouched();

    if (this.form.valid) {

      const dto = Object.assign({}, this.form.value);

      if (this.isEdit) {
        this.patchContact(dto).then((x) => {
        });
      } else {
        this.addContact(dto).then((x) => {
        });
      }

      this.contactModal.hide();

    } else {

      this.logger.log("ERRORS ", this.form.errors);
      this.logger.log("FORM INVALID");

    }
  }

  async patchContact(dto) {

    const patch = compare(this.preEdit, dto);

    const result = await this.leadService.patchLeadContact(dto.id, patch);

    if (dto.contactRole != null) {

      const patch2 = compare({}, {contactRole: dto.contactRole});
      const result2 = await this.leadService.patchLeadContactLink(this.leadContactLink.id, patch2);
    }

    this.eventService.LeadCRMEvent.emit({eventType: LeadEventTypeEnum.LeadContactAdded, object: result.dto});
    this.eventService.LeadCRMEvent.emit({eventType: LeadEventTypeEnum.LeadContactLinkAdded, object: result.dto});
  }

  async addContact(dto) {

    const result = await this.leadService.addLeadContact(dto);

    if (!result.isValid) {
      this.toast.error(result.message, "Error", this.toastOpts);
      return;
    }

    this.logger.log("THIS LEAD ", this.lead);

    // If we are adding a contact from the Lead Page, link it
    if (this.lead?.id !== "") {
      const response = await this.leadService.addLeadContactLink({
        leadId: this.lead?.id,
        leadContactId: result.dto.id,
        createdByContactId: this.user.contactId,
        updatedByContactId: this.user.contactId,
        contactRole: dto.contactRole
      });
    }

    this.eventService.LeadCRMEvent.emit({eventType: LeadEventTypeEnum.LeadContactAdded, object: result.dto});
  }

  cancelModal() {
    this.contactModal.hide();
    this.eventService.LeadCRMEvent.emit({eventType: LeadEventTypeEnum.DialogClosed, object: null});
  }
}
