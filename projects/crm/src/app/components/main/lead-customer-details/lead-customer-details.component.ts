import {Component, OnInit, ViewChild} from '@angular/core';
import {ActivatedRoute} from '@angular/router';
import {ToastService} from 'ng-uikit-pro-standard';
import {LeadCustomerDTO, SphLeadResult, SphLeadSearchResult} from '../../../interfaces/index';
import {LeadDTO} from '../../../interfaces/index';
import {ModalDirective} from "ng-uikit-pro-standard";
import {debounceTime, distinctUntilChanged, filter as rxFilter, switchMap, takeUntil} from "rxjs/operators";
import {UntypedFormControl} from "@angular/forms";
import {from, Observable} from "rxjs";
import {compare} from "fast-json-patch";
import {LeadCRMService} from "../../../services/lead-crm.service";
import {URLService} from "../../../services/url.service";
import {EventService} from "../../../services/event.service";
import { LoggerService } from '../../../global/services/index';
import {LeadEventTypeEnum, SphLeadSourceEnum } from '../../../global/enums/index';

@Component({
  selector: 'app-lead-customer-details',
  templateUrl: './lead-customer-details.component.html',
  styleUrls: ['./lead-customer-details.component.scss', '../../lead-crm.scss']
})
export class LeadCustomerDetailsComponent implements OnInit {

  searchControl = new UntypedFormControl();
  searching: boolean;
  suggestedCustomers: SphLeadResult[];

  constructor(private activeRoute: ActivatedRoute,
              private leadService: LeadCRMService,
              private eventService: EventService,
              private toast: ToastService,
              private urlService: URLService,
              private logService: LoggerService) {


    this.searchControl.valueChanges.pipe(
      debounceTime(500),
      rxFilter(x => !!x),
      distinctUntilChanged(),
      switchMap(changedValue => this.search(changedValue as string)
        .pipe(takeUntil(this.searchControl.valueChanges))))
      .subscribe((results: SphLeadResult[]) => {

        this.suggestedCustomers = results;

      });
  }

  logger = this.logService.taggedLogger(this.constructor?.name);

  leadCustomerId: string;
  leadCustomer: LeadCustomerDTO;
  leads: LeadDTO[];
  sphLeadSourceEnum = SphLeadSourceEnum;
  toastOpts: { opacity: 0.98 };

  @ViewChild("parentLeadCustomerModal") parentLeadCustomerModal: ModalDirective;

  ngOnInit(): void {

    this.leadCustomerId = this.activeRoute.snapshot.params.customerId;

    this.logger.log("LEAD CUSTOMERID ", this.leadCustomerId);

    this.leadService.getLeadCustomer(this.leadCustomerId, { component: 'lead-customer-detail'}).then(result => {
      if (result.isValid) {
        this.leadCustomer = result.dto;

        // extract leads to show in basic grid
        this.leads = this.leadCustomer.leads;

        this.logger.log("Loaded lead customer: ", this.leadCustomer);
      } else {
        this.toast.error(result.message, "Error", this.toastOpts);
        this.logger.error(result.message);
      }
    });
  }

  leadDetails(lead: LeadDTO) {
    this.urlService.crmLeadView(lead.id);
  }

  returnToMain() {
    // navigate back to the main crm screen
    this.urlService.crmMainView();
  }

  editCustomer(event: MouseEvent, leadCustomer: LeadCustomerDTO) {

    event.stopPropagation();
    this.eventService.LeadCRMEvent.emit({eventType: LeadEventTypeEnum.ShowCustomerModal, object: leadCustomer});

  }

  addParentModal() {

    this.parentLeadCustomerModal.show();

  }

  search(value: string): Observable<SphLeadResult[]> {

    this.searching = true;
    const searchString = value + ";filter=sourceId," + this.sphLeadSourceEnum.LeadCustomer;

    return from(this.leadService.globalSearch({searchString}).then((x: SphLeadSearchResult) => {
      this.searching = false;

      return x.results;
    }));
  }


  setParentLeadCustomer(id: string) {

    const patch = compare({}, {parentLeadCustomerId: id});

    this.leadService.patchLeadCustomer(this.leadCustomerId, patch);

  }

  unlinkParent() {

    const patch = compare({parentLeadCustomerId: this.leadCustomer.parentLeadCustomerId }, {});
    this.leadService.patchLeadCustomer(this.leadCustomerId, patch);
  }
}
