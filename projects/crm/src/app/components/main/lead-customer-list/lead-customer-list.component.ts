import {Component, Input, OnDestroy, OnInit} from '@angular/core';
import {UntypedFormBuilder, UntypedFormControl, UntypedFormGroup} from '@angular/forms';
import {Subscription} from 'rxjs';
import {LeadEventTypeEnum, SphLeadSourceEnum } from '../../../global/enums/index';
import { LoggerService } from '../../../global/services/index';
import {LeadCustomerDTO} from '../../../interfaces/index';
import {EventService} from "../../../services/event.service";
import {LeadCRMService} from "../../../services/lead-crm.service";
import {URLService} from "../../../services/url.service";

@Component({
  selector: 'app-lead-customers-list',
  templateUrl: './lead-customer-list.component.html',
  styleUrls: ['./lead-customer-list.component.scss']
})
export class LeadCustomerListComponent implements OnInit, OnDestroy {

  loading: boolean;

  constructor(private leadService: LeadCRMService,
              private eventService: EventService,
              private formBuilder: UntypedFormBuilder,
              private urlService: URLService,
              private logService: LoggerService) {
  }

  logger = this.logService.taggedLogger(this.constructor?.name);

  @Input() sphLeadSourceId: number;

  sphLeadSourceEnum = SphLeadSourceEnum;
  customers: LeadCustomerDTO[];
  pageSize = 20;
  page = 1;
  count = 0;
  pageSizes = [
    {value: 10, label: "10"},
    {value: 20, label: "20"},
    {value: 50, label: "50"},
    {value: 100, label: "100"},
  ];

  form: UntypedFormGroup;
  leadEventSub: Subscription;


  async ngOnInit() {
    this.form = this.formBuilder.group({
      leadStatusId: new UntypedFormControl('', [])
    });

    this.leadEventSub = this.eventService.LeadCRMEvent.subscribe((data) => {
      if (data.eventType == LeadEventTypeEnum.LeadCustomerAdded) {
        this.fetchCustomers();
      }
    });

    this.fetchCustomers();
  }

  ngOnDestroy() {
    if (this.leadEventSub) {
      this.leadEventSub.unsubscribe();
    }
  }

  fetchCustomers() {
    // create dto using form values and pagination
    const dto = this.form.value;
    dto.offset = (this.page - 1) * this.pageSize;
    dto.limit = this.pageSize;
    this.loading = true;

    this.leadService.getLeadCustomers(dto).then(result => {
      this.customers = result.results;
      this.count = result.totalItems;

      this.loading = false;
      this.logger.info("Fetched customers: ", result);
    });
  }

  newCustomer() {
    this.eventService.LeadCRMEvent.emit({eventType: LeadEventTypeEnum.ShowCustomerModal, object: null});
  }

  onTableDataChange(page: number) {
    // fetch records using page as offset
    this.page = page;
    this.fetchCustomers();
  }

  handlePageSizeChange(event: any) {
    this.pageSize = event.target.value;
    this.page = 1;
    this.fetchCustomers();
  }

  editCustomer(customer: LeadCustomerDTO) {
    this.eventService.LeadCRMEvent.emit({eventType: LeadEventTypeEnum.ShowCustomerModal, object: customer});
  }

  leadCustomerDetails(leadCustomer: LeadCustomerDTO) {
    this.urlService.crmLeadCustomerView(leadCustomer.id);
  }
}
