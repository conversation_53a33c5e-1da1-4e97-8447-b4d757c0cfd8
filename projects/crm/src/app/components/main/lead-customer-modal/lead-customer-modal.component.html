<div mdbModal #customerModal="mdbModal" class="modal fade"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true"
     [config]="{backdrop: false, ignoreBackdropClick: true}">
  <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <span *ngIf="isEdit">Edit Lead Account</span>
        <span *ngIf="!isEdit">Create Lead Account</span>
      </div>
      <div class="modal-body">
        <form [formGroup]="form" (ngSubmit)="saveCustomer()">

          <div class="md-form">
            <input #name class="form-control float-label" formControlName="name" type="text" mdbInput
                   name="name"
                   autofocus id="name"/>
            <label for="name">Company / Business Name</label>
            <div class="error-message"
                 *ngIf="(f.name.dirty || submitted) && f.name.errors && f.name.errors.required">
              Account Name is required
            </div>
          </div>

          <div class="row">

            <div class="col-md-6">


              <div class="md-form">
                <input class="form-control float-label" formControlName="phone" type="text" mdbInput
                       name="phone"
                       id="phone"/>
                <label for="phone">Phone Number</label>
                <div class="error-message"
                     *ngIf="(f.phone.dirty || submitted) && f.phone.errors && f.phone.errors.required">
                  Phone Number is required
                </div>
              </div>
            </div>

            <div class="col-md-6">

              <div class="md-form">
                <input class="form-control float-label" formControlName="mobile" type="text" mdbInput
                       name="mobile"
                       id="mobile"/>
                <label for="mobile">Mobile Number</label>

              </div>
            </div>
          </div>

          <div class="md-form">
            <input class="form-control float-label" formControlName="email" type="text" mdbInput
                   name="email"
                   id="email"/>
            <label for="email">E-mail</label>

            <div class="error-message"
                 *ngIf="(f.email.dirty || submitted) && f.email.errors && f.email.errors.required">
              Account Email is required
            </div>
          </div>

          <div class="row">

            <div class="col-md-6">
              <div class="md-form">
                <input class="form-control float-label" formControlName="postcode" type="text" mdbInput
                       name="postcode"
                       id="postcode"/>
                <label for="address">Postcode</label>
              </div>

              <div class="md-form">
                <textarea class="form-control float-label" formControlName="address" type="text" mdbInput
                          name="address"
                          id="address"></textarea>
                <label for="address">Address</label>
              </div>
            </div>

            <div class="col-md-6">

              <div class="mb-4 md-select pb-1">

                <mdb-select-2
                  [outline]="true"
                  id="sourceId"
                  [label]="'Lead Source'"
                  formControlName="sourceId"
                  placeholder="Lead Source">
                  <mdb-select-option *ngFor="let source of leadSources"
                                     [value]="source.value">{{ source.label}}</mdb-select-option>
                </mdb-select-2>

                <div class="error-message pl-3"
                     *ngIf="(f.sourceId.dirty || submitted) && f.sourceId.errors && f.sourceId.errors.required">
                  Source is required
                </div>
              </div>

              <div class="md-form">
                <textarea class="form-control float-label" formControlName="note" type="text" mdbInput
                          name="note" id="note"></textarea>
                <label for="note">Note</label>
              </div>
            </div>

          </div>


          <div class="mt-3 text-right">
            <button class="btn btn-primary-outline ml-2" type="button" (click)="cancelModal()">Cancel</button>
            <button class="btn btn-primary ml-2" style="min-width: 80px;" type="submit">
              <span *ngIf="isEdit">Update</span>
              <span *ngIf="!isEdit">Add</span>
            </button>
          </div>

        </form>

      </div>
    </div>
  </div>
</div>
