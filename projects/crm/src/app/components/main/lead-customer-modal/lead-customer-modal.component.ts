import {AfterViewInit, Component, ElementRef, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {ModalDirective} from "ng-uikit-pro-standard";
import {LeadCustomerDTO} from '../../../interfaces/index';
import {Subscription} from 'rxjs';
import {UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators} from '@angular/forms';
import {ToastService} from 'ng-uikit-pro-standard';
import {EventService} from "../../../services/event.service";
import {LeadCRMService} from "../../../services/lead-crm.service";
import {LeadSourceService} from "../../../services/lead-source.service";
import {LoggerService} from '../../../global/services/index';
import { LeadEventTypeEnum } from '../../../global/enums/index';

@Component({
  selector: 'app-lead-customer-modal',
  templateUrl: './lead-customer-modal.component.html',
  styleUrls: ['./lead-customer-modal.component.scss']
})
export class LeadCustomerModalComponent implements OnInit, OnDestroy, AfterViewInit {

  leadSources: { label: string, value: number }[] = [];

  constructor(private eventService: EventService,
              private formBuilder: UntypedFormBuilder,
              private leadService: LeadCRMService,
              private leadSourceService: LeadSourceService,
              private toast: ToastService,
              private logService: LoggerService) {

    this.form = this.formBuilder.group({
      name: new UntypedFormControl('', [Validators.required]),
      address: new UntypedFormControl('', []),
      postcode: new UntypedFormControl('', []),
      email: new UntypedFormControl('', [Validators.required]),
      phone: new UntypedFormControl('', [Validators.required]),
      mobile: new UntypedFormControl(''),
      sourceId: new UntypedFormControl('', [Validators.required]),
      note: new UntypedFormControl('')
    });
  }

  logger = this.logService.taggedLogger(this.constructor?.name);

  @ViewChild('customerModal') customerModal: ModalDirective;

  isEdit = false;
  customer: LeadCustomerDTO;
  crmEventSub: Subscription;

  form: UntypedFormGroup;
  submitted = false;

  toastOpts: { opacity: 0.98 };

  ngOnInit(): void {

    this.crmEventSub = this.eventService.LeadCRMEvent.subscribe((data) => {
      if (data.eventType === LeadEventTypeEnum.ShowCustomerModal) {
        this.isEdit = !!data.object;
        this.customer = data.object;

        if (this.isEdit) {
          this.setFormControlsEdit();
        }

        this.initCustomerModal();
      }
    });

  }

  initCustomerModal() {

    this.customerModal.show();

    this.leadSourceService.getLeadSources({component: 'lead-customer-modal'}).then(res => {
      if (res && Array.isArray(res.results)) {
        this.logger.log("RES ", res);
        res.results.forEach((x) => {
          this.leadSources.push({value: x.id, label: x.sourceName});
        });
      }
    });

  }

  ngAfterViewInit() {

  }

  ngOnDestroy() {
    if (this.crmEventSub) {
      this.crmEventSub.unsubscribe();
    }
  }

  get f() {
    return this.form.controls;
  }

  setFormControlsEdit() {
    this.form.controls.name.setValue(this.customer.name);
    this.form.controls.address.setValue(this.customer.address);
    this.form.controls.postcode.setValue(this.customer.postcode);
    this.form.controls.email.setValue(this.customer.email);
    this.form.controls.phone.setValue(this.customer.phone);
    this.form.controls.mobile.setValue(this.customer.mobile);
  }

  saveCustomer() {
    this.submitted = true;

    if (this.form.valid) {
      const dto = {...this.form.value} as LeadCustomerDTO;

      if (!this.isEdit) {
        this.addCustomer(dto);
      }
    }
  }

  addCustomer(dto) {
    this.leadService.addLeadCustomer(dto).then(result => {
      if (!result.isValid) {
        this.toast.error(result.message, "Error", this.toastOpts);
        return;
      } else {
        // raise event now that customer is saved, so lead dialog can refresh list
        this.eventService.LeadCRMEvent.emit({eventType: LeadEventTypeEnum.LeadCustomerAdded, object: result.dto});
        this.customerModal.hide();
      }
    });
  }

  patchCustomer(dto) {

  }

  cancelModal() {
    this.customerModal.hide();
    this.eventService.LeadCRMEvent.emit({eventType: LeadEventTypeEnum.DialogClosed, object: null});
  }

}
