<app-lead-screen [title]="'Leads'" [searchSourceId]="sphLeadSourceEnum.LeadContactLink"></app-lead-screen>
<div class="widget padding">

  <form [formGroup]="filterForm">

    <div class="d-flex flex-wrap widget-header">

      <div class="flex-shrink-1 text-right pl-1">
        <div class="switch blue-white-switch">
          <span class="switch-label" style="line-height: 29px;">My Leads &nbsp;</span>
          <label>
            <input type="checkbox" formControlName="onlyMe" (ngModelChange)="showOnlyMe()">
            <span class="lever"></span>
          </label>
        </div>
      </div>

      <div class="flex-shrink-1 pl-1 narrowest-select">
        <mdb-select-2
          [outline]="true"
          formControlName="ownerId"
          (ngModelChange)="changeOwnerFilter()"
          style="width: 180px;"
          placeholder="All Staff"
        >
          <mdb-select-option *ngFor="let contact of filterCRMUsers"
                             [value]="contact.value">{{ contact.label}}</mdb-select-option>
        </mdb-select-2>
      </div>


      <!--
      <div class="flex-shrink-1 pl-1 narrowest-select">

        <mdb-select-2
          [outline]="true"
          style="width: 180px;"
          formControlName="leadProductId"
          placeholder="All Products"
          (ngModelChange)="changeProductFilter()">
          <mdb-select-option *ngFor="let xoption of filterLeadProductOptions"
                             [value]="xoption.value">{{ xoption.label}}</mdb-select-option>
        </mdb-select-2>
      </div>
      -->

      <input type="hidden" formControlName="leadProductId">

      <div class="flex-shrink-1 pl-2 narrowest-select">

        <mdb-select-2
          [outline]="true"
          formControlName="leadStatusId"
          style="width: 180px;"
          placeholder="All Statuses"
          (ngModelChange)="changeLeadStatusFilter()">
          <mdb-select-option *ngFor="let xoption of filterLeadStatusOptions"
                             [value]="xoption.value">{{ xoption.label}}</mdb-select-option>
        </mdb-select-2>
      </div>

      <div class="flex-shrink-1 pl-2 narrowest-select">
        <mdb-select-2
          [outline]="true"
          style="width: 180px;"
          formControlName="leadCampaignId"
          placeholder="All Campaigns"
          (ngModelChange)="changeCampaignFilter()">
          <mdb-select-option *ngFor="let xoption of campaignOptions"
                             [value]="xoption.value">{{ xoption.label}}</mdb-select-option>
        </mdb-select-2>
      </div>

      <div class="flex-shrink-1 text-right pl-2">
        <div class="switch blue-white-switch">
          <span class="switch-label" style="line-height: 29px;">Active Only&nbsp;</span>
          <label>
            <input type="checkbox" formControlName="activeOnly" (ngModelChange)="changeActiveOnly()">
            <span class="lever"></span>
          </label>
        </div>
      </div>

      <div class="flex-grow-1"></div>

      <div class="flex-shrink-1 text-right">
        <button class="btn btn-sm btn-secondary" (click)="newLead()" mdbTooltip="Create New Lead"><i
          class="fas fa-plus-circle"></i> Lead
        </button>
      </div>
    </div>
  </form>

  <div *ngIf="loading">

    <div class="text-center pt-5 pb-5">
      <i class="fa fa-spin fa-2x fa-spinner"></i>
      <h1 class="mt-2">Loading</h1>
    </div>
  </div>


  <div *ngIf="!loading">

    <div *ngIf="!leads || leads.length == 0">
      <h1 class="text-center mt-4 mb-4">
        <div>No leads to display. Sad times</div>
      </h1>
    </div>
    <table class="table table-striped table-hover table-compressed table-narrow mt-1 lead-list-table"
           *ngIf="displayFormat != 'CRM' && leads && leads.length > 0">
      <thead>
      <tr>
        <th style="padding: 0 0.35rem; width: 20px;">
          <mdb-checkbox (ngModelChange)="toggleAllCheckboxes($event)" [(ngModel)]="checkAll"></mdb-checkbox>
        </th>
        <th class="sortable" (click)="orderBy('name')">Name</th>
        <th>Vehicle</th>
        <th>E-mail</th>
        <th class="sortable" (click)="orderBy('status')">Status</th>
        <th>Owner</th>
        <th class="shrink-cell sortable" (click)="orderBy('updated')">Updated</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let lead of leads" (click)="leadDetails(lead)">
        <td style="width: 20px;">
          <div style="padding-top: 8px; width: 20px;">
            <mdb-checkbox [(ngModel)]="toggleChecked[lead.id]"></mdb-checkbox>
          </div>
        </td>
        <td class="">
          <div class="table-line-1">
            {{ lead.primaryLeadContactLink?.leadContact.forename }} {{ lead.primaryLeadContactLink?.leadContact.surname }}
          </div>
          <div *ngIf="lead.primaryLeadContactLink?.leadContact.phone" class="table-line-2">
            {{ lead.primaryLeadContactLink?.leadContact.phone }}
          </div>
          <div *ngIf="lead.primaryLeadContactLink?.leadContact.mobile" class="table-line-2">
            {{ lead.primaryLeadContactLink?.leadContact.mobile }}
          </div>
        </td>
        <td>
          <div class="table-line-1">
            {{ lead.leadVehicle?.vehicleLookupInfo?.makeName }}
            {{ lead.leadVehicle?.vehicleLookupInfo?.modelName }}
          </div>
          <div class="table-line-2">
            {{ lead.leadVehicle?.vehicleLookupInfo?.derivName }}
          </div>
          <div class="vrm-container">{{ lead.leadVehicle?.vehicleLookupInfo?.vrm }}</div>
        </td>
        <td>{{ lead.primaryLeadContactLink?.leadContact.email }}</td>
        <td class=""><span
          [style]="{'background': lead.leadStatus.statusRGB || '#888'}"
          class="lead-status status-color status-color-{{ lead.leadStatus.id }}">{{ lead.leadStatus.statusName }}</span>
        </td>
        <td class="">{{ lead.owner?.contactName }}</td>
        <td class="shrink-cell">{{ lead.updated | date: 'dd MMM YYYY' }}</td>
      </tr>
      </tbody>
      <tfoot>
      <tr>
        <td colspan="8">
          <div mdbDropdown class="remarq-dropdown">
          <span mdbDropdownToggle classInside="dropdown-toggle" class="btn btn-primary btn-xs">
            Manage <i class="fa fa-caret-down"></i>
          </span>
            <div class="dropdown-menu">
              <span class="dropdown-item" (click)="reassignLeads()">Reassign Leads</span>
            </div>
          </div>
        </td>
      </tr>
      </tfoot>
    </table>

    <table class="table table-striped table-hover table-compressed table-narrow mt-1 lead-list-table"
           *ngIf="displayFormat == 'CRM' && leads && leads.length > 0">
      <thead>
      <tr>
        <th>Product</th>
        <th>Account</th>
        <th>Contact</th>
        <th>Status</th>
        <th>Updated</th>
        <th>Owner</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let lead of leads | paginate: {
              itemsPerPage: pageSize,
              currentPage: page,
              totalItems: count
            }" (click)="leadDetails(lead)">
        <td class=""><strong>{{ lead.leadProduct.productName }}</strong></td>
        <td class=""><strong>{{ lead.leadCustomer.name }}</strong></td>
        <td class="">
          {{ lead.primaryLeadContactLink?.leadContact?.forename }} {{ lead.primaryLeadContactLink?.leadContact?.surname }}
        </td>
        <td class="">
            <span
              [style]="{'background': lead.leadStatus.statusRGB || '#888'}"
              class="lead-status status-color">{{ lead.leadStatus.statusName }}
            </span>
        </td>
        <td class="">{{ lead.updated | date: 'dd MMM YYYY' }}</td>
        <td class="">{{ lead.owner?.contactName }}</td>
      </tr>
      </tbody>
    </table>

  </div>
</div>
<div class="row">
  <div class="col-6" style="padding-top: 15px; margin-top: 2px; padding-left: 20px;">
    <div class="d-flex">
      <div class="flex-shrink-1 micro-text">
        Items per page
      </div>
      <div class="flex-shrink-1 pl-2 micro-select on-background" style="min-width: 100px;">
        <mdb-select [outline]="true" [options]="pageSizes" [(ngModel)]="pageSize"
                    (change)="handlePageSizeChange($event)"></mdb-select>
      </div>
    </div>
  </div>

  <div class="col-6" style="text-align: end; padding-top: 15px;">
    <pagination-controls
      responsive="true"
      class="paginator"
      previousLabel="Prev"
      nextLabel="Next"
      (pageChange)="onTableDataChange($event)">
    </pagination-controls>
  </div>
</div>
