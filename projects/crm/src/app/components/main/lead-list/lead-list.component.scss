.micro-text {
  line-height: 30px;
  font-size: 0.75rem;
}

.switch-label {
  font-weight: 500;
  font-size: 0.825rem;
  display: inline-block;
  padding-right: 4px;
}

.lead-status {
  display: inline-block;
  border-radius: 11px;
  padding: 2px 7px 4px 7px;
  font-size: 11px;
  line-height: 0.875rem;
  text-align: center;
}

.status-color {
  color: #fff;
}


th.sortable {
  padding-right: 20px;
  position: relative;
  cursor: pointer;
  &:after {
    content:"\f0dc";
    font-family: var(--fontAwesome);
    font-weight: 900;
    position: absolute;
    right: 5px;
    width: 15px;
    text-align: right;
  }
}

.lead-list-table tbody tr td {

  vertical-align: middle;

}

.vrm-container {
  font-weight: bold;
  text-transform: uppercase;
  background-color: var(--vrmBackgroundColour) !important;
  padding: 0px 4px;
  display: inline-block;
  margin-bottom: 4px;
  border-radius: 5px;
  line-height: 1rem;
  border: 1px solid #EBDC8B;
}
