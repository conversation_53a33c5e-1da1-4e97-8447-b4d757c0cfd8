<app-lead-screen
  [title]="'Queue'"
  [searchSourceId]="sphLeadSourceEnum.LeadCustomer"></app-lead-screen>


<div class="widget padding">

  <div class="d-flex">

    <div class="flex-shrink-1 text-right pl-1">
      <div class="switch blue-white-switch">
        <span class="switch-label" style="line-height: 29px;">My Queue &nbsp;</span>
        <label>
          <input type="checkbox" [(ngModel)]="onlyMe" (ngModelChange)="showOnlyMe()">
          <span class="lever"></span>
        </label>
      </div>
    </div>

    <div class="flex-shrink-1">
      <div class="btn-group">
        <label class="btn btn-sm" [(ngModel)]="queueType" (click)="queueTypeChanged()"
               mdbRadio="">All</label>
        <label class="btn btn-sm" [(ngModel)]="queueType" (click)="queueTypeChanged()"
               mdbRadio="overdue">Overdue</label>
        <label class="btn btn-sm" [(ngModel)]="queueType" (click)="queueTypeChanged()"
               mdbRadio="upcoming">Upcoming</label>
      </div>
    </div>
    <div class="flex-grow-1">

    </div>
    <div class="flex-shrink-1">

      <app-crm-format-selector (displayFormatEvent)="displayFormatEvent($event)"></app-crm-format-selector>

    </div>
  </div>

  <div *ngIf="loadingNotes">

    <div class="pt-4 pb-4 text-center">
      <i class="fa fa-spin fa-spinner fa-2x"></i>
      <h1 class="mt-2">Loading</h1>
    </div>

  </div>

  <div *ngIf="!loadingNotes">

    <div *ngIf="leadNotes.length == 0">

      <div class="pt-4 pb-3 text-center"><h1>No items in queue</h1></div>

    </div>

    <table class="table w-100 table-striped table-narrow queue-table" *ngIf="leadNotes.length > 0">
      <thead>
      <tr>
        <th>Owner</th>
        <th>Time</th>
        <th *ngIf="displayFormat=='CRM'">Account</th>
        <th *ngIf="displayFormat=='CarBuying'">VRM</th>

        <th>Contact</th>
        <th>Note</th>
        <th>&nbsp;</th>
        <th>Action</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let leadNote of leadNotes" (click)="viewLead(leadNote.leadId)">
        <td>
          {{ leadNote.lead.owner?.contactName || 'N/A' }}
        </td>
        <td [class]="(leadNote.overdue) ? 'overdue' : 'upcoming'">
          <div class="countdown">{{ leadNote.reminder | timeLeftPipe: "Overdue" | async }}</div>
          <div class="small-time">{{ leadNote.reminder | date: "HH:mm dd/MM/yy" }}</div>
        </td>
        <td *ngIf="displayFormat=='CRM'">{{ leadNote.lead.leadCustomer.name }}</td>
        <td *ngIf="displayFormat=='CarBuying'"><span class="vrm">{{ leadNote.lead?.leadVehicle?.vrm }}</span></td>
        <td>
          <div class="table-line-1">
            {{ leadNote.lead?.primaryLeadContactLink.leadContact?.forename }}
            {{ leadNote.lead?.primaryLeadContactLink.leadContact?.surname }}
          </div>
          <div class="table-line-2">
            {{ leadNote.lead?.primaryLeadContactLink.leadContact?.phone }}
          </div>
        </td>
        <td [mdbTooltip]="leadNote.note">
          <div class="note-summary">{{ leadNote.note }}</div>
        </td>
        <td class=""><span
          [style]="{'background': leadNote?.lead?.leadStatus?.statusRGB || '#888'}"
          class="lead-status status-color status-color-{{ leadNote?.lead?.leadStatus?.id }}">{{ leadNote?.lead?.leadStatus?.statusName }}</span>
        </td>
        <td><i class="fa fa-bell" (click)="showLeadNoteModal($event, leadNote)"></i></td>
      </tr>
      </tbody>
    </table>
  </div>
</div>
