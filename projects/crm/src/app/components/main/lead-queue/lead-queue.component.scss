.micro-text { line-height: 30px; font-size: 0.75rem; }
.switch-label { font-weight: 500; font-size: 0.825rem; display: inline-block; padding-right: 7px; }

.lead-status { display: inline-block; border-radius: 11px; padding: 0 7px; }
.status-color { background-color: #888; color: #fff; }

.note-summary { overflow-y: hidden; white-space: nowrap; overflow-x: hidden; text-overflow: ellipsis; max-width: 300px; }

.countdown { }
.small-time { line-height: 11px; font-size: 11px; opacity: 0.7; }

.overdue { font-weight: 600; color: var(--errorColour); overflow-x: hidden; }

.queue-table tbody td { vertical-align: middle; }

.vrm { font-weight: bold; text-transform: uppercase; background-color: var(--vrmBackgroundColour); line-height: 17px; display: inline-block; padding: 0 4px; border-radius: 3px; }
