import {Component, Input, OnD<PERSON>roy, OnInit} from '@angular/core';
import {LeadNoteDTO} from '../../../interfaces/index';
import {UntypedFormGroup} from '@angular/forms';
import {LeadCRMService} from "../../../services/lead-crm.service";
import {EventService} from "../../../services/event.service";
import {URLService} from "../../../services/url.service";
import {LeadEventTypeEnum, SphLeadSourceEnum} from '../../../global/enums/index';
import {UserService} from '../../../global/services/index';
import {User} from '../../../global/interfaces/index';

@Component({
  selector: 'app-lead-queue',
  templateUrl: './lead-queue.component.html',
  styleUrls: ['./lead-queue.component.scss']
})
export class LeadQueueComponent implements OnInit, OnDestroy {

  private currentUser: User;
  private searchForm: UntypedFormGroup;
  private page = 1;
  private pageSize = 50;
  leadNotes: LeadNoteDTO[];
  queueType = "";
  sphLeadSourceEnum = SphLeadSourceEnum;
  loadingNotes = true;
  displayFormat: string;
  onlyMe: any = true;

  constructor(
    private leadService: LeadCRMService,
    private userService: UserService,
    private eventService: EventService,
    private adminUrlService: URLService) {
  }

  async ngOnInit() {

    await this.userService.loadCurrentUser();
    this.currentUser = this.userService.CurrentUser;
    this.displayFormat = localStorage.getItem("displayFormat");

    this.fetchQueue();
  }

  ngOnDestroy() {
  }

  fetchQueue() {

    this.loadingNotes = true;

    this.leadService.getLeadQueue({
      filters: {
        queueType: this.queueType,
        contactId: (this.onlyMe) ? this.currentUser.contactId : null,
        customerId: (this.onlyMe) ? null : this.currentUser.customerId
      },
      component: "lead-queue"
    }).then(result => {

      this.loadingNotes = false;

      const now = new Date();

      this.leadNotes = result.results;

      this.leadNotes.forEach((note: LeadNoteDTO) => {
        note.overdue = (note.reminderSet && new Date(note.reminder) < now);
      });
    });
  }

  onTableDataChange(page: number) {
    // fetch records using page as offset
    this.page = page;
    this.fetchQueue();
  }

  handlePageSizeChange(event: any) {
    this.pageSize = event.target.value;
    this.page = 1;
    this.fetchQueue();
  }

  showLeadNoteModal(event, note: LeadNoteDTO) {

    event.preventDefault();
    event.stopPropagation();

    this.eventService.LeadCRMEvent.emit({eventType: LeadEventTypeEnum.ShowLeadNoteModal, object: note});
  }

  viewLead(leadId: string) {

    this.adminUrlService.crmLeadView(leadId);

  }

  queueTypeChanged() {

    this.fetchQueue();
  }

  displayFormatEvent($event: string) {

    this.displayFormat = $event;
  }

  showOnlyMe() {

    this.fetchQueue();

  }
}
