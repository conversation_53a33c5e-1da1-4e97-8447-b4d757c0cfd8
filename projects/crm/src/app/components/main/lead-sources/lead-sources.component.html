<app-lead-screen [title]="'Lead Sources'"></app-lead-screen>

<h2 *ngIf="loading">Loading...</h2>

<div *ngIf="!loading">

  <div style="max-height: 100%; overflow-y: auto;" *ngIf="leadSources;else noRecords">

    <div class="widget padding mt-1">
      <div class="d-flex flex-wrap">
        <div class="flex-grow-1">
          &nbsp;
        </div>
        <div class="flex-shrink-1">

          <div class="switch blue-white-switch">
            <span class="switch-label">Show Deleted &nbsp;</span>
            <label>
              <input type="checkbox" [(ngModel)]="showDeleted">
              <span class="lever"></span>
            </label>
          </div>
        </div>
        <div class="flex-shrink-1">
          <button (click)="addLeadSource()" class="btn btn-sm btn-secondary"><i class="fa fa-plus-circle"></i> Lead Source
          </button>
        </div>
      </div>
      <table class="table table-striped table-hover table-compressed table-narrow" *ngIf="!loading; else isLoading">
        <thead>
        <tr>
          <th>ID</th>
          <th>Source</th>
          <th>Added</th>
          <th>Updated</th>
          <th>Status</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let leadSource of showLeadSources()" (click)="leadSourceDetails(leadSource)">
          <td class="shrink-cell">{{ leadSource.id }}</td>
          <td class=""><strong>{{ leadSource.sourceName }}</strong></td>
          <td class="">{{ leadSource.added | date: 'dd MMM YYYY' }}</td>
          <td class="">{{ leadSource.updated | date: 'dd MMM YYYY' }}</td>
          <td class="shrink-cell">{{ statusName[leadSource.statusId] }}</td>
        </tr>
        </tbody>
      </table>

      <ng-template #isLoading>
        <div class="pt-4 pb-4 text-center">
          <i class="fa fa-2x fa-spin fa-spinner"></i>
          <h1 class="mt-2">Loading</h1>
        </div>
      </ng-template>

    </div>
  </div>

  <ng-template #noRecords>No Records</ng-template>

</div>

<!-- modal for new vehicle -->
<div mdbModal #leadSourceModal="mdbModal" class="modal fade" tabindex="-1"
     [config]="{backdrop: false, ignoreBackdropClick: true}"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">

        <div *ngIf="!isEdit">Adding Lead Source</div>
        <div *ngIf="isEdit">Editing Lead Source</div>

      </div>
      <div class="modal-body">
        <form [formGroup]="form" (ngSubmit)="saveSource()">

          <input type="hidden" name="id" formControlName="id">

          <div class="md-form">
            <input mdbInput class="form-control float-label text-capitalize" style="font-weight: 500;"
                   type="text"
                   #sourceName formControlName="sourceName" id="sourceName"/>
            <label for="sourceName">Lead Source Name</label>
          </div>

          <mdb-select [outline]="true"
                      [options]="statusOptions"
                      formControlName="statusId"
                      placeholder="Select Status"></mdb-select>

          <div class="mt-3 text-right">
            <button class="btn btn-secondary ml-2" type="button" (click)="hideModal()">Cancel</button>
            <button class="btn btn-primary ml-2" style="min-width: 80px;" type="submit">
              <span *ngIf="isEdit">Update</span>
              <span *ngIf="!isEdit">Add</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>


