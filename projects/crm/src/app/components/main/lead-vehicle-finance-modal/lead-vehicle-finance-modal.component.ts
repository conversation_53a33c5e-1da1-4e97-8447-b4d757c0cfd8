import {AfterViewInit, Component, ElementRef, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {ModalDirective} from "ng-uikit-pro-standard";
import {LeadCustomerDTO, LeadDTO, LeadVehicleDTO} from '../../../interfaces/index';
import {Subscription} from 'rxjs';
import {UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators} from '@angular/forms';
import {ToastService} from 'ng-uikit-pro-standard';
import {EventService} from "../../../services/event.service";
import {LeadCRMService} from "../../../services/lead-crm.service";
import {LeadSourceService} from "../../../services/lead-source.service";
import {LoggerService} from '../../../global/services/index';
import {FinanceStatusEnum, LeadEventTypeEnum} from '../../../global/enums/index';
import {compare} from "fast-json-patch";
import {LeadVehicleService} from "../../../services/lead-vehicle.service";

@Component({
  selector: 'app-lead-vehicle-finance-modal',
  templateUrl: './lead-vehicle-finance-modal.component.html',
  styleUrls: ['./lead-vehicle-finance-modal.component.scss']
})
export class LeadVehicleFinanceModalComponent implements OnInit, OnDestroy, AfterViewInit {

  leadSources: { label: string, value: number }[] = [];
  private preEdit: any;
  private leadVehicle: LeadVehicleDTO;
  private lead: LeadDTO;
  financeStatusNames: any[] = [];

  constructor(private eventService: EventService,
              private formBuilder: UntypedFormBuilder,
              private leadService: LeadCRMService,
              private leadVehicleService: LeadVehicleService,
              private leadSourceService: LeadSourceService,
              private toast: ToastService,
              private logService: LoggerService) {

    this.form = this.formBuilder.group({
      financeStatus: new UntypedFormControl(''),
      financeHouse: new UntypedFormControl(''),
      financeReference: new UntypedFormControl(''),
      settlementAmount: new UntypedFormControl(''),
      settlementEmail: new UntypedFormControl(''),
      settlementPhone: new UntypedFormControl(''),
      settlementPaymentReference: new UntypedFormControl(''),
      customerPaymentReference: new UntypedFormControl(''),
    });

    this.financeStatusNames = [];

    for (const key in FinanceStatusEnum) {
      if (isNaN(Number(key))) {
        this.financeStatusNames.push({id: FinanceStatusEnum[key].toString(), name: key});
      }
    }

    console.log("FSN ", this.financeStatusNames);
  }

  @ViewChild('vehicleFinanceModal') vehicleFinanceModal: ModalDirective;

  logger = this.logService.taggedLogger(this.constructor?.name);
  isEdit = false;
  customer: LeadCustomerDTO;
  crmEventSub: Subscription;

  form: UntypedFormGroup;
  submitted = false;

  toastOpts: { opacity: 0.98 };

  ngOnInit(): void {

    this.crmEventSub = this.eventService.LeadCRMEvent.subscribe((data) => {
      if (data.eventType === LeadEventTypeEnum.ShowLeadVehicleFinanceModal) {

        this.leadVehicle = data.object.lead.leadVehicle;
        this.lead = data.object.lead;

        this.setFormControlsEdit();
        this.initCustomerModal();
      }
    });

  }

  initCustomerModal() {
    this.vehicleFinanceModal.show();
  }

  ngAfterViewInit() {

  }

  ngOnDestroy() {
    if (this.crmEventSub) {
      this.crmEventSub.unsubscribe();
    }
  }

  get f() {
    return this.form.controls;
  }

  setFormControlsEdit() {

    this.form.patchValue({
      financeStatus: this.leadVehicle?.financeStatus.toString(),
      financeHouse: this.leadVehicle?.financeHouse,
      financeReference: this.leadVehicle?.financeReference,
      settlementAmount: this.leadVehicle?.settlementAmount,
      settlementEmail: this.leadVehicle?.settlementEmail,
      settlementPhone: this.leadVehicle?.settlementPhone,
      settlementPaymentReference: this.leadVehicle?.settlementPaymentReference,
      customerPaymentReference: this.leadVehicle?.customerPaymentReference,
    });

    this.preEdit = Object.assign({}, this.form.value);
  }

  saveFinance() {

    this.submitted = true;

    if (this.form.valid) {

      const patch = compare(this.preEdit, this.form.value);

      this.leadVehicleService.patchLeadVehicle(this.lead?.leadVehicle?.id, patch).then(result => {
        this.eventService.LeadCRMEvent.emit({eventType: LeadEventTypeEnum.LeadVehicleFinanceAdded, object: result.dto});
        this.vehicleFinanceModal.hide();
      });
    }
  }

  cancelModal() {
    this.vehicleFinanceModal.hide();
    this.eventService.LeadCRMEvent.emit({eventType: LeadEventTypeEnum.DialogClosed, object: null});
  }

}
