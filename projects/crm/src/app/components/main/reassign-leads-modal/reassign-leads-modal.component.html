<div mdbModal #reassignLeadsModal="mdbModal" class="modal fade"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true" [config]="{backdrop: 'static'}">
  <div class="modal-dialog modal-md modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">

        <div>
          <button type="button" class="close pull-right" aria-label="Close" (click)="reassignLeadsModal.hide()"><span
            aria-hidden="true">×</span></button>
          Reassign Lead(s)
        </div>
      </div>
      <div class="modal-body">

        <form [formGroup]="reassignForm">

          <div class="d-flex">
            <div class="flex-grow-1 narrow-select">

              <mdb-select-2 formControlName="newOwnerId" [outline]="true"
                            [label]="'Assign To'"
                            placeholder="Select User"
                            class="w-100">

                <mdb-select-option *ngFor="let crmUser of crmUsers" [value]="crmUser.value">
                  <span class="availability available-{{ crmUser.userStatus }}"></span> {{ crmUser.label}}
                </mdb-select-option>
              </mdb-select-2>
            </div>
            <div class="flex-shrink-1">
              <div class="pl-2">
                <span class="btn btn-primary-outline" (click)="reassignLeadsModal.hide()">Cancel</span>
              </div>
            </div>
            <div class="flex-shrink-1">
              <div class="pl-2">
              <span class="btn btn-primary" (click)="confirmReassign()">
                <span *ngIf="reassigning"><i class="fa fa-spin fa-spinner"></i></span>
                <span *ngIf="!reassigning">OK</span>
              </span>
              </div>
            </div>
          </div>
        </form>

      </div>
    </div>
  </div>
</div>
