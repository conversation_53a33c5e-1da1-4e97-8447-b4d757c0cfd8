.date-and-address {

  .md-form {
    margin-bottom: 0.4rem !important;
  }

  input[type=date], input[type=time] {

    padding-top: 0.6rem !important;
    padding-bottom: 0.5rem !important;
  }
}

.availability {
  height: 10px;
  width: 10px;
  margin-right: 5px;
  display: inline-block;
  background-color: #ccc;
  border-radius: 2px;

  &.available-2 {
    background-color: var(--errorColour);
  }
  &.available-1 {
    background-color: var(--successColour);
  }
}

.vrm {

  font-weight: 900;
  text-transform: uppercase;

}

.btn-input {

  padding-top: 12px;
  padding-bottom: 10px;
  font-size: 14px;


}

.vehicle-grid {
  display: grid;
  grid-gap: 5px;
  grid-template-columns: repeat(auto-fit, minmax(200px, auto));

  .md-form {
    margin-bottom: 0px !important;
  }

  .switch {
    line-height: 40px;
  }
}

.switch-label {
  font-weight: 500;
  font-size: 0.825rem;
  display: inline-block;
  padding-right: 7px;
}

h2 {

  margin-right: 10px;
  line-height: 20px;
  padding-bottom: 5px;
  display: inline-block;

  &.tab-selected {

    border-bottom: 2px solid var(--colour20);

  }

}

#pickupAddress, #dropoffAddress {

  min-height: 45px;

}
