import {Component, OnInit, ViewChild} from '@angular/core';
import {ValuationLookupTypeEnum} from "../../../../global/enums";
import {ActivatedRoute} from '@angular/router';
import {ModalDirective} from "ng-uikit-pro-standard";
import {UntypedFormControl, UntypedFormGroup} from '@angular/forms';
import {SelectItemDTO} from "../../../../global/interfaces";
import {LoggerService} from "../../../../global/services";
import {
  IValuationNode,
  ValuationAttributeDTO,
  ValuationNodeDTO, ValuationPillarDTO,
  ValuationProfileDTO,
  ValuationSearchDTO
} from "../../../../global/interfaces";
import {AdminValuationService} from "../../../../global/services";
import {LocaleService, URLService} from '../../../../services';

@Component({
  selector: 'app-valuation-admin',
  templateUrl: './valuation-profile-edit.component.html',
  styleUrls: ['./valuation-profile-edit.component.scss', '../valuation.scss']
})
export class ValuationProfileEditComponent implements OnInit {

  nodeSelected: ValuationAttributeDTO;
  pillarsChosen: number[] = [null, null, null, null, null, null];
  valuationNodes: ValuationNodeDTO[] = [];

  constructor(
    private valuationService: AdminValuationService,
    public adminUrl: URLService,
    private activeRoute: ActivatedRoute,
    public localeService: LocaleService,
    private logService: LoggerService) {
    this.profileId = this.activeRoute.snapshot.params.profileId;
  }

  logger = this.logService.taggedLogger(this.constructor?.name);

  @ViewChild('nodeModal') nodeModal: ModalDirective;
  public LookupTypeEnum = ValuationLookupTypeEnum;

  profileId: string;
  profile: ValuationProfileDTO;
  inputForm: UntypedFormGroup;
  allAttributes: ValuationAttributeDTO[];

  // pillar attributes
  pillars: ValuationAttributeDTO[];

  // pillar to add valuation node to
  selectedPillar: ValuationPillarDTO;

  // node attributes
  nodes: ValuationAttributeDTO[];

  showingSummary = false;

  ngOnInit(): void {
    // load the profile
    this.valuationService.search(<ValuationSearchDTO> {filters: {valuationProfileId: this.profileId}, component: 'profile-edit'})
      .then(res => {
        this.logger.info("Profile: ", res);

        if (res && res.length > 0) {
          this.profile = res[0];
          this.valuationNodes = res[0].valuationNodes;
        }
      });


    // load existing pillar valuation nodes (they contain child nodes as a list)
    // this.loadPillars();
    this.loadAttributes();
  }

  loadAttributes() {

    this.valuationService.getLookupAttributes().then(res => {
      this.logger.info("Valuation lookups: ", res);

      this.allAttributes = res;

      this.pillars = res.filter(x => x.isPillar);
      this.nodes = res.filter(x => !x.isPillar);

      this.allAttributes.forEach(x => {
        x.filteredItems = x.lookupItems;
        x.parentPillar = null;

        if (x.isPillar) {
          if (x.lookupType != ValuationLookupTypeEnum.Age) {
            x.filteredItems = this.sortedSelect(x.filteredItems);
          }
          x.filteredItems.unshift({value: null, label: "All " + ValuationLookupTypeEnum[x.lookupType].toString() + "s"});

          // Toyota/Lexus hack
          if (x.lookupType === ValuationLookupTypeEnum.Make) {
            let tm = x.filteredItems.findIndex(x => x.label == "Toyota");
            if (tm) {
              this.move(x.filteredItems, tm, 1);
            }

            tm = x.filteredItems.findIndex(x => x.label == "Lexus");
            if (tm) {
              this.move(x.filteredItems, tm, 2);
            }
          }
        }

        const isModel = x.lookupType === ValuationLookupTypeEnum.Model;
        const isDeriv = x.lookupType === ValuationLookupTypeEnum.Deriv;

        if (isModel || isDeriv) {

          x.noParentMessage = `Select ${isModel ? 'Make' : 'Model'}`;
          x.parentPillar =
            this.pillars.find(y => y.lookupType === (isModel ? ValuationLookupTypeEnum.Make : ValuationLookupTypeEnum.Model));
        }
      });
    });
  }

  move(array, from, to) {
    if (to === from) {
      return array;
    }

    let target = array[from];
    let increment = to < from ? -1 : 1;

    for (let k = from; k != to; k += increment) {
      array[k] = array[k + increment];
    }
    array[to] = target;
    return array;
  }

  private setLookupItems(lookupType: ValuationLookupTypeEnum, childType: ValuationLookupTypeEnum) {
    const parent = this.getLookup(lookupType);
    if (!parent) {
      return;
    }

    const child = this.allAttributes.find(x => x.lookupType === childType);

    child.filteredItems = child.lookupItems.filter(x => x.parentId === parent.selectedItem);
    child.filteredItems.unshift({value: null, label: "All " + ValuationLookupTypeEnum[childType].toString()});
  }

  private getLookup(lookupType: ValuationLookupTypeEnum) {
    const parent = this.pillars.find(x => x.lookupType === lookupType);
    if (!parent || !parent.selectedItem) {
      return null;
    }

    return parent;
  }

  pillarSelected(pillar: ValuationAttributeDTO, i) {

    this.pillarsChosen[i + 1] = pillar.selectedItem;

    // set the count of valuation nodes (that have values set) for each attributeNode (i.e. Fuel - 3 if 3 of the 5 fuel types have values set)
    this.setNodeValueCounts();

    if (pillar.lookupType === ValuationLookupTypeEnum.Make) {
      // clear model and deriv lookups
      this.clearDropdown(ValuationLookupTypeEnum.Model);
      this.clearDropdown(ValuationLookupTypeEnum.Deriv);

      this.setLookupItems(ValuationLookupTypeEnum.Make, ValuationLookupTypeEnum.Model);
    } else if (pillar.lookupType === ValuationLookupTypeEnum.Model) {
      // clear deriv lookup
      this.clearDropdown(ValuationLookupTypeEnum.Deriv);

      this.setLookupItems(ValuationLookupTypeEnum.Model, ValuationLookupTypeEnum.Deriv);
    }

    if (this.nodeSelected) {

      this.buildForm();

    }
  }

  setNodeValueCounts() {
    this.nodes.forEach(node => {
      const nodes = this.valuationNodes.filter(x => x.lookupType === node.lookupType
        && x.pillar1 == this.pillarsChosen[1]
        && x.pillar2 == this.pillarsChosen[2]
        && x.pillar3 == this.pillarsChosen[3]
        && x.pillar3 == this.pillarsChosen[4]
        && x.pillar3 == this.pillarsChosen[5]
      );
      node.valueCount = nodes?.length;
    });
  }


  clearDropdown(lookupType: ValuationLookupTypeEnum) {
    const attribute = this.allAttributes.find(x => x.lookupType === lookupType);
    if (attribute) {
      attribute.selectedItem = null;
    }
  }

  isOptionDisabled(pillar: ValuationAttributeDTO): boolean {
    return pillar.parentPillar && !pillar.parentPillar.selectedItem;
  }

  isAddPillarDisabled(pillar: ValuationAttributeDTO) {

    if (!pillar.selectedItem) {
      return false;
    }

    // no more than one of the same pillar lookup per type (i.e. 2 x Toyota in Make)
    return this.profile.valuationPillars.find(x => x.lookupType === pillar.lookupType && x.lookupId === pillar.selectedItem) != null;
  }

  addPillarNode(pillar: ValuationAttributeDTO) {

    // do nothing when pillar is disabled
    if (this.isAddPillarDisabled(pillar)) {
      return;
    }

    const lookupId = pillar.selectedItem;
    const lookupName = pillar.lookupItems.find(x => x.value === lookupId)?.label;

    if (!lookupName) {
      this.logger.warn("Couldn't find lookup for pillar: ", pillar);
      return;
    }

    // create the node in the database and allow user to set values via form
    const dto = {
      valuationProfileId: this.profileId,
      lookupType: pillar.lookupType,
      lookupId,
      lookupName,
      multiplier: 0,
      statusId: 1
    } as ValuationPillarDTO;
    this.valuationService.addPillarNode(dto).then(res => {
      if (res.isValid) {
        this.profile.valuationPillars.push(res.dto);
      }
    });
  }

  filterPillars(valuationPillars: ValuationNodeDTO[], lookupType: ValuationLookupTypeEnum): ValuationNodeDTO[] {
    return valuationPillars.filter(x => x.lookupType === lookupType);
  }

  nodeValuesChanged(lookupType, lookup, event) {

    const stub = "-" + lookupType + "-" + lookup.value;
    let multiValue = this.f['multi' + stub].value;
    let addValue = this.f['addvalue' + stub].value;
    const callForQuote = this.f['call' + stub].value;
    const noQuote = this.f['noquote' + stub].value;

    if (event == 'multi' && multiValue != "0" && multiValue != "") {
      addValue = 0;
      this.inputForm.patchValue({['addvalue' + stub]: ''});
    }

    if (event == 'addValue' && addValue != "0" && addValue != "") {
      multiValue = 0;
      this.inputForm.patchValue({['multi' + stub]: ''});
    }

    const lookupName = lookup.label;

    const data = {
      multiplier: multiValue,
      addValue,
      callForQuote,
      noQuote,
      lookupName
    };

    // Update the local value
    let a = this.getNode(lookupType, lookup);

    if (a.length > 0) {
      a[0].multiplier = 1 + (multiValue / 100);
      a[0].addValue = addValue;
      a[0].noQuote = noQuote;
      a[0].callForQuote = callForQuote;
      a[0].lookupName = lookupName;
    }

    this.valuationService.setNodeValue(
      this.profile.id,
      this.pillarsChosen[1],
      this.pillarsChosen[2],
      this.pillarsChosen[3],
      this.pillarsChosen[4],
      this.pillarsChosen[5],
      lookupType,
      lookup.value,
      data).then((res) => {

      // if this is a new node, add it to the profile and reset counts
      const existing = this.getNode(res.dto.lookupType, {value: res.dto.lookupId});
      if (!existing || existing.length == 0) {

        // the returned dto does not contain makeName etc. and since they are selected pillars at this point
        // we can add them manually to the dto so they show correctly in the summary
        res.dto.makeName = this.getPillarLabel(ValuationLookupTypeEnum.Make);
        res.dto.modelName = this.getPillarLabel(ValuationLookupTypeEnum.Model);
        res.dto.ageName = this.getPillarLabel(ValuationLookupTypeEnum.Age);
        res.dto.derivName = this.getPillarLabel(ValuationLookupTypeEnum.Deriv);

        this.logger.warn("No node found for res: ", res.dto);

        this.profile.valuationNodes.push(res.dto);
        this.setNodeValueCounts();
      } else {
        this.logger.info("Node found not refreshing counts for existing: ", existing);
      }
    });
  }

  getPillarLabel(lookupType: ValuationLookupTypeEnum) {
    let lookup = this.pillars.find(x => x.lookupType === lookupType);
    return lookup.lookupItems.find(x => x.value == lookup?.selectedItem)?.label;
  }

  getNode(lookupType, lookupItem) {

    return this.valuationNodes.filter(y =>
      y.pillar1 == this.pillarsChosen[1] &&
      y.pillar2 == this.pillarsChosen[2] &&
      y.pillar3 == this.pillarsChosen[3] &&
      y.pillar4 == this.pillarsChosen[4] &&
      y.pillar5 == this.pillarsChosen[5] &&
      y.lookupType == this.nodeSelected.lookupType &&
      y.lookupId == lookupItem.value.toString()
    );
  }

  selectNode(node: ValuationAttributeDTO) {

    this.nodeSelected = node;

    this.buildForm();
  }

  buildForm() {

    this.inputForm = new UntypedFormGroup({});

    this.nodeSelected.lookupItems.forEach((lookupItem) => {

      const stub = this.nodeSelected.lookupType + "-" + lookupItem.value;

      const a = this.getNode(this.nodeSelected.lookupType, lookupItem);

      let defaults: ValuationNodeDTO = {};

      if (a.length > 0) {
        defaults = a[0];
      }

      // add a count to the valuation node attribute
      //this.nodeSelected.lookupType = 20;

      this.inputForm.addControl('multi-' + stub, new UntypedFormControl(defaults.multiplier == 1 ? '' : ((defaults.multiplier * 100) - 100)));
      this.inputForm.addControl('addvalue-' + stub, new UntypedFormControl(defaults.addValue == 0 ? '' : defaults.addValue));
      this.inputForm.addControl('noquote-' + stub, new UntypedFormControl(defaults.noQuote));
      this.inputForm.addControl('call-' + stub, new UntypedFormControl(defaults.callForQuote));
    });

  }

  sortedLookupItems(nodeSelected: ValuationAttributeDTO) {

    return nodeSelected?.lookupItems.sort((a, b) => {
      return a.label.localeCompare(b.label);
    });
  }


  get f() {

    if (this.inputForm) {
      return this.inputForm?.controls;
    }
    return null;

  }

  setNoQuote(lookupType: ValuationLookupTypeEnum, lookup, event) {

    const stub = "-" + lookupType + "-" + lookup.value;
    const callVar = "call" + stub;
    const noQuoteVar = "noquote" + stub;
    const multiVar = "multi" + stub;
    const addValueVar = "addvalue" + stub;

    if (this.f[noQuoteVar].value === true) {

      this.inputForm.patchValue({
          [callVar]: false,
        }
      );
    }

    this.nodeValuesChanged(lookupType, lookup, 'noQuote');
  }

  setCall(lookupType: ValuationLookupTypeEnum, value, event) {

    const stub = "-" + lookupType + "-" + value;
    const callVar = "call" + stub;
    const noQuoteVar = "noquote" + stub;
    const multiVar = "multi" + stub;
    const addValueVar = "addvalue" + stub;

    if (this.f[callVar].value === true) {

      this.inputForm.patchValue({
          [noQuoteVar]: false,
        }
      );
    }

    this.nodeValuesChanged(lookupType, value, 'callForQuote');
  }

  sortedNodes(nodes: ValuationAttributeDTO[]): ValuationAttributeDTO[] {

    //return nodes.sort((a, b) => a.name.localeCompare(b.name));
    return nodes.sort((a, b) => a.order - b.order);

  }

  sortedSelect(nodes: SelectItemDTO[]) {

    return nodes.sort((a, b) => a.label.localeCompare(b.label));

  }

  showSummary() {
    if (!this.showingSummary) {

    }

    this.showingSummary = !this.showingSummary;
  }

  nodeSummaryValuesChanged(event: IValuationNode, b: boolean) {

  }

  nodeChanged(node: IValuationNode) {
    // find the lookup item according to the node that's been edited
    const attrNode = this.allAttributes.find(x => x.lookupType === node.lookupType);
    const item = attrNode?.lookupItems?.find(x => x.value == node.lookupId);

    if (!attrNode || !item) {
      this.logger.warn("No item found: ", node);
    }

    const data = {
      lookupName: item.label,
      addValue: node.addValue,
      multiplier: node.multiplier
    } as IValuationNode;


    this.valuationService.setNodeValue(
      this.profile.id,
      node.pillar1,
      node.pillar2,
      node.pillar3,
      node.pillar4,
      node.pillar5,
      node.lookupType,
      node.lookupId,
      data).then((res) => {

      if (!res.isValid) {
        this.logger.warn("result invalid when saving node: ", res);
      } else {
        this.setNodeValueCounts();
      }
    });
  }

  deleteNode(node: IValuationNode) {
    this.valuationService.deleteValuationNode(node.id).then(res => {
      if (res) {
        this.profile.valuationNodes = this.profile.valuationNodes.filter(x => x.id != node.id);
      }
    });
  }
}
