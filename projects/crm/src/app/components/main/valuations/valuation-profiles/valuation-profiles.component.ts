import {Component, OnInit, ViewChild} from '@angular/core';
import {UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators} from '@angular/forms';
import {ModalDirective} from "ng-uikit-pro-standard";
import {compare} from 'fast-json-patch';
import {StatusEnum} from "../../../../global/enums";
import {HelpersService} from "../../../../global/services";
import {AdminValuationService} from "../../../../global/services";
import {ValuationProfileDTO} from "../../../../global/interfaces";
import {URLService} from '../../../../services/url.service';

@Component({
  selector: 'app-valuation-profiles',
  templateUrl: './valuation-profiles.component.html',
  styleUrls: ['./valuation-profiles.component.scss', '../valuation.scss']
})
export class ValuationProfilesComponent implements OnInit {

  constructor(
    private valuationService: AdminValuationService,
    private formBuilder: UntypedFormBuilder,
    private urlService: URLService,
    private helperService: HelpersService) {
  }

  @ViewChild('profileModal') profileModal: ModalDirective;

  public StatusEnum = StatusEnum;
  profiles: ValuationProfileDTO[];

  form: UntypedFormGroup;
  submitted = false;
  defaultProfileId = null;

  editing = false;
  editProfileId = null;

  ngOnInit(): void {
    this.form = this.formBuilder.group({
      name: new UntypedFormControl('', {validators: Validators.required})
    });

    this.loadProfiles();
  }

  loadProfiles() {
    this.valuationService.search({}).then(res => {
      this.profiles = res;

      const defaultProfile = res.find(x => x.isDefault === true);

      if (defaultProfile != null) {
        this.defaultProfileId = defaultProfile.id;
      }
    });
  }

  get f() {
    return this.form.controls;
  }

  addProfile() {
    this.editing = false;

    // show dialog to name new profile
    this.profileModal.show();
  }

  saveProfile() {

    this.submitted = true;

    if (!this.f.name.errors) {
      if (this.editing) {
        const patch = compare({}, {name: this.f.name.value});

        this.valuationService.patchProfile(this.editProfileId, patch).then(res => {
          if (res.isValid) {
            this.loadProfiles();
          }
          this.profileModal.hide();
        });
      } else {
        this.valuationService.addProfile({name: this.form.value.name}).then(res => {
          if (res.isValid) {
            this.profiles.push(res.dto);
          }

          this.profileModal.hide();
        }).catch(err => {
          this.profileModal.hide();
        });
      }
    }
  }

  viewProfile(profile: ValuationProfileDTO) {
    this.urlService.adminValuationsProfile(profile.id);
  }

  confirmDeleteProfile(profile: ValuationProfileDTO) {
    const msg = `Are you sure you want to remove ${profile.name} from the profiles list?`;

    this.helperService.confirmationDialog("Confirm Remove Valuation Profile", msg, 'Yes', 'No')
      .subscribe(result => {
        if (result) {
          // set the profile to deleted so it does not show up on the list
          this.deleteProfile(profile);
        }
      });
  }

  deleteProfile(profile: ValuationProfileDTO, previousDTO: ValuationProfileDTO = {}) {

    const patch = compare(previousDTO, {statusId: StatusEnum.Deleted});

    this.valuationService.patchProfile(profile.id, patch).then(res => {
      if (res.isValid) {
        this.loadProfiles();
      }
    });
  }

  makePrimary(id: string) {

    this.valuationService.makeProfilePrimary(id).then(() => {
      this.defaultProfileId = id;
    });
  }

  editProfileName(profile: ValuationProfileDTO) {
    this.editing = true;
    this.editProfileId = profile.id;
    this.f["name"].setValue(profile.name);
    this.profileModal.show();
  }
}
