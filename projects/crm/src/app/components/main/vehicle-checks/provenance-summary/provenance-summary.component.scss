.pair-label {
  font-size: 0.6rem;
  text-align: center;
}

.pair-value {
  font-size: 1.4rem;
  line-height: 1rem;
  text-align: center;
}

.provenance-status {
  font-size: 14px;
  font-weight: bold;
  line-height: 17px;
  width: 100%;
  display: inline-block;
  border-radius: 4px;
}

.mw-80px { max-width: 80px; }

.notOk {
  background-color: var(--errorColour);
  color: #fff;
}

.notOk-finance {
  background-color: var(--warningColour);
  color: #fff;
}

.ok {
  background-color: var(--successColour);
  color: #fff;
}

.flex-basis-100 {
  flex-basis: 100px;
}
