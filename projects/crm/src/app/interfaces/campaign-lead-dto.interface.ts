import {CampaignDTO} from './campaign-dto.interface';
import {LeadDTO} from './lead-dto.interface';
import {CampaignOutcomeDTO} from './campaign-outcome-dto.interface';
import {BaseDTO} from "../global/interfaces";

export interface CampaignLeadDTO extends BaseDTO {
  campaignId: string;
  campaign: CampaignDTO;

  leadId: string;
  lead: LeadDTO;

  campaignOutcomeId?: number;
  campaignOutcome: CampaignOutcomeDTO;
}
