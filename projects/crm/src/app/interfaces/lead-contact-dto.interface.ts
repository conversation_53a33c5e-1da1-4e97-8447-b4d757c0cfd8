import {LeadBaseModelDTO} from './lead-base-model-dto.interface';
import {LeadContactLinkDTO} from './lead-contact-link-dto.interface';
import {BaseSearchDTO, ContactDTO } from "../global/interfaces";

export interface LeadContactDTO extends LeadBaseModelDTO {
  idString?: string;
  forename?: string;
  surname?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  leadContactLinks?: LeadContactLinkDTO[];
  contactId?: string; // internal contact (not CRM contact)
  contact?: ContactDTO;
  role?: string; // This will end up on the LeadContactLink property, not LeadContact
}

export interface LeadContactSearchDTO extends BaseSearchDTO
{
  filters?: {
    forename?: string;
    surname?: string;
    email?: string;
    phone?: string;
  };
  useSphinx?: boolean;
}
