import {LeadProductDTO} from './lead-product-dto.interface';
import {LeadCustomerDTO} from './lead-customer-dto.interface';
import {LeadBaseModelDTO} from './lead-base-model-dto.interface';
import {LeadNoteDTO} from './lead-note-dto.interface';
import {LeadContactLinkDTO} from './lead-contact-link-dto.interface';
import {LeadStatusDTO} from "./lead-status-dto.interface";
import {LeadSourceDTO} from "./lead-source-dto.interface";
import {LeadVehicleDTO} from "./lead-vehicle-dto.interface";
import {BaseSearchDTO, ContactDTO, MovementDTO} from '../global/interfaces';

export interface LeadDTO extends LeadBaseModelDTO {
  leadProductId?: number;
  leadProduct?: LeadProductDTO;

  leadCustomerId?: string;
  leadCustomer?: LeadCustomerDTO;

  leadStatusId?: number;
  leadStatus?: LeadStatusDTO;

  ownerId?: string;
  owner?: ContactDTO;

  primaryLeadContactLinkId?: string;
  primaryLeadContactLink?: LeadContactLinkDTO;

  leadNotes?: LeadNoteDTO[];
  leadContactLinks?: LeadContactLinkDTO[];

  leadSourceId?: number;
  leadSource?: LeadSourceDTO;

  leadVehicle?: LeadVehicleDTO;

  movements?: MovementDTO[];
}

export interface SphLeadSearchResult {
  totalItems: number;
  results: SphLeadResult[];
}

export interface SphLeadResult {
  id: number;
  idString: string;
  sourceId?: number;
  recordStatusId?: number;
  added?: number;
  fullName?: string;
  customerName?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  postcode?: string;
  address?: string;
}

export interface LeadSearchDTO extends BaseSearchDTO {
  filters?: {
    leadCustomerId?: string;
    leadProductId?: number;
    leadCampaignId?: string;
    id?: string;
    leadStatusId?: number;
    statusId?: number;
    ownerId?: string;
  };
}

export interface CRMLeadsParams {
  onlyMe?: boolean;
  leadProductId?: number;
  leadStatusId?: number;
  ownerId?: string;
  leadCampaignId?: string;
  activeOnly?: boolean;
}

