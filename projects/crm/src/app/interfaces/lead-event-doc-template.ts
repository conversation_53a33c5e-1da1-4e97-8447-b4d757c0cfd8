import {LeadDTO} from "./lead-dto.interface";
import {BaseIntDTO, BaseSearchDTO} from "../global/interfaces";
import {LeadMediaDTO} from "./lead-media-dto.interface";
import {DocumentDTO} from "../global/interfaces";

export interface LeadEventDocTemplateDTO extends BaseIntDTO {

  leadId: string;
  lead: LeadDTO;

  documentId: string;
  document: DocumentDTO;

  leadMediaId: string;
  leadMedia: LeadMediaDTO;

  latestVersionNumber: number;
}

export interface LeadEventDocTemplateSearchDTO extends BaseSearchDTO {

}
