import {FinanceStatusEnum, ServiceHistoryTypeEnum} from "../global/enums";
import {
  BaseDTO, BodyTypeDTO,
  DerivDTO, FuelTypeDTO,
  MakeDTO,
  ModelDTO, PlateDTO, TransmissionTypeDTO, UInspectDTO,
  ValuationQuoteDTO,
  VehicleColourDTO,
  VehicleDTO,
  VehicleLookupInfoDTO, VehicleTypeDTO
} from "../global/interfaces";
import {LeadDTO} from "./lead-dto.interface";

export interface LeadVehicleDTO extends BaseDTO {
  vrm: string;
  financeStatus: FinanceStatusEnum; // TODO: FinanceStatusEnum;
  financeHouse: string;
  financeReference: string;
  settlementAmount?: number;
  settlementEmail?: string;
  settlementPhone?: string;
  settlementPaymentReference?: string;
  customerPaymentReference?: string;
  customerWants?: number;
  privateHireClear: boolean;
  drivingSchoolClear: boolean;
  transportCosts?: number;
  preferredCollectionDate: string;
  lead: LeadDTO;
  leadId: string;
  currentValuationQuoteId: string;
  currentValuationQuote: ValuationQuoteDTO;
  vehicleId: string;
  vehicle: VehicleDTO;
  vehicleLookupInfoId: string;
  vehicleLookupInfo: VehicleLookupInfoDTO;
  odometer?: number;
  makeId?: number;
  modelId?: number;
  derivId?: number;
  fuelTypeId?: number;
  bodyTypeId?: number;
  transmissionTypeId?: number;
  plateId?: number;
  vatStatusId?: number;
  v5StatusId?: number;
  vehicleColourId?: number;
  vehicleTypeId?: number;
  serviceHistoryType: ServiceHistoryTypeEnum;
  noOfKeys?: number;
  weight?: string;
  engineCC?: number;
  owners?: number;
  runner?: boolean;
  grade?: number;
  lastService: string;
  make: MakeDTO;
  model: ModelDTO;
  deriv: DerivDTO;
  plate: PlateDTO;
  bodyType: BodyTypeDTO;
  fuelType: FuelTypeDTO;
  transmissionType: TransmissionTypeDTO;
  vehicleType: VehicleTypeDTO;
  vehicleColour: VehicleColourDTO;
  externalAppraisalId?: string;
  externalAppraisal: UInspectDTO;
}
