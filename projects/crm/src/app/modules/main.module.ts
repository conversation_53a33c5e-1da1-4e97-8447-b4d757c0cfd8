import {NgModule} from '@angular/core';
import {CommonModule, DatePipe} from '@angular/common';

import {RouterModule} from "@angular/router";
import {IconsModule, InputsModule, MDBBootstrapModulesPro} from "ng-uikit-pro-standard";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {ImageCropperComponent} from "ngx-image-cropper";
import {DndModule} from "ngx-drag-drop";
import {NgsgModule} from "ng-sortgrid";
import {NgxPaginationModule} from "ngx-pagination";
import {NgxGoogleAnalyticsModule, NgxGoogleAnalyticsRouterModule} from "ngx-google-analytics";
import {HTTP_INTERCEPTORS, HttpClientJsonpModule} from "@angular/common/http";
import {AmplifyAuthenticatorModule} from "@aws-amplify/ui-angular";
import {
  AdminContactService,
  ApiService,
  CognitoService,
  ContactService,
  CustomerService,
  HandlerService,
  MovementAddressService,
  UserService,
  ValuationQuoteService,
  VehicleLookupService,
  ExternalEventQueueService,
  ImageService,
  UInspectService,
  UInspectAnswerService,
  UInspectFormatService,
  UInspectMediaService,
  UInspectQuestionService, UInspectQuestionOptionService, UInspectSectionService,
  UInspectCommonService
} from "../global/services";
import {} from "../global/interfaces";
import {} from "../global/enums";
import {HeaderInterceptor} from "../global/interceptors";
import {EventService} from "../services/event.service";
import {DataService} from "../services/data.service";
import {MainComponent} from "../components/main/main.component";
import {LeadStatusesComponent} from "../components/main/lead-statuses/lead-statuses.component";
import {LeadSourcesComponent} from "../components/main/lead-sources/lead-sources.component";
import {CRMDashboardComponent} from "../components/main/crm-dashboard/crm-dashboard.component";
import {CampaignComponent} from "../components/main/campaigns/campaign.component";
import {CampaignDetailsComponent} from "../components/main/campaign-details/campaign-details.component";
import {CampaignOutcomesComponent} from "../components/main/campaign-outcomes/campaign-outcomes.component";
import {LeadQueueComponent} from "../components/main/lead-queue/lead-queue.component";
import {LeadComponent} from "../components/main/lead-common/lead.component";
import {LeadProductsComponent} from "../components/main/lead-products/lead-products.component";
import {LeadListComponent} from "../components/main/lead-list/lead-list.component";
import {LeadDetailsComponent} from "../components/main/lead-details/lead-details.component";
import {LeadContactModalComponent} from "../components/main/lead-contact-modal/lead-contact-modal.component";
import {LeadCustomerModalComponent} from "../components/main/lead-customer-modal/lead-customer-modal.component";
import {ColorPickerModule} from "ngx-color-picker";
import {LeadNoteModalComponent} from "../components/main/lead-note-modal/lead-note-modal.component";
import {CRMFormatSelectorComponent} from "../components/main/crm-format-selector/crm-format-selector.component";
import {
  ManualValuationModalComponent
} from "../components/main/valuations/manual-valuation-modal/manual-valuation-modal.component";
import {BookMovementComponent} from "../components/movements/book-movement-modal/book-movement.component";
import {ReassignLeadsModalComponent} from "../components/main/reassign-leads-modal/reassign-leads-modal.component";
import {LeadCustomerListComponent} from "../components/main/lead-customer-list/lead-customer-list.component";
import {LeadCustomerDetailsComponent} from "../components/main/lead-customer-details/lead-customer-details.component";
import {LeadContactListComponent} from "../components/main/lead-contact-list/lead-contact-list.component";
import {LeadContactDetailsComponent} from "../components/main/lead-contact-details/lead-contact-details.component";
import {CRMUsersComponent} from "../components/main/crm-users/crm-users.component";
import {
  AppSideMenuComponent,
  LoadingSpinnerComponent, DragdropfilesComponent
} from '../global/components';
import {
  LeadCustomerAddressComponent
} from "../components/main/lead-customer-address-modal/lead-customer-address.component";
import {
  LeadAppraisalDetailsModalComponent
} from "../components/main/lead-appraisal-details-modal/lead-appraisal-details-modal.component";
import {
  LeadAppraisalDetailsComponent
} from "../components/main/lead-appraisal-details/lead-appraisal-details.component";
import {
  LeadVehicleFinanceModalComponent
} from "../components/main/lead-vehicle-finance-modal/lead-vehicle-finance-modal.component";
import {LeadVehicleService} from "../services/index";
import {
  CamelCasePipe,
  DatexPipe,
  DecodeHtmlEntitiesPipe,
  EngineCCPipe,
  MileagePipe,
  PrettyJsonPipePipe,
  TimeLeftPipe
} from "../global/pipes";
import {
  ProvenanceSummaryComponent
} from "../components/main/vehicle-checks/provenance-summary/provenance-summary.component";
import {
  ValuationSummaryComponent
} from "../components/main/vehicle-checks/valuation-summary/valuation-summary.component";
import {ValuationTestComponent} from "../components/main/valuations/valuation/valuation-test.component";
import {
  ValuationNodeCompactComponent
} from "../components/main/valuations/valuation-node-compact/valuation-node-compact.component";
import {
  ValuationProfileEditComponent
} from "../components/main/valuations/valuation-profile-edit/valuation-profile-edit.component";
import {
  ValuationProfilesComponent
} from "../components/main/valuations/valuation-profiles/valuation-profiles.component";
import {
  ValuationQuoteHistoryComponent
} from "../components/main/valuations/valuation-quote-history/valuation-quote-history.component";
import {ValuationQuotesComponent} from "../components/main/valuations/valuation-quotes/valuation-quotes.component";
import {
  ValuationResultViewComponent
} from "../components/main/valuations/valuation-result-view/valuation-result-view.component";
import {ValueDevComponent} from "../components/main/valuations/value-dev/value-dev.component";
import {ValuationNodeComponent} from "../components/main/valuations/valuation-node/valuation-node.component";
import {LeadAppraisalsComponent} from "../components/main/lead-appraisals/lead-appraisals.component";
import {MainRoutingModule} from "./main-routing.module";
import {QRCodeModule} from "angularx-qrcode";
import {
  CampaignOutcomeService,
  CampaignService, CRMUserService,
  LeadCRMService,
  LeadMediaService,
  LeadDocumentCategoryService,
  LeadProductService,
  LeadSourceService,
  LeadStatusService,
  LeadDocumentService
} from "../services/index";
import {LeadDocumentsComponent} from "../components/main/lead-details/lead-documents/lead-documents.component";
import {
  LeadDocumentCategoriesComponent
} from "../components/main/lead-document-categories/lead-document-categories.component";
import {CustomCurrencyPipe} from "../pipes/custom-currency-pipe";

@NgModule({
  declarations: [
    MainComponent,
    AppSideMenuComponent,
    LeadStatusesComponent,
    LeadSourcesComponent,
    LeadQueueComponent,
    LeadProductsComponent,
    LeadListComponent,
    LeadDocumentCategoriesComponent,
    LeadDetailsComponent,
    LeadNoteModalComponent,
    LeadContactModalComponent,
    LeadContactListComponent,
    LeadCustomerModalComponent,
    LeadCustomerAddressComponent,
    LeadVehicleFinanceModalComponent,
    LeadContactDetailsComponent,
    LeadCustomerDetailsComponent,
    LeadCustomerListComponent,
    LoadingSpinnerComponent,
    ManualValuationModalComponent,
    LeadComponent,
    CRMDashboardComponent,
    CampaignComponent,
    CRMFormatSelectorComponent,
    CRMUsersComponent,
    CampaignDetailsComponent,
    CampaignOutcomesComponent,
    BookMovementComponent,
    ReassignLeadsModalComponent,
    LeadAppraisalsComponent,
    ValuationTestComponent,
    ValuationNodeComponent,
    ValuationNodeCompactComponent,
    ValuationProfileEditComponent,
    ValuationProfilesComponent,
    ValuationQuoteHistoryComponent,
    ValuationQuotesComponent,
    ValuationResultViewComponent,
    ValueDevComponent,
    MileagePipe,
    DatexPipe,
    EngineCCPipe,
    TimeLeftPipe,
    CamelCasePipe,
    PrettyJsonPipePipe,
    CustomCurrencyPipe,
    LeadAppraisalDetailsModalComponent,
    LeadAppraisalDetailsComponent,
    ProvenanceSummaryComponent,
    ValuationSummaryComponent,
    LeadDocumentsComponent,
    DragdropfilesComponent
  ],
  imports: [
    RouterModule,
    CommonModule,
    MainRoutingModule,
    HttpClientJsonpModule,
    IconsModule,
    InputsModule,
    FormsModule, ReactiveFormsModule,
    MDBBootstrapModulesPro,
    ImageCropperComponent,
    ColorPickerModule,
    NgxGoogleAnalyticsModule.forRoot('G-M4J2XR9P0C'),
    NgxGoogleAnalyticsRouterModule,
    DndModule, NgsgModule, NgxPaginationModule, AmplifyAuthenticatorModule, QRCodeModule,
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HeaderInterceptor,
      multi: true
    },
    ApiService,
    UserService,
    CognitoService,
    DataService,
    EventService,
    ContactService,
    CustomerService,
    DatePipe,
    EngineCCPipe,
    AdminContactService, // TODO: Move the below to lazy loading
    UserService,
    LeadProductService,
    CampaignService,
    CampaignOutcomeService,
    LeadCRMService,
    LeadMediaService,
    LeadDocumentCategoryService,
    LeadVehicleService,
    LeadStatusService,
    LeadSourceService,
    HandlerService,
    ValuationQuoteService,
    MovementAddressService,
    CRMUserService,
    VehicleLookupService,
    ExternalEventQueueService,
    ImageService,
    UInspectService,
    UInspectAnswerService,
    UInspectFormatService,
    UInspectMediaService,
    UInspectQuestionService,
    UInspectQuestionOptionService,
    UInspectSectionService,
    UInspectCommonService,
    LeadDocumentService
  ],
  exports: [
    CommonModule,
    RouterModule,
    DatePipe,
    DatexPipe,
    TimeLeftPipe,
    CamelCasePipe,
    CustomCurrencyPipe,
    EngineCCPipe,
    BookMovementComponent,
    DecodeHtmlEntitiesPipe,
  ]
})
export class MainModule {
}
