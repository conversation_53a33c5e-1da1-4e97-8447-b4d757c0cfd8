import {EventEmitter, Injectable} from '@angular/core';
import {InMailFolderEnum, LeadEventTypeEnum, ValuationEventTypeEnum} from "../global/enums";

@Injectable()

export class EventService {

  public LeadCRMEvent: EventEmitter<{ eventType: LeadEventTypeEnum, object?: any }> =
    new EventEmitter<{ eventType: LeadEventTypeEnum; object?: any }>();

  public ValuationEvent: EventEmitter<{ eventType: ValuationEventTypeEnum, object?: any }> =
    new EventEmitter<{ eventType: ValuationEventTypeEnum; object?: any }>();

  public InMailEvent: EventEmitter<InMailFolderEnum> = new EventEmitter<InMailFolderEnum>();
}
