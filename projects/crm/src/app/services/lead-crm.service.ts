import {Injectable} from '@angular/core';
import {compare} from "fast-json-patch";
import {
  CRMAttribDTO,
  CRMAttribSearchDTO,
  LeadContactDTO, LeadContactLinkDTO,
  LeadContactLinkSearchDTO,
  LeadContactSearchDTO, LeadCustomerDTO,
  LeadCustomerSearchDTO, LeadDTO, LeadNoteDTO,
  LeadNoteSearchDTO, LeadProductSearchDTO,
  LeadQueueSearchDTO,
  LeadSearchDTO, SphLeadSearchResult
} from "../interfaces/index";
import {HttpOptions} from "@capacitor-community/http";
import {DataService} from "./data.service";
import {ApiService, CustomerService, HandlerService} from '../global/services';
import {ContactSearchDTO, SearchResultDTO, ValidatedResultDTO, ValuationQuoteSearchDTO} from '../global/interfaces';
import {LeadAppraisalSearchDTO, LeadVehicleAppraisalDTO} from '../interfaces/lead-appraisal-search-dto.interface';
import {LeadChartDTO} from "../interfaces/lead-chart-dto";

@Injectable()
export class LeadCRMService {

  private serviceUrl = '/api/lead';


  constructor(
    private apiClient: ApiService, private data: DataService,
    private customerService: CustomerService,
    private handler: HandlerService) {
    // this.logging.componentName = "LeadCRMService";
  }

  globalSearch(searchDTO: {
    searchString?: string
  }): Promise<any> {

    const url = `${this.data.apiUrl}${this.serviceUrl}/sphSearch`;
    return this.apiClient.get({url, headers: {accept: 'application/json'}}, {query: searchDTO}) as Promise<SphLeadSearchResult>;
  }

  getLeads(query?: LeadSearchDTO): Promise<SearchResultDTO> {
    const url = `${this.data.apiUrl}${this.serviceUrl}s`;
    return this.apiClient.get({url, headers: {accept: 'application/json'}}, {query}) as Promise<any>;
  }

  // THIS ONLY RETRIEVES ALL THE CONTACT RECORDS FOR CRM USERS, NOT THE CRM USER RECORD ITSELF
  // FOR THAT SEE CRMUserService.Get()
  // IDEALLY THIS WOULD BE IN CONTACT SERVICE, BUT REQUIRES SEPARATION
  getCRMUserContacts(customerId, query?: ContactSearchDTO): Promise<SearchResultDTO> {
    return this.customerService.getContacts(customerId, query) as Promise<SearchResultDTO>;
  }

  getLeadsByOwner(query: LeadSearchDTO): Promise<SearchResultDTO> {
    const url = `${this.data.apiUrl}${this.serviceUrl}sByOwner`;
    return this.apiClient.get({url}, {query});
  }

  reassignLeads(leadIds: string[], newOwnerId: string, assignedById: string) {
    const data = {assignedById, newOwnerId, leadIds};
    const url = `${this.data.apiUrl}${this.serviceUrl}/assign`;
    return this.apiClient.post({url, data, headers: {accept: 'application/json'}}) as Promise<any>;
  }

  getLeadCustomers(query?: LeadCustomerSearchDTO): Promise<SearchResultDTO> {

    const url = `${this.data.apiUrl}/api/lead-customers`;
    const options = {url, headers: {accept: 'application/json'}} as HttpOptions;

    return this.apiClient.get(options, {query}) as Promise<any>;
  }

  getLeadNotes(leadId: string, query?: LeadNoteSearchDTO): Promise<SearchResultDTO> {
    const url = `${this.data.apiUrl}/api/lead/${leadId}/lead-notes`;
    const options = {url, headers: {accept: 'application/json'}} as HttpOptions;
    return this.apiClient.get(options, {query}) as Promise<any>;
  }

  getLeadQueue(query?: LeadQueueSearchDTO): Promise<SearchResultDTO> {

    let url = "";

    if (query.filters.contactId == null) {
      url = `${this.data.apiUrl}/api/customer/${query.filters.customerId}/leadQueue`;
    } else {
      url = `${this.data.apiUrl}/api/contact/${query.filters.contactId}/leadQueue`;
    }

    const options = {url, headers: {accept: 'application/json'}} as HttpOptions;
    return this.apiClient.get(options, {query}) as Promise<any>;
  }

  getLeadContacts(query?: LeadContactSearchDTO): Promise<SearchResultDTO> {
    const url = `${this.data.apiUrl}/api/lead-contacts`;
    return this.apiClient.get({url, headers: {accept: 'application/json'}}, {query}) as Promise<any>;
  }

  getLeadProducts(query?: LeadProductSearchDTO): Promise<SearchResultDTO> {
    const url = `${this.data.apiUrl}/api/lead-products`;
    return this.apiClient.get({url}, {query}) as Promise<SearchResultDTO>;
  }

  getLead(leadId: string, query?: LeadSearchDTO): Promise<ValidatedResultDTO> {
    const url = `${this.data.apiUrl}${this.serviceUrl}/${leadId}`;
    return this.apiClient.get({url}, {query}) as Promise<ValidatedResultDTO>;
  }

  getLeadContact(leadContactId: string, query?: LeadContactSearchDTO): Promise<ValidatedResultDTO> {
    const url = `${this.data.apiUrl}/api/lead-contact/${leadContactId}`;
    return this.apiClient.get({url}, {query}) as Promise<ValidatedResultDTO>;
  }

  getLeadContactLinks(leadId: string, query?: LeadContactLinkSearchDTO): Promise<SearchResultDTO> {
    const url = `${this.data.apiUrl}/api/lead/${leadId}/contact-links`;
    return this.apiClient.get({url}, {query}) as Promise<SearchResultDTO>;
  }

  getContactLinks(leadContactId: string, query?: LeadContactLinkSearchDTO): Promise<SearchResultDTO> {
    const url = `${this.data.apiUrl}/api/lead-contact/${leadContactId}/contact-links`;
    return this.apiClient.get({url}, {query}) as Promise<SearchResultDTO>;
  }

  getLeadCustomer(leadCustomerId: string, query?: LeadCustomerSearchDTO): Promise<ValidatedResultDTO> {
    const url = `${this.data.apiUrl}/api/lead-customer/${leadCustomerId}`;
    return this.apiClient.get({url}, {query}) as Promise<ValidatedResultDTO>;
  }

  addLeadCustomer(dto: LeadCustomerDTO): Promise<ValidatedResultDTO> {
    const url = `${this.data.apiUrl}/api/lead-customer`;
    return this.apiClient.post({url, data: dto}) as Promise<ValidatedResultDTO>;
  }

  addLeadContact(dto: LeadContactDTO): Promise<ValidatedResultDTO> {
    const url = `${this.data.apiUrl}/api/lead-contact`;
    return this.apiClient.post({url, data: dto}) as Promise<ValidatedResultDTO>;
  }

  addLeadContactLink(dto: LeadContactLinkDTO): Promise<ValidatedResultDTO> {
    const url = `${this.data.apiUrl}/api/lead/${dto.leadId}/contact-link`;
    return this.apiClient.post({url, data: dto}) as Promise<ValidatedResultDTO>;
  }

  addLead(dto: LeadDTO): Promise<ValidatedResultDTO> {
    const url = `${this.data.apiUrl}${this.serviceUrl}`;
    return this.apiClient.post({url, data: dto}) as Promise<ValidatedResultDTO>;
  }

  addLeadNote(dto: LeadNoteDTO): Promise<ValidatedResultDTO> {

    const url = `${this.data.apiUrl}/api/lead/${dto.leadId}/lead-note`;
    return this.apiClient.post({url, data: dto}) as Promise<ValidatedResultDTO>;
  }

  patchLeadCustomer(leadCustomerId: string, patch) {
    const url = `${this.data.apiUrl}/api/lead-customer/${leadCustomerId}`;
    return this.apiClient.patch({url, data: patch}) as Promise<ValidatedResultDTO>;
  }

  patchLead(leadId: string, patch) {
    const url = `${this.data.apiUrl}/api/lead/${leadId}`;
    return this.apiClient.patch({url, data: patch}) as Promise<ValidatedResultDTO>;
  }

  patchLeadNote(leadNoteId, patch) {
    const url = `${this.data.apiUrl}/api/lead-note/${leadNoteId}`;
    return this.apiClient.patch({url, data: patch}) as Promise<ValidatedResultDTO>;
  }

  patchLeadContact(leadContactId, patch) {
    const url = `${this.data.apiUrl}/api/lead-contact/${leadContactId}`;
    return this.apiClient.patch({url, data: patch}) as Promise<ValidatedResultDTO>;
  }

  patchLeadContactLink(leadContactLinkId, patch) {
    const url = `${this.data.apiUrl}/api/lead-contact-link/${leadContactLinkId}`;
    return this.apiClient.patch({url, data: patch}) as Promise<ValidatedResultDTO>;
  }

  getLeadValuationQuotes(leadId: string, dto?: ValuationQuoteSearchDTO) {

    const url = `${this.data.apiUrl}/api/lead/${leadId}/valuation-quotes`;
    return this.apiClient.get({url}, {query: dto}) as Promise<SearchResultDTO>;
  }

  getCRMAttribs(dto?: CRMAttribSearchDTO): Promise<SearchResultDTO> {
    const url = `${this.data.apiUrl}/api/crm-attribs`;
    return this.apiClient.get({url}, {query: dto}) as Promise<SearchResultDTO>;
  }

  patchCRMAttribs(attribId: number, dto?: CRMAttribDTO, previousDTO: CRMAttribDTO = {}) {

    const patch = compare(previousDTO, dto);
    const url = `${this.data.apiUrl}/api/crm-attrib/${attribId}`;
    return this.apiClient.patch({url, data: patch}) as Promise<SearchResultDTO>;
  }

  createCRMAttribs(dto?: CRMAttribDTO) {
    const url = `${this.data.apiUrl}/api/crm-attrib`;
    return this.apiClient.post({url, data: dto}) as Promise<SearchResultDTO>;
  }

  getLeadAppraisals(leadId: string, searchDTO?: LeadAppraisalSearchDTO): Promise<SearchResultDTO> {
    const url = `${this.data.apiUrl}/api/lead/${leadId}/appraisals`;
    return this.apiClient.get({url}, {query: searchDTO}) as Promise<SearchResultDTO>;
  }

  getLeadAppraisalDetails(leadVehicleAppraisalId: string): Promise<SearchResultDTO> {
    const url = `${this.data.apiUrl}/api/lead-vehicle-appraisal/${leadVehicleAppraisalId}`;
    return this.apiClient.get({url}) as Promise<SearchResultDTO>;
  }

  getChartData(): Promise<LeadChartDTO[]> {
    const url = `${this.data.apiUrl}/api/lead-crm/chart-data`;
    return this.apiClient.get({url}) as Promise<LeadChartDTO[]>;
  }
}
