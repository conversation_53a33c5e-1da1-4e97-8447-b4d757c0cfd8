import {Injectable} from '@angular/core';
import {SearchResultDTO, ValidatedResultDTO} from '../global/interfaces';
import {ApiService} from '../global/services';
import {DataService} from "./data.service";
import {compare} from "fast-json-patch";
import {LeadEventDocTemplateDTO, LeadEventDocTemplateSearchDTO} from "../interfaces/lead-event-doc-template";

@Injectable()
export class LeadEventDocTemplateService {

  private serviceUrl = '/api/lead-event-doc-template';

  constructor(
    private apiClient: ApiService, private data: DataService,
  ) {
  }

  search(searchDTO?: LeadEventDocTemplateSearchDTO): Promise<SearchResultDTO> {

    const url = `${this.data.apiUrl}${this.serviceUrl}s`;

    return this.apiClient.get({url, headers: {accept: 'application/json'}}, {query: searchDTO}) as Promise<any>;
  }

  create(data: LeadEventDocTemplateDTO): Promise<ValidatedResultDTO> {

    const url = `${this.data.apiUrl}${this.serviceUrl}`;

    return this.apiClient.post({url, data, headers: {accept: 'application/json'}}) as Promise<ValidatedResultDTO>;
  }

  patch(id, dto: LeadEventDocTemplateDTO, previousDTO= {}): Promise<any> {
    const patch = compare(previousDTO, dto);
    const url = `${this.data.apiUrl}${this.serviceUrl}/${id}`;
    return this.apiClient.patch({url, data: patch}) as Promise<any>;
  }
}

