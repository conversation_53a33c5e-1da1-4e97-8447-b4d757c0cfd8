import {Injectable} from '@angular/core';
import {SearchResultDTO, ValidatedResultDTO} from '../global/interfaces';
import {ApiService} from '../global/services';
import {DataService} from "./data.service";
import {compare} from "fast-json-patch";
import {LeadMediaDTO, LeadMediaSearchDTO} from "../interfaces/lead-media-dto.interface";
import {LeadMediaUploadDTO} from "../interfaces/lead-media-upload-dto.interface";

@Injectable()
export class LeadMediaService {

  private serviceUrl = '/api/lead-media';

  constructor(
    private apiClient: ApiService, private data: DataService,
  ) {
  }

  search(searchDTO?: LeadMediaSearchDTO): Promise<SearchResultDTO> {

    const url = `${this.data.apiUrl}${this.serviceUrl}s`;

    return this.apiClient.get({url, headers: {accept: 'application/json'}}, {query: searchDTO}) as Promise<any>;
  }

  create(data: LeadMediaDTO): Promise<ValidatedResultDTO> {

    const url = `${this.data.apiUrl}${this.serviceUrl}`;

    return this.apiClient.post({url, data, headers: {accept: 'application/json'}}) as Promise<ValidatedResultDTO>;
  }

  patch(id, dto: LeadMediaDTO, previousDTO= {}): Promise<any> {
    const patch = compare(previousDTO, dto);
    const url = `${this.data.apiUrl}${this.serviceUrl}/${id}`;
    return this.apiClient.patch({url, data: patch}) as Promise<any>;
  }

}

