import {Injectable} from '@angular/core';
import {ToastService} from 'ng-uikit-pro-standard';
import {LeadStatusDTO} from "../interfaces/index";
import {DataService} from "./data.service";
import { ApiService } from '../global/services';
import {BaseSearchDTO, SearchResultDTO, ValidatedResultDTO} from '../global/interfaces';

@Injectable()
export class LeadProductService {

  private serviceUrl = '/api/lead-product';


  constructor(
    private apiClient: ApiService, private data: DataService,
    private toast: ToastService,
  ) {
    // this.logging.componentName = "LeadStatusService";
  }

  getLeadProducts(query?: BaseSearchDTO): Promise<SearchResultDTO> {

    const url = `${this.data.apiUrl}/api/lead-products`;

    return this.apiClient.get({url, headers: {accept: 'application/json'}}, {query}) as Promise<SearchResultDTO>;
  }

  addLeadProduct(dto: LeadStatusDTO): Promise<ValidatedResultDTO> {
    const url = `${this.data.apiUrl}${this.serviceUrl}`;
    return this.apiClient.post({url, data: dto}) as Promise<ValidatedResultDTO>;
  }

  patchLeadProduct(id: number, patchData): Promise<ValidatedResultDTO> {
    const url = `${this.data.apiUrl}${this.serviceUrl}/${id}`;
    return this.apiClient.patch({url, data: patchData}) as Promise<ValidatedResultDTO>;
  }

  getLeadProduct(leadProductId: number): Promise<ValidatedResultDTO> {
    const url = `${this.data.apiUrl}${this.serviceUrl}/${leadProductId}`;
    return this.apiClient.get({url}) as Promise<ValidatedResultDTO>;
  }
}
