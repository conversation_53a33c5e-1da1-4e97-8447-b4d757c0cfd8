import {Injectable} from '@angular/core';
import {ToastService} from 'ng-uikit-pro-standard';
import {BaseSearchDTO, SearchResultDTO, ValidatedResultDTO} from '../global/interfaces';
import { ApiService } from '../global/services';
import {LeadStatusDTO} from "../interfaces/index";
import {DataService} from "./data.service";

@Injectable()
export class LeadStatusService {

  private serviceUrl = '/api/lead-status';


  constructor(
    private apiClient: ApiService, private data: DataService,
    private toast: ToastService,
  ) {
    // this.logging.componentName = "LeadStatusService";
  }

  getLeadStatuses(searchDTO?: BaseSearchDTO): Promise<SearchResultDTO> {

    const url = `${this.data.apiUrl}/api/lead-statuses`;
    //return this.apiClient.get({url, headers: {accept: 'application/json'}}, {query: searchDTO}) as Promise<SearchResultDTO>;
    return this.apiClient.get({url}, {query: searchDTO}) as Promise<SearchResultDTO>;
  }

  addLeadStatus(dto: LeadStatusDTO): Promise<ValidatedResultDTO> {
    const url = `${this.data.apiUrl}${this.serviceUrl}`;
    return this.apiClient.post({url, data: dto}) as Promise<ValidatedResultDTO>;
  }

  patchLeadStatus(id: number, patchData): Promise<ValidatedResultDTO> {
    const url = `${this.data.apiUrl}${this.serviceUrl}/${id}`;
    return this.apiClient.patch({url, data: patchData}) as Promise<ValidatedResultDTO>;
  }

  getLeadStatus(leadStatusId: number): Promise<ValidatedResultDTO> {
    const url = `${this.data.apiUrl}${this.serviceUrl}/${leadStatusId}`;
    return this.apiClient.get({url}) as Promise<ValidatedResultDTO>;
  }
}
