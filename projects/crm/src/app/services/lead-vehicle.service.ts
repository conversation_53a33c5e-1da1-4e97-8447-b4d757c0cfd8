import {Injectable} from '@angular/core';
import {ToastService} from 'ng-uikit-pro-standard';
import {BaseSearchDTO, SearchResultDTO, ValidatedResultDTO} from '../global/interfaces';
import { ApiService } from '../global/services';
import {LeadStatusDTO} from "../interfaces/index";
import {DataService} from "./data.service";

@Injectable()
export class LeadVehicleService {

  private serviceUrl = '/api/lead-status';

  constructor(
    private apiClient: ApiService, private data: DataService,
    private toast: ToastService,
  ) {
  }

  patchLeadVehicle(leadVehicleId, patch) {
    const url = `${this.data.apiUrl}/api/lead-vehicle/${leadVehicleId}`;
    return this.apiClient.patch({url, data: patch}) as Promise<ValidatedResultDTO>;
  }
}

