:root {

  /* Heights */
  --headerHeight: 63px;
  --underNavHeight: 0px;
  --totalHeaderHeight: calc(var(--headerHeight) + var(--underNavHeight));
  --searchMenuWidth: 250px;
  --altDangerColour: #c00;
  --footerHeight: 94px;

  /* Float Labels */
  --floatLabelWeight: 500;
  --floatLabelFontSize: 0.8rem;
  --floatLabelPadding: 5px;
  --floatLabelTop: -9px;
  --floatLabelLeft: 9px;
  --floatLabelScale: 0.8;
  --floatInputFocusLabelWeight: 500;
  --placeholderWeight: 500;
  --placeholderFontSize: 1rem;
  --aspectRatio: 0.75;
  --inverseAspectRatio: calc(1 / var(--aspectRatio));
  --fontAwesome: "Font Awesome 5 Free";
  --fas: 900;
  --widgetBgColour: #fff;
  --vrmBackgroundColour: #fff6c3;
}

.danger-color-fg {
  color: var(--altDangerColour);
}
