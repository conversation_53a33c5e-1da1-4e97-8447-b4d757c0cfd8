:root {

  --font1: "MontSerrat";
  --font2: "MontSerrat";
  --defaultFont: var(--font1);
  --widgetBorderRadius: 8px;
  --imageOutlineColour: var(--widgetBorderColour);

  /* Palette */
  --colour1: #252525;
  --colour2: #FF497B;
  --colour3: #fff;
  --colour4: #FFEE66;
  --colour5: #008DCE;
  --inputBorderColour: #b3cad6;
  --primaryColour: #252525;

  --textLabelColour: #667A88;
  --navbarBgColour: #fff;
  --textColour: var(--colour1);
  --navbarTextColour: var(--textColour);
  --bgColour: #f0f1f2;
  --headerHeight: 63px;
  --attentionBoxBackgroundColour: #dee2e6;
  --dropdownBackgroundColour: #fff;
  --dropdownItemTextColour: var(--colour1);
  --dropdownHoverItemBackgroundColour: var(--colour1);
  --dropdownHoverItemTextColour: #fff;
  --topbarIconColour: #008DCE;
  --vrmBackgroundColour: #fff6c3;
  --sideNavBackgroundColour: #fff;
  --sideNavItemBackgroundColour: #008DCE;
  --sideNavHoverTextColour: var(--colour4);
  --sideNavHoverBackgroundColour: #86C7F3;
  --sideNavTextColour: #333;
  --linkColour: var(--colour5);
  --primaryButtonColour: #008DCE;
  --secondaryButtonColour: #008DCE;
  --switchColour: #008DCE;


  --successColour: #007a5a;
  --warningColour: #d4af37;
  --errorColour: #dc3545;
  --buyNowColour: #314d5f;
  --timedSaleColour: #0e2839;
  --managedSaleColour: #1e3b4e;
  --underwriteSaleColour: #476375;
  --footerBackgroundColour: #333;

}

.device {
  --headerHeight: 96px;
  --headerPaddingTop: 30px;
}


