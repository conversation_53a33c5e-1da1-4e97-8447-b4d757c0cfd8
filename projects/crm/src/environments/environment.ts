// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

import {NgxLoggerLevel} from "ngx-logger";

export const environment = {
  production: false,
  serviceUrl: "https://localhost:5001", // todo: change this when test/dev environment ready
  messageHubUrl: "https://localhost:5001/messagehub",
  googleMapsAPIKey: "AIzaSyDRREM8vnh8GSDRXLsVt_Mi1tnhJCBYb6I",
  uInspectUrl: "https://localhost:44322",
  logging: {
    level: NgxLoggerLevel.DEBUG,
    disableConsoleLogging: false
  }
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
