import {Component, OnInit} from '@angular/core';
import {AdminAccountsService} from '../../services';

@Component({
  selector: 'app-accounts-data',
  templateUrl: 'accounts-data.component.html',
  styleUrls: ['accounts-data.component.scss']
})
export class AccountsDataComponent implements OnInit {

  constructor(
    private adminAccountsService: AdminAccountsService
  ) {
  }

  tenants: any;

  ngOnInit() {
    this.adminAccountsService.getTenants().then(res => {
      this.tenants = res;
      console.log("Tenants data: ", res);
    })
  }
}
