/* Order lines table styles */
:host {
  display: block;
  background-color: #f2f2f2;
  padding: 15px;
}

h3 {
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 10px 0;
}

.table-header {
  display: flex;
  font-size: 11px;
  font-weight: 500;
  color: #666;
  padding: 6px 0;
  border-bottom: 1px solid #eaeaea;
  text-transform: uppercase;
}

.table-body {
  background-color: white;
}

.line-row {
  display: flex;
  padding: 8px 0;
  border-bottom: 1px solid #eaeaea;
}

.line-row:last-child {
  border-bottom: none;
}

.col-product { width: 30%; }
.col-reference { width: 25%; }
.col-tax-rate { width: 15%; text-align: center; }
.col-net, .col-vat, .col-gross {
  width: 10%;
  text-align: right;
  font-family: monospace;
}
