import {Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild} from '@angular/core';
import {ContactDTO, CustomerInternalInfoDTO} from '../../../global/interfaces';
import {ModalDirective} from "ng-uikit-pro-standard";
import {AdminWhosWhoService} from "../services";
import {StatusEnum} from '../../../global/enums';
import {UntypedFormBuilder, UntypedFormControl, UntypedFormGroup} from "@angular/forms";
import {AdminCustomerInternalInfoService, HelpersService} from '../../../global/services';

@Component({
  selector: 'app-admin-assign-customer-dialog',
  templateUrl: './admin-assign-customer-dialog.component.html',
  styleUrls: ['./admin-assign-customer-dialog.component.scss']
})
export class AdminAssignCustomerDialogComponent implements OnInit, OnChanges {

  @Input() customerInternalInfo: CustomerInternalInfoDTO;
  @Input() showAssignDialog: boolean;
  @Output() customerAssignedEvent = new EventEmitter<{ customerId: number, assignedTo: number }>();

  @ViewChild("assignCustomerModal") assignCustomerModal: ModalDirective;

  assignedOptions: ContactDTO[];
  adminLookup = {};
  assignForm: UntypedFormGroup;
  assignedTo: string;
  statusOptions: { label: string; value: number }[] = [];
  statusName: any[] = [];

  constructor(
    private helpersService: HelpersService,
    private whosWhoService: AdminWhosWhoService,
    private adminCustomerInternalInfoService: AdminCustomerInternalInfoService, private fb: UntypedFormBuilder) {

    this.loadSiteAdmins().then(() => {
    });

    this.assignForm = this.fb.group({
      customerId: new UntypedFormControl(null),
      assignedTo: new UntypedFormControl(null),
      assignComment: new UntypedFormControl(null),
    });

    this.initStatuses();

  }

  initStatuses() {

    const statuses = this.helpersService.getNamesAndValues(StatusEnum);

    this.statusOptions = statuses.map(status => {
      this.statusName[status.value] = status.name;
      return {label: status.name, value: status.value};
    });
  }

  ngOnChanges(changes: SimpleChanges) {

    if (changes["showAssignDialog"]) {
      if (this.showAssignDialog == true) {

        this.assignCustomerModal.show();
        this.assignForm.patchValue({
          customerId: this.customerInternalInfo.customerId,
          assignedTo: this.customerInternalInfo.assignedTo,
          assignComment: null
        });
      }
    }
  }

  ngOnInit(): void {
  }

  get f() {
    return this.assignForm.value;
  }

  async loadSiteAdmins() {

    return this.whosWhoService.getSiteAdmins({component: 'whos-who'}, true).then((res) => {

      if (res.totalItems > 0) {

        const admins = res.results;

        this.assignedOptions = res.results.map((x) => {
          return {
            id: x.id,
            contactName: x.contactName,
            email: x.email,
            statusId: x.statusId,
            disabled: (x.statusId !== StatusEnum.Active),
          };
        });

        admins.forEach((x) => {
          this.adminLookup[x.id] = x.contactName;
        });
      }
    });
  }

  async submitAssignForm() {

    const origComment = this.assignForm.get('assignComment').value;
    const customerId = this.assignForm.get('customerId').value;
    const assignedTo = this.assignForm.get('assignedTo').value;
    const assignComment = " to " + this.adminLookup[assignedTo] +
      (origComment ? (": " + origComment) : "");

    this.adminCustomerInternalInfoService.setAssignedTo(customerId, {assignedTo, assignComment}).then((x) => {

      this.assignCustomerModal.hide();
      this.customerAssignedEvent.emit({customerId, assignedTo});
    });
  }

  onClose($event: ModalDirective) {
    this.customerAssignedEvent.emit(null);
  }

  selectRow(i: string) {
    this.assignedTo = i;
    this.assignForm.patchValue({assignedTo: i});
  }
}
