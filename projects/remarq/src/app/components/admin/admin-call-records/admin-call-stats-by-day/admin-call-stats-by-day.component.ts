import {Component, EventEmitter, Input, OnInit, Output} from "@angular/core";
import {AdminVoipService} from "../../services";
import {CallRecordDTO, CallStatsDTO, SearchCallRecordsDTO } from "projects/common/interfaces";

@Component({
  selector: 'app-admin-call-stats-by-day',
  templateUrl: 'admin-call-stats-by-day.component.html',
  styleUrls: ['admin-call-stats-by-day.component.scss']
})
export class AdminCallStatsPerDayComponent implements OnInit {

  constructor(private voipService: AdminVoipService) {
  }

  @Output() filterChanged = new EventEmitter<{ contactId: string }>

  private _filterContactId?: string = null;

  @Input('filterContactId')
  set filterContactId(value: string) {
    if (value !== this._filterContactId) {
      this._filterContactId = value;
      this.onFilterChanged({ contactId: value });
    }
  }
  get filterContactId(): string {
    return this._filterContactId;
  }

  callStats: CallStatsDTO[] = [];
  filteredCallStats: CallStatsDTO[] = [];

  callRecords: CallRecordDTO[] = [];

  callFilter: {
    date?: Date,
    contactId?: string
    direction?: string;
  } = {};

  isBusy = false;

  ngOnInit() {
    this.fetchCallStats();
  }

  fetchCallStats() {
    this.voipService.getStats().then(res => {
      this.callStats = res;
      this.filteredCallStats = res;
      console.log("Call stats: ", res)
    });
  }

  onFilterChanged(data: {contactId: string}) {
    if (data.contactId) {
      this.filteredCallStats = this.callStats.filter(x => x.contactId == data.contactId);
    } else {
      this.filteredCallStats = this.callStats;
    }

    this.callFilter.contactId = data.contactId;
  }

  fetchContactCallRecords() {
    this.isBusy = true;

    const dto = {
      component: 'call-records',
      filters: {
        contactId: this.callFilter.contactId,
        callDate: this.callFilter.date,
        callDirection: this.callFilter.direction
      }
    } as SearchCallRecordsDTO;

    this.voipService.search(dto).then(res => {
      this.callRecords = res.results;
      this.isBusy = false;

      console.log("Loaded call records: ", this.callRecords);
    }).catch(() => {
      this.isBusy = false;
    });
  }

  onStatClicked(data: {date: any; contactId: string}) {
    this.callFilter.contactId = data.contactId;
    this.callFilter.date = data.date;

    this.fetchContactCallRecords();
  }
}
