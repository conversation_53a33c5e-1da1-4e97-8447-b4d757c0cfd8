import { Component, OnInit, Input, OnChanges, SimpleChanges } from '@angular/core';
import {Chart, registerables} from 'chart.js';
import { CallStatsDTO } from '../../../global/interfaces';

Chart.register(...registerables);

@Component({
  selector: 'app-admin-call-stats-chart',
  template: `<div class="chart-container"><canvas class="chart-canvas" id="callDataChart"></canvas></div>`,
  styleUrls: ['./admin-call-records.component.scss']
})
export class AdminCallStatsChartComponent implements OnInit, OnChanges {
  @Input() callData: CallStatsDTO[];
  @Input() dateRange: 'days' | 'weeks' | 'months';
  private chart: any;

  constructor() { }

  ngOnInit(): void {
    this.renderChart();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.callData || changes.dateRange) {
      this.renderChart();
    }
  }

  renderChart(): void {
    if (this.chart) {
      this.chart.destroy();
    }

    const groupedData = this.groupByDateRange(this.callData, this.dateRange);
    const labels = Object.keys(groupedData);
    const totalCalls = [];
    const totalDuration = [];
    const totalCost = [];

    labels.forEach(label => {
      totalCalls.push(groupedData[label].totalCalls);
      totalDuration.push(groupedData[label].totalDuration);
      totalCost.push(groupedData[label].totalCost);
    });

    const ctx = (document.getElementById('callDataChart') as HTMLCanvasElement).getContext('2d');
    this.chart = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: labels,
        datasets: [
          {
            label: 'Total Calls',
            data: totalCalls,
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            borderColor: 'rgba(75, 192, 192, 1)',
            borderWidth: 1,
            yAxisID: 'y-axis-1',
            //fill: true,
            //tension: 0.4
          },
          {
            label: 'Total Duration (mins)',
            data: totalDuration,
            backgroundColor: 'rgba(153, 102, 255, 0.2)',
            borderColor: 'rgba(153, 102, 255, 1)',
            borderWidth: 1,
            yAxisID: 'y-axis-2',
            // fill: true,
            // tension: 0.4
          },
          {
            label: 'Total Cost (£)',
            data: totalCost,
            backgroundColor: 'rgba(255, 159, 64, 0.2)',
            borderColor: 'rgba(255, 159, 64, 1)',
            borderWidth: 1,
            yAxisID: 'y-axis-3',
            // fill: true,
            // tension: 0.4
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          'y-axis-1': {
            type: 'linear',
            position: 'left',
            beginAtZero: true,
            grid: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          },
          'y-axis-2': {
            type: 'linear',
            position: 'right',
            beginAtZero: true,
            grid: {
              drawOnChartArea: false,
            }
          },
          'y-axis-3': {
            type: 'linear',
            position: 'right',
            beginAtZero: true,
            grid: {
              drawOnChartArea: false,
            }
          },
          x: {
            type: 'category',
            ticks: {
              autoSkip: false,
            }
          }
        }
      }
    });
  }

  groupByDateRange(data: CallStatsDTO[], range: 'days' | 'weeks' | 'months') {
    return data.reduce((acc, call) => {
      const dateKey = this.getDateKey(call.callDate, range);
      if (!acc[dateKey]) {
        acc[dateKey] = { totalCalls: 0, totalDuration: 0, totalCost: 0 };
      }
      acc[dateKey].totalCalls += call.totalCalls;
      acc[dateKey].totalDuration += call.totalDuration;
      acc[dateKey].totalCost += call.totalCost;
      return acc;
    }, {});
  }

  getDateKey(date: Date, range: 'days' | 'weeks' | 'months'): string {
    const d = new Date(date);
    const options: Intl.DateTimeFormatOptions = { day: '2-digit', month: '2-digit', year: 'numeric' };

    switch (range) {
      case 'days':
        return d.toLocaleDateString('en-GB', options);  // 'en-GB' for dd/MM/yyyy format
      case 'weeks':
        const startOfWeek = new Date(d.setDate(d.getDate() - d.getDay()));
        const weekStartString = startOfWeek.toLocaleDateString('en-GB', options);
        return `${weekStartString} - Week ${this.getWeekNumber(startOfWeek)}`;
      case 'months':
        return `${('0' + (d.getMonth() + 1)).slice(-2)}/${d.getFullYear()}`;
      default:
        return '';
    }
  }

  getWeekNumber(d: Date): number {
    const oneJan = new Date(d.getFullYear(), 0, 1);
    const numberOfDays = Math.floor((d.getTime() - oneJan.getTime()) / (24 * 60 * 60 * 1000));
    return Math.ceil((d.getDay() + 1 + numberOfDays) / 7);
  }


  // Helper function to generate random color
  getRandomColor(index: number): string {
    const colors = [
      'rgba(255, 99, 132, 0.2)',
      'rgba(54, 162, 235, 0.2)',
      'rgba(255, 206, 86, 0.2)',
      'rgba(75, 192, 192, 0.2)',
      'rgba(153, 102, 255, 0.2)',
      'rgba(255, 159, 64, 0.2)'
    ];
    return colors[index % colors.length];
  }
}
