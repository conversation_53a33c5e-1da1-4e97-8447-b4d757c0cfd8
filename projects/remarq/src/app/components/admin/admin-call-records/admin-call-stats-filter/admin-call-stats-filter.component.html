<div class="d-flex flex-wrap mb-1" style="width: 100%">
  <div class="flex-shrink-1">
    <form *ngIf="filterForm" [formGroup]="filterForm">
      <div class="d-flex grid-gap-5">
        <div class="select select-sm no-margin" style="min-width: 200px;">
          <mdb-select-2 [outline]="true"
                        formControlName="contactId"
                        placeholder="Select User">
            <mdb-select-option [value]="null">All Users</mdb-select-option>
            <mdb-select-option *ngFor="let vu of voipUserOptions" [value]="vu.value">{{ vu.label }}
            </mdb-select-option>
          </mdb-select-2>
        </div>
        <!--        <div class="select-sm no-margin" style="min-width: 200px;">-->
        <!--          <mdb-select-2 [outline]="true"-->
        <!--                        formControlName="dateRange"-->
        <!--                        placeholder="Select Range">-->
        <!--            <mdb-select-option *ngFor="let op of dateRangeOptions" [value]="op.value">{{ op.label }}-->
        <!--            </mdb-select-option>-->
        <!--          </mdb-select-2>-->
        <!--        </div>-->
      </div>
    </form>
  </div>
</div>
