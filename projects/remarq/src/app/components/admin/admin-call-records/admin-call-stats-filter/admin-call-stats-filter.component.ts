// template: `<div><canvas #trendLineChart></canvas></div>`
import {Component, EventEmitter, OnInit, Output} from "@angular/core";
import {UntypedFormBuilder, UntypedFormControl, UntypedFormGroup} from "@angular/forms";
import {IOption} from "ng-uikit-pro-standard";
import {AdminWhosWhoService} from "../../services";

@Component({
  selector: 'app-admin-call-stats-filter',
  templateUrl: 'admin-call-stats-filter.component.html',
  styleUrls: ['../admin-call-records.component.scss']
})
export class AdminCallStatsFilterComponent implements OnInit {

  constructor(private fb: UntypedFormBuilder,
              private whosWhoService: AdminWhosWhoService) {

    this.filterForm = this.fb.group({
      dateRange: new UntypedFormControl('days'),
      contactId: new UntypedFormControl(null),
    });

    this.filterForm.valueChanges.subscribe(() => {
      this.onFilterChanged();
    });
  }

  @Output() filterChanged = new EventEmitter<{ contactId: string }>

  filterForm: UntypedFormGroup;
  voipUserOptions: Array<IOption>;

  isBusy = false;

  ngOnInit() {
    this.fetchUsers();
  }

  fetchUsers() {
    this.whosWhoService.getSiteAdmins({component: 'whos-who'}, true).then((res) => {
      this.voipUserOptions = this.whosWhoService.getAssignedOptions(res.results);
    });
  }

  get f() {
    return this.filterForm.controls;
  }

  // filterToMe() {
  //   if (this.filterForm.value.assignedTo !== this.currentUser.contactId) {
  //     this.filterForm.patchValue({contactId: this.currentUser.contactId});
  //   } else {
  //     this.filterForm.patchValue({contactId: null});
  //   }
  // }

  onFilterChanged() {
    this.filterChanged.emit({ contactId: this.f?.contactId?.value });
  }
}
