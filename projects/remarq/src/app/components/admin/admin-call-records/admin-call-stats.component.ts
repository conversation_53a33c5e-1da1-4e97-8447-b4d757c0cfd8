import {Component, EventEmitter, OnInit, Output} from "@angular/core";
import {CallRecordDTO, CallStatsDTO, SearchCallRecordsDTO} from "../../../global/interfaces";
import {AdminVoipService, AdminWhosWhoService} from "../services";
import {UntypedFormBuilder, UntypedFormControl, UntypedFormGroup} from "@angular/forms";
import {IOption} from "ng-uikit-pro-standard";

@Component({
    selector: 'app-admin-callstats',
    templateUrl: './admin-call-stats.component.html',
    styleUrls: ['./admin-call-records.component.scss']
})
export class AdminCallStatsComponent implements OnInit {

  constructor(private voipService: AdminVoipService,
              private fb: UntypedFormBuilder,
              private whosWhoService: AdminWhosWhoService) {

    this.filterForm = this.fb.group({
      dateRange: new UntypedFormControl('days'),
      contactId: new UntypedFormControl(null),
    });

    this.filterForm.valueChanges.subscribe(() => {
      this.refreshFilter();
    });
  }

  @Output() filterChanged = new EventEmitter<{ contactId: string }>

  filterForm: UntypedFormGroup;
  callStats: CallStatsDTO[] = [];
  filteredCallStats: CallStatsDTO[] = [];

  contactCallRecords: CallRecordDTO[] = [];

  voipUserOptions: Array<IOption>;
  dateRangeOptions = [
    {label: "Days", value: 'days'},
    {label: "Weeks", value: 'weeks'},
    {label: "Months", value: 'months'},
  ];

  isBusy = false;
  selectedRow: number;

  ngOnInit() {
    this.fetchRecords();
    this.fetchUsers();
  }

  fetchUsers() {
    this.whosWhoService.getSiteAdmins({component: 'whos-who'}, true).then((res) => {
      this.voipUserOptions = this.whosWhoService.getAssignedOptions(res.results);
    });
  }

  get f() {
    return this.filterForm.controls;
  }

  fetchRecords() {
    this.voipService.getStats().then(res => {
      this.callStats = res;
      this.filteredCallStats = res;
      console.log("Call stats: ", res)
    });
  }

  refreshFilter() {
    if (this.f?.contactId?.value) {
      this.filteredCallStats = this.callStats.filter(x => x.contactId == this.f.contactId.value)
    } else {
      this.filteredCallStats = this.callStats;
    }

    this.filterChanged.emit({ contactId: this.f?.contactId?.value });
  }

  fetchContactCallRecords(contactId: string, date: Date, direction: string) {
    this.isBusy = true;

    const dto = {
      component: 'call-records',
      filters: {
        contactId: contactId,
        callDate: date,
        callDirection: direction
      }
    } as SearchCallRecordsDTO;

    this.voipService.search(dto).then(res => {
      this.contactCallRecords = res.results;
      this.isBusy = false;

      console.log("Contact call records: ", this.contactCallRecords);
    }).catch(() => {
      this.isBusy = false;
    });
  }

  selectRow(index: number): void {
    this.selectedRow = index;
  }
}
