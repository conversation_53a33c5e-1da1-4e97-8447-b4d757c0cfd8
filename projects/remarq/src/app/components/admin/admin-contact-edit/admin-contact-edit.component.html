<div mdbModal #editContactModal="mdbModal" class="modal fade" tabindex="-1"
     data-backdrop="false"
     [config]="{backdrop: 'static'}"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true">

  <div class="modal-dialog modal-dialog-centered modal-md" role="document">
    <div class="modal-content">
      <div class="modal-header">
        Edit User
      </div>

      <form [formGroup]="editContactForm" (ngSubmit)="saveContact()">
        <div class="modal-body">

          <input type="hidden" formControlName="id">

          <div class="row">

            <div class="col-md-12">
              <div class="md-form">
                <input mdbInput class="form-control float-label" id="editContactName" formControlName="contactName">
                <label for="editContactName">Contact Name</label>
              </div>
            </div>

            <div class="col-md-12">
              <div class="md-form">
                <input mdbInput class="form-control float-label" id="editEmail" formControlName="email">
                <label for="editEmail">E-mail</label>
              </div>
            </div>

            <div class="col-md-6">
              <div class="md-form">
                <input mdbInput class="form-control float-label" id="editPhone1" formControlName="phone1">
                <label for="editPhone1">Phone 1</label>
              </div>
            </div>

            <div class="col-md-6">
              <div class="md-form">
                <input mdbInput class="form-control float-label" id="editPhone2" formControlName="phone2">
                <label for="editPhone2">Phone 2</label>
              </div>
            </div>

            <div class="col-md-12 mb-3 select" *ngIf="roleChoices != null && roleChoices.length > 0">

              <mdb-select-2 [outline]="true" [multiple]="true" placeholder="Add Roles" label="Roles"
                            formControlName="roles">
                <mdb-select-option *ngFor="let option of this.roleChoices"
                                   value="{{ option.value }}">{{ option.label }}
                </mdb-select-option>
              </mdb-select-2>

            </div>

            <div class="col-md-6 pt-2" *ngIf="adminInRoles()">
              <div class="md-form">
                <input mdbInput class="form-control float-label" id="extension" formControlName="extension">
                <label for="extension">Extension</label>
              </div>
            </div>

          </div>
        </div>
        <div class="modal-footer">
          <div class="d-flex w-100">
            <div class="flex-grow-1" *ngIf="editingContact">
              <button class="mr-2 btn btn-primary-outline" type="button" (click)="toggleActivation()">
                <span *ngIf="editingContact.statusId == StatusEnum.Active">Deactivate</span>
                <span *ngIf="editingContact.statusId == StatusEnum.Paused">Reactivate</span>
                <span *ngIf="editingContact.statusId == StatusEnum.Pending">Activate</span>
                <span *ngIf="editingContact.statusId == StatusEnum.Deleted">Undelete</span>
              </button>
            </div>
            <div class="flex-shrink-1">
              <button class="mr-2 btn btn-tertiary-outline" type="button" (click)="this.editContactModal.hide();">
                Cancel
              </button>
              <button class="btn btn-tertiary" type="submit">Save</button>
            </div>
          </div>
        </div>

      </form>
    </div>
  </div>
</div>
