import {Component, Input, OnInit} from '@angular/core';
import {SearchService} from "../../../services";
import {AdminUrlService, AdminWhosWhoService} from "../services";
import {ContactDTO, CustomerDTO, CustomerSearchTotalsDTO, User, WhosWhoAdminSearchDTO} from "../../../global/interfaces";
import {UntypedFormBuilder, UntypedFormControl, UntypedFormGroup} from "@angular/forms";
import {IOption} from "ng-uikit-pro-standard";
import {UserService} from '../../../global/services';
import { EMPTY_GUID } from 'projects/common/shared';

@Component({
  selector: 'app-admin-search-view',
  templateUrl: './admin-search-view.component.html',
  styleUrls: ['./admin-search-view.component.scss']
})
export class AdminSearchViewComponent implements OnInit {

  @Input() title: string;
  isBusy = false;
  currentUser: User;

  constructor(private searchService: SearchService,
              public adminUrl: AdminUrlService,
              public userService: UserService,
              private fb: UntypedFormBuilder,
              private whosWhoService: AdminWhosWhoService) {

    this.filterForm = this.fb.group({
      assignedTo: new UntypedFormControl(EMPTY_GUID),
      daysAgo: new UntypedFormControl(0),
    });

    this.filterForm.valueChanges.subscribe(() => {
      this.search();
    });


  }

  page = 1;
  count = 0;
  pageSizes = [10, 20, 50, 100];
  pageSize = 10;

  topSearchers: CustomerSearchTotalsDTO[];
  filterForm: UntypedFormGroup;
  assignedOptions: Array<IOption>;
  daysAgoOptions = [
    {label: "All Dates", value: 0},
    {label: "Last 7 days", value: 7},
    {label: "Last 30 days", value: 30},
    {label: "Last 90 days", value: 90},
  ];

  ngOnInit(): void {
    this.fetchAssignees();
    this.fetchTopSearchers();

    this.userService.loadCurrentUser().then(() => {
      this.currentUser = this.userService.CurrentUser;
    });
  }

  fetchAssignees() {
    this.whosWhoService.getSiteAdmins({component: 'whos-who'}, true).then((res) => {
      this.assignedOptions = this.whosWhoService.getAssignedOptions(res.results);
    });
  }

  fetchTopSearchers() {
    this.isBusy = true;
    const dto = {
      filters: {
        ...this.filterForm?.value
      },
      offset: (this.page - 1) * this.pageSize,
      limit: this.pageSize
    } as WhosWhoAdminSearchDTO;

    this.searchService.getTopSearches(dto).then(res => {
      this.topSearchers = res.results;
      this.count = res.totalItems;
      this.isBusy = false;
    });
  }

  onTableDataChange(page: number) {
    // fetch records using page as offset
    this.page = page;
    this.fetchTopSearchers();
  }

  handlePageSizeChange(event: any) {
    this.pageSize = event.target.value;
    this.page = 1;
    this.fetchTopSearchers();
  }

  search() {
    this.fetchTopSearchers();
  }

  filterToMe() {
    if (this.filterForm.value.assignedTo !== this.currentUser.contactId) {
      this.filterForm.patchValue({assignedTo: this.currentUser.contactId});
    } else {
      this.filterForm.patchValue({assignedTo: EMPTY_GUID});
    }
  }

  assignedToName(item: CustomerDTO) {
    if (item.customerInternalInfo?.assignedTo != null && this.assignedOptions.length > 0) {
      return this.assignedOptions.find(x => x.value === item.customerInternalInfo?.assignedTo)?.label;
    }
  }

  protected readonly EMPTY_GUID = EMPTY_GUID;
}
