
links {

  .nav-link {
    display: flex !important;
  }

  .nav-label {
    flex-grow: 1;
  }

  .nav-caret {
    flex-shrink: 1;
    color: #aaa;
  }
}

.assigned-to {

  white-space: nowrap;
  background-color: #E7F0F8;
  border: 1px solid #C1D9EE;
  display: inline-block;
  color: var(--textColour);
  font-weight: 400;

  &.unassigned {
    background-color: var(--dangerColour);
    border: 1px solid var(--dangerColour);
    color: #fff;
  }

  .assigned-to-link {
    font-weight: 700;
  }
}

.filter-box {
  padding: 10px 15px !important;
  margin-bottom: 10px;
}

.medium-input {

  .input-group-text {
    padding: 0.1rem 0.5rem !important;
    font-size: 0.875rem !important;
    height: 30px;
  }

  .form-control {
    padding: 0.1rem 0.5rem !important;
    line-height: 1 !important;
    height: 30px;
    font-size: 0.875rem !important;
  }
}

.narrow-input {

  .input-group-text {
    padding: 0.1rem 0.5rem !important;
    font-size: 0.875rem !important;
    height: 26px;
  }

  .form-control {
    padding: 0.1rem 0.5rem !important;
    line-height: 1 !important;
    height: 26px;
    font-size: 0.875rem !important;
  }
}


#filterValues {
  position: absolute;
  width: 400px;
  background-color: #fff;
  padding: 10px;
  z-index: 999;
  border-radius: 0 3px 3px 0;
  box-shadow: 0 5px 11px 0 rgba(0, 0, 0, .18), 0 4px 15px 0 rgba(0, 0, 0, .15);

}

#admin-side-nav {

  .side-nav {
    padding-top: var(--totalHeaderHeight) !important;
    width: 200px !important;
    margin-top: 0px !important;
  }
}


#admin-main-panel {

  padding-top: 0 !important;
  margin-right: 0px !important;
  padding-right: 0px !important;
  padding-left: 0px !important;
  margin-left: 0px !important;
}

@media (min-width: 1200px) {

  #admin-main-panel {

    margin-left: 0rem !important;
    padding-left: 13rem !important;
    margin-right: 0px !important;
  }
}

