<div class="d-flex flex-wrap mb-1">
  <div class="flex-grow-1">
    <h1 class="page-header" style="line-height: 32px; margin-bottom: 0px">{{ title }}</h1>
  </div>
  <div class="flex-shrink-1">
    <form *ngIf="filterForm" [formGroup]="filterForm" (submit)="search()">
      <div class="d-flex grid-gap-5">
        <div>
          <div class="btn btn-sm btn-secondary" (click)="filterToMe()"><i [class]="filterForm.value.assignedTo == currentUser.contactId ? 'fas fa-user-tie' : 'fa fa-users'"></i></div>
        </div>
        <div class="select select-sm no-margin" style="min-width: 200px;">
          <mdb-select-2 [outline]="true"
                        formControlName="assignedTo"
                        placeholder="Select Assigned">
            <mdb-select-option [value]="EMPTY_GUID">All Assignees</mdb-select-option>
            <mdb-select-option *ngFor="let ato of assignedOptions" [value]="ato.value">{{ ato.label }}
            </mdb-select-option>
          </mdb-select-2>
        </div>
        <div class="select select-sm no-margin" style="min-width: 200px;">

          <mdb-select-2 [outline]="true"
                        formControlName="daysAgo"
                        placeholder="Select Range">
            <mdb-select-option *ngFor="let dao of daysAgoOptions" [value]="dao.value">{{ dao.label }}
            </mdb-select-option>
          </mdb-select-2>
        </div>
      </div>
    </form>
  </div>
</div>

<div class="widget p-2">
  <div *ngIf="isBusy">
    <app-loading-spinner [centreSpinner]="true"></app-loading-spinner>
  </div>


  <div *ngIf="!isBusy">
    <div *ngIf="logins.length === 0">
      <div class="text-center pt-4 pb-3">
        <h1>No results found</h1>
      </div>
    </div>

    <div *ngIf="logins.length > 0">

      <table *ngIf="!isBusy"
             class="table table-striped table-hover table-compressed table-narrow" maxWidth="400" mdbTable
             mdbTableScroll
             scrollX="true">
        <thead>
        <tr>
          <th>Customer</th>
          <th>Contact</th>
          <th>Action</th>
          <th>Date</th> <!-- sale details -->
          <th>IP Address</th>
          <th>Location</th>
          <th class="shrink-cell">Assigned</th>
        </tr>
        </thead>
        <tbody>
        <tr
          (click)="adminUrl.adminCustomerView(item.customerId, false)"
          *ngFor="let item of logins | paginate: { itemsPerPage: pageSize, currentPage: page, totalItems: count }">

          <td class="shrink-cell">
            <div class="table-line-1">{{ item.customer?.customerName }}</div>
            <div class="table-line-2">{{ item.customer?.email }}</div>
          </td>
          <td class="shrink-cell">
            <div class="table-line-1">{{ item.contact?.contactName }}</div>
            <div class="table-line-2">{{ item.contact?.email }}</div>
          </td>
          <td>
            {{ item.actionName }}
          </td>
          <td>
            {{ item.added | date: 'dd MMM YYYY HH:mm' }}
          </td>
          <td>
            <div class="table-line-1">{{ item.ipAddress }}</div>
          </td>
          <td>
            <div class="table-line-1">{{ item.ipLocation }}</div>
          </td>
          <td class="shrink-cell">
            {{ assignedToName(item) || "Unassigned" }}
          </td>
        </tr>
        </tbody>
      </table>

      <div class="h-100 pagination-divider">
        <div class="row">
          <div class="col-6 pagination-page-size">
            Items per Page:
            <select (change)="handlePageSizeChange($event)" class="form-control w-auto inline-block">
              <option *ngFor="let size of pageSizes" [ngValue]="size">
                {{ size }}
              </option>
            </select>
          </div>

          <div class="col-6 text-right">
            <pagination-controls
              (pageChange)="onTableDataChange($event)"
              class="paginator"
              nextLabel="Next"
              previousLabel="Prev"
              responsive="true">
            </pagination-controls>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

