import {Component, Input, OnInit, ViewChild} from '@angular/core';
import {AdviewService} from "../../../services";
import {AdvertDTO, AdviewDTO, WhosWhoAdminSearchDTO} from "../../../global/interfaces";
import {AdminUrlService} from "../services";
import { SaleTypeName } from '../../../global/enums';

@Component({
  selector: 'app-customer-adview-adverts',
  templateUrl: './customer-adview-adverts.component.html',
  styleUrls: ['./customer-adview-adverts.component.scss']
})
export class CustomerAdviewAdvertsComponent implements OnInit {

  constructor(private adviewService: AdviewService,
              public adminUrl: AdminUrlService) {
  }

  saleTypeName = SaleTypeName;

  private _customerId: any;
  @Input('customerId') set customerId(value) {
    if (value && value != this._customerId) {
      this._customerId = value;
      this.fetchAdviews();
    }
  }

  get customerId() {
    return this._customerId;
  }

  isBusy = false;
  adViews: AdviewDTO[];
  adverts: AdvertDTO[];

  ngOnInit(): void {
  }

  async fetchAdviews() {
    this.isBusy = true;

    const dto = {
      component: 'admin-advert-view',
      filters: {
        customerId: this.customerId
      },
    } as WhosWhoAdminSearchDTO;

    await this.adviewService.getAdminAdviews(dto).then(res => {
      this.adverts = res.results.map(a => {
        return a.advert;
      });
      console.log("Adverts: ", this.adverts);

      this.isBusy = false;
    }).catch(err => {
      this.isBusy = false;
    });
  }

}
