.search-list {
  max-height: 600px;
  overflow-y: auto;
}

.search-container {
  border-bottom: 1px solid #eee;
  display: flex;
}

.search-flex:nth-of-type(odd) {
  background-color: #F7F8FC;
}

//.search-container:nth-child(odd) {
//  background-color: #f2f2f2;
//}
//
//.search-container:nth-child(even) {
//  background-color: #e0e0e0;
//}

.search-flex div:nth-child(odd) {
  background-color: #f2f2f2;
}

.search-flex div:nth-child(even) {
  background-color: #e8e8e8;
}
