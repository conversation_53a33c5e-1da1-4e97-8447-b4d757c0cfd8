<div class="widget p-3">

  <div class="d-flex">

    <div *ngIf="title" class="header flex-grow-1">{{ title }}</div>

    <div class="action-filter ">
      <div class="input-group input-group-xs flex-shrink-1">
        <div class="input-group-prepend">
          <div class="input-group-text">
            Filter
          </div>
        </div>

        <div>
          <mdb-select-2 [style]="{width: '250px'}"
                        [(ngModel)]="actionFilter"
                        (ngModelChange)="changeFilter()">
            <mdb-select-option [value]="0">All Activity</mdb-select-option>
            <mdb-select-option *ngFor="let contactActionOption of contactActionOptions"
                               [value]="contactActionOption.value">
              <span>{{ contactActionOption.label }}</span>
            </mdb-select-option>
          </mdb-select-2>
        </div>
      </div>
    </div>
  </div>

  <div
    style="width: 100%; min-height: 150px; max-height: 400px; overflow-y: scroll; overflow-x: hidden; margin-top: 7px;"
    #scrollable
    class="widget contact-actions">
    <div>
      <table class="sticky-column sticky-first-column sticky-last-column table-striped table table-narrow">
        <tbody>
        <tr *ngFor="let action of contactActions">
          <td style="font-size: 0.7rem; font-weight: 600; white-space: nowrap">
            <div class="table-line-1">
              <span class="time">{{ action.added | date:'dd/MM/YY HH:mm:ss' }}:</span> {{ action.actionName }}
            </div>
            <div class="table-line-2">
              <span *ngIf="action.contact?.contactName">{{ action.contact?.contactName }} &nbsp; </span>
              <span *ngIf="action.contact?.phone1">{{ action.contact?.phone1 }} &nbsp; </span>
              <span *ngIf="action.contact?.email">{{ action.contact?.email }} &nbsp; </span>
            </div>
          </td>
          <td>{{ action.ipAddress }}</td>
        </tr>
        </tbody>
      </table>
    </div>
    <div class="text-center" [attr.data-last]="'true'">
      <span *ngIf="!actionsLoaded">
        <i class="fa fa-spin fa-spinner"></i> Loading..
      </span>
      <span *ngIf="actionsLoaded">
        &nbsp;
      </span>
    </div>
  </div>
</div>
