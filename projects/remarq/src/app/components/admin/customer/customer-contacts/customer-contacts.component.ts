import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit, Output,
  QueryList,
  ViewChild,
  ViewChildren
} from '@angular/core';
import {UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators} from '@angular/forms';
import {Router} from '@angular/router';
import {EventService, URLService} from '../../../../services';
import {MDBModalService, ModalDirective} from 'ng-uikit-pro-standard';
import {ContactDTO, InviteDTO, User} from '../../../../global/interfaces';
import {
  AdminContactService,
  ContactService,
  CustomerService,
  LoggerService,
  UserService
} from '../../../../global/services';
import {StatusEnum} from '../../../../global/enums';
import {GlobalConstants} from '../../../../global/shared';
import {NgbdSortableHeader} from '../../../../global/directives';
import {AdminCustomerService} from "../../services";

@Component({
  selector: 'app-customer-contacts',
  templateUrl: './customer-contacts.component.html',
  styleUrls: ['./customer-contacts.component.scss'],
})
export class CustomerContactsComponent implements OnInit, OnDestroy {

  @ViewChild("newContactModal", {static: true}) newContactModal: ModalDirective;
  @ViewChild("customerContacts", {static: true}) customerContacts: ElementRef;

  @Input() customerId: string;

  newContactForm: UntypedFormGroup;
  showDeleteConfirm = false;

  @Output() customerUpdate: EventEmitter<any> = new EventEmitter();

  constructor(
    private adminCustomerService: AdminCustomerService,
    private customerService: CustomerService,
    private eventService: EventService,
    private contactService: ContactService,
    private adminContactService: AdminContactService,
    private router: Router,
    public url: URLService,
    public userService: UserService,
    private formBuilder: UntypedFormBuilder,
    private modalService: MDBModalService,
    private logService: LoggerService) {
  }

  logger = this.logService.taggedLogger(this.constructor?.name);

  isLoading: boolean;

  public contacts: ContactDTO[];
  public temp: {} = false;
  public searchTerm: string;
  public showNewCustomer = false;
  public deleteContactId: string = null;
  public deleteRow: number = null;
  public user: User;
  public inviteContactDTO: InviteDTO;
  public roleChoices: any[];


  @ViewChildren(NgbdSortableHeader) headers: QueryList<NgbdSortableHeader>;
  isMobile = GlobalConstants.IsMobile;

  public StatusEnum = StatusEnum;
  editingContact: ContactDTO;
  showContactModalTime: any;

  async ngOnInit() {

    this.newContactForm = this.formBuilder.group({
      contactName: new UntypedFormControl('', Validators.required),
      email: new UntypedFormControl("", Validators.required),
      roles: new UntypedFormControl(null, Validators.required)
    });

    await this.userService.loadCurrentUser().then(() => {

      this.user = this.userService.CurrentUser;

    });

    this.isLoading = true;

    this.loadContacts().then(() => {
    });
    this.restoreDivSizes([this.customerContacts]);
  }

  loadContacts() {

    return this.customerService.getContacts(this.customerId, {component: 'admin-customer'}).then((result) => {

      this.contacts = result.results;
      this.temp = true;
      this.isLoading = false;
    });
  }

  showCreateContact() {

    this.newContactModal.show();
  }

  showEditContact(contactRef: ContactDTO) {

    this.editingContact = contactRef;
    this.showContactModalTime = new Date();

  }

  createContact() {

    if (!this.newContactForm.valid) {
      return;
    }

    this.inviteContactDTO = Object.assign(this.newContactForm.value);

    this.contactService.inviteContact(this.inviteContactDTO).then(() => {

      this.newContactModal.hide();
      this.loadContacts().then(() => {
      });

    });
  }

  deleteContact(event, rowId, contactId) {

    event.stopPropagation();

    this.deleteContactId = contactId;
    this.deleteRow = rowId;
    this.showDeleteConfirm = true;
  }

  confirmDelete() {

    this.contactService
      .delete(this.deleteContactId, this.user.customerId)
      .then((result) => {

        this.contacts.splice(this.deleteRow, 1);

      });
  }

  ngOnDestroy() {
  }

  cancelDelete() {
    this.showDeleteConfirm = false;
  }

  suspendContact(event, contactId: string) {
    event.stopPropagation();

    this.logger.debug("ContactId", contactId);

    this.adminContactService.suspendContact(contactId, this.customerId)
      .then(() => {
        this.loadContacts().then(() => {
        });
      });
  }

  resumeContact(event, id: string) {

    event.stopPropagation();

    this.contactService.patch(id, {statusId: StatusEnum.Active})
      .then(() => {
        this.loadContacts().then(() => {
        });
      });
  }

  storeResize($event) {

    const key = "object-size-" + $event.target.id;
    const height = $event.borderBoxSize[0].blockSize;
    const width = $event.borderBoxSize[0].inlineSize;
    const objectSize = localStorage.getItem(key) || "";
    const [w, h] = objectSize.split("x");

    if ((w !== width || h !== height) && $event.target.dataAttributes?.restoredDiv === true) {
      localStorage.setItem(key, `${width}x${height}`);
    }
  }

  private restoreDivSizes(elements: ElementRef[]) {

    elements.forEach((element) => {

      const key = "object-size-" + element.nativeElement.id;
      const objectSize = localStorage.getItem(key) || "";
      const [w, h] = objectSize.split("x");

      element.nativeElement.style.width = "100%";
      element.nativeElement.style.height = h + "px";
      element.nativeElement.dataAttributes = {restoredDiv: true};
    });
  }

  reloadContacts() {
    this.loadContacts().then(() => {
    });
  }

  togglePrimary(event, contact: ContactDTO) {

    event.preventDefault();
    event.stopPropagation();

    const howManyPrimary = this.contacts.filter(x => x.isPrimary).length;

    if (contact.isPrimary && howManyPrimary === 1) {
      return;
    }

    this.contacts.forEach((c) => {

      if (c.isPrimary) {
        this.contactService.patch(c.id, {isPrimary: false}).then(() => {
          c.isPrimary = false;
        });
      }

      if (c.id == contact.id) {
        this.contactService.patch(c.id, {isPrimary: true}).then(() => {
          c.isPrimary = true;
        });
        this.customerService.patchCustomer(c.customerId, {primaryContactId: c.id}).then(() => {

          this.adminCustomerService.saveCustomerNote(
            c.customerId,
            {
              note: "Changed Primary User to " + c.contactName,
              customerId: c.customerId,
            }
          ).then(() => {
            this.customerUpdate.emit(true);
          });
        });
      }
    });
  }
}
