import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {DatePipe} from "@angular/common";
import {CustomerAttribService, EventService, LookupService, URLService} from "../../../../services";
import {AdminCustomerService, AdminUrlService} from "../../services";
import {UntypedFormBuilder} from "@angular/forms";
import {
  AdminCustomerInternalInfoService,
  CustomerService,
  HelpersService,
  LoggerService
} from "../../../../global/services/index";
import {
  AttribEnum,
  MessageAreaEnum,
  MessageMethodEnum,
  MessageTypeEnum,
  StatusEnum,
  WhosWhoStatusEnum
} from "../../../../global/enums";
import {AdminTaskDTO, CustomerAdminDTO, CustomerAttribDTO, CustomerNoteDTO} from '../../../../global/interfaces';
import {AdminTaskService} from "../../services/admin-task.service";

@Component({
  selector: "app-customer-notes2",
  templateUrl: "./customer-notes2.component.html",
  styleUrls: ['./customer-notes2.component.scss'],
  providers: [DatePipe],
})

export class CustomerNotes2Component implements OnInit {

  // tslint:disable-next-line:variable-name
  _notesCustomer: CustomerAdminDTO = {};
  whosWhoStatusName: { [p: number]: { name: string; value: number } };
  whosWhoOptions: { label: string; value: number }[];

  @Output() statusChanged: EventEmitter<any> = new EventEmitter();

  savingNoteStatus = false;
  private originalFormStatus: { newStatusId?: null, whosWhoStatus?: null, rejectReasonId?: null } = {};

  @Input('notesCustomer') set notesCustomer(value: any) {
    this._notesCustomer = value;
    if (value) {
      this.fetchNotes(this.notesCustomer.id, "input notes customer: " + value.id);
    }
  }

  @Input() height = "130px";

  get notesCustomer(): any {
    return this._notesCustomer;
  }

  @Input() getNotes: boolean;

  statusOptions: any;
  searchStatusOptions: any;
  statusName: {} = {};
  rejectOptions: any;
  oldStatusId: any;
  oldRejectReasonId: any;
  newNoteForm: any;
  customerNotes: any[] = [];
  waitingNotes = false;

  constructor(
    private customerAttribService: CustomerAttribService,
    private datePipe: DatePipe,
    public urlService: URLService,
    public adminUrl: AdminUrlService,
    public eventService: EventService,
    private fb: UntypedFormBuilder,
    private helperService: HelpersService,
    private lookupService: LookupService,
    private customerService: CustomerService,
    private logService: LoggerService,
    private adminCustomerService: AdminCustomerService,
    private customerInternalInfoService: AdminCustomerInternalInfoService,
    private adminTaskService: AdminTaskService
  ) {
    this.whosWhoStatusName = this.helperService.getNamesAndValuesAsObject(WhosWhoStatusEnum, true);
    this.whosWhoOptions = this.helperService.getLabelsAndValues(WhosWhoStatusEnum, true);

    this.initNoteForm();
  }

  logger = this.logService.taggedLogger(this.constructor?.name);
  hideWhosWhoStatus = false;

  ngOnInit() {

    this.statusOptions = this.helperService.getLabelsAndValues(StatusEnum);
    this.searchStatusOptions = this.statusOptions;
    this.searchStatusOptions.unshift({value: 0, label: 'Select Status'});

    this.statusOptions.forEach(x => {
      this.statusName[x.value] = x.label;
    });

    this.getRejectOptions();
    this.initNoteForm();

  }

  getRejectOptions() {

    this.lookupService.getAttribvalsByAttribId(AttribEnum.customer_reject_reason, {
      component: 'AdminCustomerSearch',
      filters: {removeDeletedTask: true}
    }).then((response) => {
      this.rejectOptions = response.map(x => {
        return {value: x.id, label: x.attribvalName};
      });

      this.logger.debug("OPTIONS ", this.rejectOptions);
    });
  }

  showNotes(caller: string) {

    console.log("Show notes called from ", caller);

    if (!this.notesCustomer) {
      return;
    }

    if (this.notesCustomer.customerAttribs) {
      const rejected = this.notesCustomer.customerAttribs?.filter(x => x.attribId === AttribEnum.customer_reject_reason as number) || [];
      const rejectReasonId = rejected.length > 0 ? rejected[0].attribvalId : null;
      this.newNoteForm.patchValue(rejectReasonId);
      this.oldRejectReasonId = rejectReasonId;
    }

    this.newNoteForm.patchValue({newStatusId: this.notesCustomer.statusId});
    this.newNoteForm.patchValue({newNote: ''});
    this.oldStatusId = this.notesCustomer.statusId;

    // ???
    // this.fetchNotes(this.notesCustomer.id);

  }

  initNoteForm() {

    this.newNoteForm = this.fb.group({
      newNote: [''],
      sleepDate: [''],
      sleepTime: [''],
      reminderSet: [false],
      taskId: [null],
      newStatusId: [this.notesCustomer?.statusId],
      rejectReasonId: [this.oldRejectReasonId],
      whosWhoStatus: [this.notesCustomer?.customerInternalInfo?.whosWhoStatus?.toString() || "0"]
    });

    this.originalFormStatus = this.newNoteForm.value;
  }

  resetNoteForm() {
    this.newNoteForm.patchValue({newNote: ''});
    this.originalFormStatus = this.newNoteForm.value;
  }

  clearTask(id: string) {

    this.adminTaskService.clearTask(id).then(() => {
      this.resetNoteForm();
      this.fetchNotes(this.notesCustomer.id, "clear task");
    });
  }

  reschedule(task: AdminTaskDTO, note: CustomerNoteDTO) {

    const now = task.sleepUntil;
    const dateString = this.datePipe.transform(now, 'yyyy-MM-dd');
    const timeString = this.datePipe.transform(now, 'HH:mm:ss');

    this.newNoteForm.patchValue({
      sleepDate: dateString,
      sleepTime: timeString,
      newNote: note.note,
      reminderSet: true,
      taskId: task.id,
    });
  }

  toggleReminder() {

    const now = new Date();
    const dateString = this.datePipe.transform(now, 'yyyy-MM-dd');
    const timeString = this.datePipe.transform(now, 'HH:mm:ss');

    if (this.n.reminderSet.value === true) {
      this.newNoteForm.patchValue({sleepTime: timeString});
      this.newNoteForm.patchValue({sleepDate: dateString});
    } else {

      this.newNoteForm.patchValue({sleepDate: null, sleepTime: null});
    }
  }

  async saveNote() {

    this.savingNoteStatus = true;

    const newStatusId = this.newNoteForm.get("newStatusId").value;
    const oldStatusId = this.originalFormStatus.newStatusId;
    const newRejectReasonId = this.newNoteForm.get("rejectReasonId").value;
    const oldRejectReasonId = this.originalFormStatus.rejectReasonId;
    const newWhosWhoStatus = this.newNoteForm.get("whosWhoStatus").value;
    const oldWhosWhoStatus = this.originalFormStatus.whosWhoStatus;

    // If we've changed the WhosWhoStatus
    if (newWhosWhoStatus !== oldWhosWhoStatus) {

      await this.customerInternalInfoService
        .patchCustomer(this.notesCustomer.id, {whosWhoStatus: newWhosWhoStatus})
        .then(() => {
          this.statusChanged.emit(true);
        });

      this.adminCustomerService.saveCustomerNote(
        this.notesCustomer.id,
        {
          note: "WhosWhoStatus changed to " + this.whosWhoStatusName[newWhosWhoStatus].name,
          customerId: this.notesCustomer.id,
        }
      ).then(() => {
      });
    }

    if (newStatusId !== oldStatusId || newRejectReasonId !== oldRejectReasonId) {

      const dto = {statusId: newStatusId};
      const put: CustomerAttribDTO = {attribvalId: newRejectReasonId};

      // First we must set the reject reason on the customer
      // Then patch to say we've changed status

      if (newRejectReasonId !== oldRejectReasonId) {
        this.customerAttribService.put(this.notesCustomer.id, AttribEnum.customer_reject_reason, put).then(() => {
          this.customerService.patchCustomer(this.notesCustomer.id, dto).then(() => {
            this.statusChanged.emit(true);
          });
        });
      } else {
        // Then patch to say we've changed status
        this.customerService.patchCustomer(this.notesCustomer.id, dto).then((x) => {
          this.statusChanged.emit(true);
        });
      }

      if (oldStatusId == (StatusEnum.Pending as number) &&
        (newStatusId == StatusEnum.Active as number || newStatusId == StatusEnum.Deleted as number)) {

        this.customerInternalInfoService.patchCustomer(this.notesCustomer.id, {idPending: false}).then(() => {
          this.statusChanged.emit(true);
          this.eventService.AuctioneerEvent.emit({
            messageMethod: MessageMethodEnum.Message,
            messageArea: MessageAreaEnum.Auctioneer,
            messageType: MessageTypeEnum.IdPendingDecrement
          });
        });
      }

      this.originalFormStatus.rejectReasonId = newRejectReasonId;
      this.originalFormStatus.newStatusId = newStatusId;

      this.adminCustomerService.saveCustomerNote(
        this.notesCustomer.id,
        {
          note: "Status changed to " + this.statusName[newStatusId],
          customerId: this.notesCustomer.id,
        }
      ).then(() => {
      });
    }

    if (this.n.taskId.value != null) {

      const sleepUntil = this.n.sleepDate.value + " " + this.n.sleepTime.value;

      this.adminTaskService.patch(
        this.n.taskId.value,
        {
          sleepUntil
        }
      ).then(() => {

        this.resetNoteForm();
        this.fetchNotes(this.notesCustomer.id, "save note");
        this.savingNoteStatus = false;
      });

    } else {

      const sleepUntil2 = this.n.sleepDate.value + " " + this.n.sleepTime.value;

      this.adminCustomerService.saveCustomerNote(
        this.notesCustomer.id,
        {
          note: this.n.newNote.value,
          customerId: this.notesCustomer.id,
          setReminder: this.n.reminderSet.value,
          sleepUntil: sleepUntil2
        }
      ).then(() => {
        this.resetNoteForm();
        this.fetchNotes(this.notesCustomer.id, "save notes 3");
        this.savingNoteStatus = false;
      });
    }

  }

  get n() {
    return this.newNoteForm?.controls;
  }

  fetchNotes(id: string, from: string) {

    console.log("FETCHING NOTES FOR ", from);

    if (id != null) {

      this.customerNotes = [];
      this.waitingNotes = true;

      this.adminCustomerService.searchCustomerNotes({
        component: "AdminCustomerSearch",
        filters: {customerId: id, statusId: StatusEnum.Active}
      }).then((result: CustomerNoteDTO[]) => {
        this.waitingNotes = false;
        this.customerNotes = result;
        this.logger.info("Waiting Notes Finished");
        this.initNoteForm();
      });
    }
  }

  changeWhosWho(value: number) {

    this.newNoteForm.patchValue({whosWhoStatus: value.toString()});

  }

  needNotes() {

    return (
        this.n?.whosWhoStatus.value !== this.originalFormStatus.whosWhoStatus ||
        this.n?.newStatusId.value !== this.originalFormStatus.newStatusId)
      && this.n.newNote.value === '';
  }
}
