.status-btn {
  font-weight: 500;
}

.note-date, .note-contact {
  font-size: 10px;
  font-weight: 400;
  color: #777;
}

.note-contact {
  font-weight: 400;
  font-size: 11px;
}

.note-note {
  font-size: 12px;
  font-weight: 500;
}

div .customer-note {
  padding: 5px;
  margin-bottom: 1px;
  border-radius: 5px;
}

div .customer-note:nth-of-type(odd) {
  background-color: #f4f4f4 !important;
}

div .customer-note:nth-of-type(even) {
  background-color: #eee !important;
}

.sleep-until {
  font-size: 11px;
  padding-right: 35px;
}

.btn-1 {
  background-color: var(--successColour) !important;
  color: #fff !important;
}

.btn-2 {
  background-color: #d4af37 !important;
  color: #fff !important;
}

.btn-3 {
  background-color: #c00 !important;
  color: #fff !important;
}

.btn-4 {
  background-color: rgb(120, 120, 120) !important;
  color: #fff !important;
}


.role-info {
  font-size: 0.6rem;
  background-color: #ddd;
  font-weight: 600;
  padding: 3px 5px;
  border-radius: 5px;
  margin-right: 5px;
}

.seller {
  background-color: #437381 !important;
  color: white;
}

.buyer {
  background-color: #734381 !important;
  color: white;
}

.sticky-column {

  --first-column-width: 70px;
  --last-column-width: 80px;
}

.sticky-column {

  overflow-x: scroll;

  &.sticky-first-column tr {
    th:first-child, td:first-child {
      position: sticky;
      left: 0;
      z-index: 2;
      min-width: var(--first-column-width);
    }
  }

  &.sticky-last-column tr {
    th:last-child, td:last-child {
      position: sticky;
      right: 0;
      z-index: 2;
      min-width: var(--last-column-width);
    }
  }

  tr th:first-child, tr td:first-child {
    position: sticky;
    left: 0;
    z-index: 2;
  }

  thead th {
    background-color: #fff;
  }

  th:not(:first-child):not(:last-child), td:not(:first-child):not(:last-child) {
    // min-width: 180px !important;
  }

  tbody {
    tr:nth-of-type(odd) td {
      background-color: var(--tableStripedBackgroundColour) !important;
    }

    tr:nth-of-type(even) td {
      background-color: #fff !important;
    }
  }
}

.note-icon {
  font-size: 1.3rem;
}

.badge2 {
  border-radius: 20px;
  min-width: 20px !important;
  min-height: 20px !important;
  line-height: 20px;
  color: #fff !important;
  font-size: 0.7rem;
  display: inline-block;
  text-align: center;
  vertical-align: middle;
  font-weight: bold;
}

.liveBadge {
  background-color: var(--primaryButtonColour);
  color: #fff !important;
}

.totalBadge {
  background-color: var(--secondaryButtonColour);
}



