<div class="d-flex flex-wrap grid-gap-10 mt-3" style="align-items: center">
  <div class="flex-grow-1">
    <h1 class="page-header">Deals Agreed</h1>
  </div>
  <div class="flex-shrink-1">
    <form [formGroup]="filterForm">
      <div class="d-flex flex-wrap grid-gap-10">
        <div class="flex-shrink-1">
          <div class="input-group input-group-sm">
            <div class="input-group-prepend">
              <div class="input-group-text">
                Show
              </div>
            </div>

            <div class="select select-sm">
              <mdb-select-2 [outline]="true"
                            style="width: 150px"
                            formControlName="completeState">
                <mdb-select-option *ngFor="let option of statuses"
                                   value="{{ option.value }}">{{ option.label }}
                </mdb-select-option>
              </mdb-select-2>
            </div>
          </div>
        </div>

        <div class="flex-shrink-1">
          <div class="select select-sm">
            <mdb-select-2 [outline]="true"
                          style="width: 150px"
                          formControlName="dateRangeId">
              <mdb-select-option *ngFor="let option of dateRange"
                                 value="{{ option.value }}">{{ option.label }}
              </mdb-select-option>
            </mdb-select-2>
          </div>
        </div>

        <div class="flex-shrink-1">
          <div class="input-group input-group-sm">
            <div class="input-group-item">
              <input class="form-control background-input"
                     placeholder="Vehicle"
                     formControlName="keywords">
            </div>
            <div class="input-group-append">
              <div class="input-group-text">
                <i class="las la-search"></i>
              </div>
            </div>
          </div>
        </div>

        <div class="flex-shrink-1">
          <div style="flex-shrink: 1" class="btn btn-sm btn-secondary" (click)="filterToMe()"
               mdbTooltip="Customers assigned to me"><i
            [class]="f.assigneeId == currentUser?.contactId ? 'fas fa-user-tie' : 'fa fa-users'"></i>
          </div>
        </div>

        <div class="flex-shrink-1">
          <div class="select select-sm no-margin" style="min-width: 200px; flex-grow: 1">
            <mdb-select-2 [outline]="true"
                          formControlName="assigneeId"
                          class="white-background"
                          placeholder="Select Assigned">
              <mdb-select-option [value]="EMPTY_GUID">All Assignees</mdb-select-option>
              <mdb-select-option *ngFor="let ato of assignedOptions" [value]="ato.value">{{ ato.label }}
              </mdb-select-option>
            </mdb-select-2>
          </div>
        </div>
      </div>
    </form>


  </div>
</div>

<div class="widget mt-3">
  <div style="max-height: 100%; overflow-y: auto;">
    <table
      class="table table-striped table-hover table-condensed deals-done" mdbTable
      mdbTableScroll
      scrollX="true">
      <thead>
      <tr>
        <th>Deal</th>
        <th>Vehicle</th>
        <th>Amount</th>
        <th>Seller</th>
        <th>Seller Contact</th>
        <th>Seller Assignee</th>
        <th>Buyer</th>
        <th>Buyer Contact</th>
        <th>Buyer Assignee</th>
        <th>Completed</th>
        <th>&nbsp;</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let item of deals | paginate: {
              itemsPerPage: pageSize,
              currentPage: page,
              totalItems: count
            }"
      >
        <!--
        (click)="showBidModal(item.advertId);"
        -->
        <td class="shrink-cell">
          {{ item.added | datex: 'Do MMM' }}
        </td>
        <td (click)="adminUrlService.adminManageNegotiations(item.advertId); $event.stopPropagation();">
          <div class="table-line-1">{{ item?.advert?.vehicle?.vrm }}</div>
          <div
            class="table-line-2">{{ item?.advert?.vehicle?.make?.makeName }} {{ item?.advert?.vehicle?.model?.modelName }}
          </div>
        </td>
        <td class="price">{{ item.bidAmt | customCurrency: null:null: '1.0-0' }}</td>
        <td>
          <div class="table-line-1">{{ item?.sellerContact.customer.customerName }}</div>
          <div class="table-line-2">{{ item?.sellerContact.customer.email }}</div>
        </td>
        <td>
          <div class="table-line-1">{{ item?.sellerContact?.contactName }}</div>
          <div class="table-line-2">{{ item?.sellerContact?.email }}</div>
        </td>
        <td>
          <div class="table-line-1"><span
            class="assigned-to btn btn-xs">{{ getAssigneeName(item?.sellerAssignee) || "Unassigned" }}</span></div>
        </td>
        <td class="contact-cell">
          <div class="table-line-1">{{ item?.buyerContact?.customer.customerName }}</div>
          <div class="table-line-2">{{ item?.buyerContact?.phone1 }}</div>
        </td>
        <td class="contact-cell">
          <div class="table-line-1">{{ item?.buyerContact?.contactName }}</div>
          <div class="table-line-2">{{ item?.buyerContact?.email }}</div>
        </td>
        <td class="contact-cell">
          <div class="table-line-1"><span
            class="assigned-to btn btn-xs">{{ getAssigneeName(item?.buyerAssignee) || "Unassigned" }}</span></div>
        </td>
        <td class="contact-cell">
          <div class="table-line-1">{{ item?.completed ? 'Yes' : 'No' }}</div>
        </td>
        <td class="shrink-cell">
          <div>
            <app-book-movement-button [advertId]="item.advertId"></app-book-movement-button>
          </div>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</div>
<div class="row">
  <div class="col-6" style="padding-top: 15px; margin-top: 2px; padding-left: 20px; font-size: 13px;">
    Results per Page:
    <select (change)="handlePageSizeChange($event)">
      <option *ngFor="let size of pageSizes" [ngValue]="size">
        {{ size }}
      </option>
    </select>
  </div>

  <div class="col-6" style="text-align: end; padding-top: 15px;">
    <pagination-controls
      (pageChange)="onTableDataChange($event)"
      class="paginator"
      nextLabel="Next"
      previousLabel="Prev"
      responsive="true">
    </pagination-controls>
  </div>
</div>

<div mdbModal #bidHistoryModal="mdbModal" class="modal fade"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true" [config]="{backdrop: 'static'}">

  <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">

        <button type="button" class="close pull-right" aria-label="Close" (click)="bidHistoryModal.hide()"><span
          aria-hidden="true">×</span></button>
        <div>Bid History</div>
      </div>

      <div class="modal-body">
        <table class="table table-striped w-100 table-compressed">
          <thead>
          <tr>
            <th>Seq</th>
            <th>Time</th>
            <th>Bidder</th>
            <th>Amount</th>
            <th>Status</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let bid of bids">
            <td>{{ bid.id }}</td>
            <td>{{ bid.added | date: 'dd MMM YYYY HH:mm:ss' }}</td>
            <td>{{ bid?.contact?.contactName }}</td>
            <td>{{ bid.bidAmt | customCurrency: null:null: '1.2' }}</td>
            <td>{{ bidStatusName[bid.bidStatus] }}</td>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
