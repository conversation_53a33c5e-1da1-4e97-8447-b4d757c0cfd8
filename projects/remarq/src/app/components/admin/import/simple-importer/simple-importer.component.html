<div class="import-container">
  <h1 class="page-header">Import Data for: {{ vendorName }}</h1>
  <form (ngSubmit)="onSubmit()" #importForm="ngForm">
    <div class="form-group" style="display: flex; grid-gap: 10px">
      <div>
        <input
          type="file"
          id="fileInput"
          class="form-control"
          (change)="onFileSelected($event)"
          required
        />
      </div>
      <div style="flex-shrink: 1; align-content: end">
        <button
          type="submit"
          class="btn btn-sm btn-primary"
          [disabled]="!selectedFile || isLoading"
        >
          {{ isLoading ? "Processing..." : "Submit" }}
        </button>
      </div>
    </div>
  </form>

  <div *ngIf="errorMessage" class="alert alert-danger mt-3">
    {{ errorMessage }}
  </div>
  <div *ngIf="successMessage" class="alert alert-success mt-3">
    {{ successMessage }}
  </div>

  <div *ngIf="resultingDtos.length > 0" class="result-container mt-4">
    <h3>Processed Data:</h3>
    <div style="max-height: 550px; overflow-y: auto">
      <table class="table table-bordered">
        <thead>
        <tr>
          <th *ngFor="let key of dtoHeaders">{{ key }}</th>
          <th>Result</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let dto of resultingDtos">
          <td *ngFor="let key of dtoHeaders">{{ dto[key] }}</td>
          <td *ngIf="!dto.importing">{{ dto.message }}</td>
          <td *ngIf="dto.importing"><i class="fa fa-spin fa-spinner"></i></td>
        </tr>
        </tbody>
      </table>
    </div>

    <div style="width: 100%; text-align: left">
      <button class="btn btn-primary" style="margin-top: 10px; margin-bottom: 10px"
              [disabled]="isImporting" (click)="importData()"
      >
        {{ isImporting ? "Importing..." : "Create Adverts" }}
      </button>
    </div>
  </div>
</div>
