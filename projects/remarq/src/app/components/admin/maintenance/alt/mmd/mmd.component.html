<h1 id="top" class="page-header">Alts</h1>

<div class="mb-2">
  Pending:
  <span *ngFor="let pending of pendings">
    <span class="link-style" style="font-weight: 500"
          (click)="refreshTables(pending.altTable, pending.vehicleTypeId, true)">
      {{ pending.altTable }}
      <span *ngIf="pending.vehicleTypeId">({{ vehicleTypeName[pending.vehicleTypeId] }})</span>
      : {{ pending.count }} </span> |
  </span>

  <span class="link-style" (click)="autofixParent()">AutoFixParentId</span>
</div>

<div class="full-width">

  <div class="left-bar">
    <table class="sidebar table table-striped">
      <thead>
      <tr>
        <th>ID</th>
        <th>Type</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let vehicleType of vehicleTypes"
          [class]="vehicleTypeId != null && vehicleTypeId == vehicleType.id ? 'row-selected' : ''">
        <td>{{ vehicleType.id}}</td>
        <td><span class="link-style"
                  (click)="selectVehicleTypeId(vehicleType.id)">{{ vehicleType.vehicleTypeName}}</span></td>
      </tr>
      <tr>
        <td>-</td>
        <td><span class="link-style"
                  (click)="refreshTables('make', vehicleTypeId, false)">Make/Model ({{ vehicleTypeName[vehicleTypeId] }}
          )</span></td>
      </tr>
      <tr>
        <td>-</td>
        <td><span class="link-style"
                  (click)="refreshTables('body_type', vehicleTypeId, false)">BodyType ({{ vehicleTypeName[vehicleTypeId] }}
          )</span></td>
      </tr>
      <tr>
        <td>-</td>
        <td><span class="link-style"
                  (click)="refreshTables('fuel_type', vehicleTypeId, false)">FuelType ({{ vehicleTypeName[vehicleTypeId] }}
          )</span></td>
      </tr>
      <tr>
        <td>-</td>
        <td><span class="link-style"
                  (click)="refreshTables('transmission_type', vehicleTypeId, false)">Transmission ({{ vehicleTypeName[vehicleTypeId] }}
          )</span></td>
      </tr>
      <tr>
        <td>-</td>
        <td><span class="link-style" (click)="refreshTables('colour', vehicleTypeId)">Colour</span></td>
      </tr>
      </tbody>
    </table>

  </div>

  <div class="main-section "
       *ngIf="showTable == 'transmission_type' || showTable == 'body_type' || showTable == 'colour' || showTable == 'fuel_type'">
    <table class="mmd-table table table-striped">
      <thead>
      <tr>
        <th>ID</th>
        <th>V.Type</th>
        <th>Value</th>
        <th>Alts</th>
        <th>Merge</th>
        <th>Action</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let record of records" class="status-{{ record.statusId }}">
        <td>{{ record.id }}</td>
        <td>{{ vehicleTypeName[record.vehicleTypeId] }}</td>
        <td style="position: relative">
          <input class="form-control" [(ngModel)]="recordValue[record.id]"
                 (change)="recordNameChanged(showTable, record.id)" style="width: 250px;">
          <span *ngIf="record.id == recordNameSaving" class="please-wait"><i class="fa fa-spin fa-spinner"></i></span>
        </td>
        <td><span class="btn btn-xs btn-secondary" style="display: inline-block; min-width: 30px; text-align: center;"
                  (click)="showAltsForRealId(showTable, record.value, record.id)">{{ record.alts.length }}</span></td>
        <td>
          <input placeholder="Merge" class="form-control merge-cell" [(ngModel)]="recordMergeTo[record.id]">
        </td>
        <td>
          <button *ngIf="! recordMergeTo[record.id] && record.statusId == statusEnum.Pending"
                  class="btn btn-xs btn-primary" (click)="approveRecord(showTable, record.id, record.vehicleTypeId)">
            Accept
          </button>
          <button *ngIf="recordMergeTo[record.id]" class="btn btn-xs btn-primary"
                  (click)="mergeRecord(showTable, record.vehicleTypeId, record.id)">Merge
          </button>
          <button *ngIf="record.statusId == statusEnum.Pending"
                  (click)="deleteType(showTable, record.id)"
                  class="ml-2 btn btn-xs btn-danger">
            <i class="fa fa-trash-alt"></i>
          </button>
        </td>
      </tr>
      </tbody>
    </table>
  </div>

  <div class="main-section" *ngIf="showTable == 'make' || showTable == 'model' || showTable == 'deriv'">
    <table>
      <tbody>
      <tr>
        <td *ngIf="vehicleTypeId" valign="top">
          <div>
            <table class="mmd-table table table-striped">
              <thead>
              <tr>
                <th>ID</th>
                <th>Make</th>
                <th>Alts</th>
                <th style="width: 60px;">Merge</th>
              </tr>

              </thead>
              <tbody>
              <tr *ngIf="havePendingMakes">
                <td colspan="2">&nbsp;</td>
                <td class="text-right">
                  <button class="btn btn-xs btn-primary" (click)="approveRecords('make')">Approve</button>
                </td>
              </tr>
              <tr *ngFor="let make of makes"
                  [class]="make.statusId == statusEnum.Pending ? 'row-pending' : (makeId != null && makeId == make.id ? 'row-selected' : '')">
                <td><span class="link-style" (click)="selectMakeId(make.id)">{{ make.id}}</span></td>
                <td nowrap style="position: relative;" class="md-form input-xs">
                  <input [(ngModel)]="makeName[make.id]"
                         (change)="recordNameChanged('make',make.id)"
                         class="form-control mmd-value col-1 mb-0">
                  <span *ngIf="recordNameSaving == make.id" class="please-wait"><i
                    class="fa fa-spin fa-spinner"></i></span>
                </td>
                <td>
                  <span class="btn btn-xs btn-secondary"
                        style="display: inline-block; min-width: 30px; text-align: center;"
                        (click)="showAltsForRealId('make', make.value, make.id)">{{ make.alts.length }}</span>

                  <input class="opacity-100 pending-checkbox" type="checkbox"
                         [(ngModel)]="makeStatus[make.id]"
                         *ngIf="make.statusId == statusEnum.Pending">
                </td>
                <td nowrap>


                  <input class="form-control merge-cell text-center" placeholder="Merge"
                         [(ngModel)]="mergeMake[make.id]">

                  <button
                    (click)="mergeRecord('make', make.vehicleTypeId, make.id)"
                    class="btn btn-xs btn-primary" *ngIf="mergeMake && mergeMake[make.id]">Merge
                  </button>


                  <button *ngIf="make.statusId == statusEnum.Pending"
                          (click)="deleteAlt('make',make.id, null, null)"
                          class="ml-2 btn btn-xs btn-danger">
                    <i class="fa fa-trash-alt"></i>
                  </button>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </td>

        <td *ngIf="makeId" valign="top">
          <div style="padding-left: 20px;">

            <table class="mmd-table table table-striped">
              <thead>
              <tr>
                <th>ID</th>
                <th>Model</th>
                <th>Alts</th>
                <th width="40">Merge</th>
              </tr>

              </thead>
              <tbody>
              <tr *ngIf="havePendingModels">
                <td colspan="2">&nbsp;</td>
                <td class="text-right">
                  <button class="btn btn-xs btn-primary" (click)="approveRecords('model')">Approve</button>
                </td>
              </tr>
              <tr *ngFor="let model of models"
                  [class]="model.statusId == statusEnum.Pending ? 'row-pending' : (modelId != null && modelId == model.id ? 'row-selected' : '')">
                <td><span class="link-style" (click)="selectModelId(model.id)">{{ model.id}}</span></td>
                <td nowrap style="position: relative;" class="md-form input-xs">
                  <input [(ngModel)]="modelName[model.id]" (change)="recordNameChanged('model',model.id)"
                                                              class="form-control mmd-value col-2 mb-0">
                  <span *ngIf="recordNameSaving == model.id" class="please-wait"><i
                    class="fa fa-spin fa-spinner"></i></span>
                </td>
                <td>

                  <span class="btn btn-xs btn-secondary"
                        style="display: inline-block; min-width: 30px; text-align: center;"
                        (click)="showAltsForRealId('model', model.value, model.id)">{{ model.alts.length }}</span>

                  <input class="pending-checkbox opacity-100" type="checkbox"
                         [(ngModel)]="modelStatus[model.id]"
                         *ngIf="model.statusId == statusEnum.Pending">
                </td>
                <td nowrap class="md-form input-xs">

                  <input class="form-control merge-cell pl-1 text-center mb-0" placeholder="Merge"
                         [(ngModel)]="mergeModel[model.id]">

                  <button
                    (click)="mergeRecord('model',null, model.id)"
                    class="btn btn-xs btn-primary" *ngIf="mergeModel && mergeModel[model.id]">Merge
                  </button>


                  <button *ngIf="model.statusId == statusEnum.Pending"
                          (click)="deleteAlt('model',model.id, null, null)"
                          class="ml-2 btn btn-xs btn-danger">
                    <i class="fa fa-trash-alt"></i>

                  </button>

                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </td>

        <td *ngIf="modelId" valign="top">
          <div style="padding-left: 20px;">
            <table class="mmd-table table table-striped">
              <thead>
              <tr>
                <th>ID</th>
                <th>Deriv</th>
                <th>Alts</th>
                <th width="40">Merge</th>
              </tr>

              </thead>
              <tbody>
              <tr *ngIf="havePendingDerivs">
                <td colspan="2">&nbsp;</td>
                <td class="text-right">
                  <button class="btn btn-xs btn-primary" (click)="approveRecords('deriv')">Approve</button>
                </td>
                <td>
                  <div><input class="pending-checkbox opacity-100" [(ngModel)]="toggleDerivs" type="checkbox"
                              (click)="toggleCheckbox('deriv')">
                  </div>
                </td>
              </tr>
              <tr *ngFor="let deriv of derivs"
                  [class]="deriv.statusId == statusEnum.Pending ? 'row-pending' : (derivId != null && derivId == deriv.id ? 'row-selected' : '')">
                <td><span class="link-style" (click)="selectDerivId(deriv.id)">{{ deriv.id}}</span></td>
                <td nowrap style="position: relative;" class="md-form input-xs">
                  <input
                  [(ngModel)]="derivName[deriv.id]"
                  (change)="recordNameChanged('deriv',deriv.id)"
                  class="form-control mmd-value col-3 mb-0">
                  <span *ngIf="recordNameSaving == deriv.id" class="please-wait"><i
                    class="fa fa-spin fa-spinner"></i></span>
                </td>
                <td>
                  <span class="btn btn-xs btn-secondary" style="display: inline-block; min-width: 30px; text-align: center;"
                        (click)="showAltsForRealId('deriv', deriv.value, deriv.id)">{{ deriv.alts.length }}</span>

                  <input class="opacity-100 pending-checkbox" type="checkbox" [(ngModel)]="derivStatus[deriv.id]" *ngIf="deriv.statusId == statusEnum.Pending">
                </td>
                <td nowrap class="input-xs md-form">


                  <input class="form-control merge-cell text-center mb-0" placeholder="Merge"
                         [(ngModel)]="mergeDeriv[deriv.id]">


                  <button
                    (click)="mergeRecord('deriv',null, deriv.id)"
                    class="btn btn-xs btn-primary" *ngIf="mergeDeriv && mergeDeriv[deriv.id]">Merge
                  </button>

                  <button *ngIf="deriv.statusId == statusEnum.Pending"
                          (click)="deleteAlt('deriv', deriv.id, null, null)"
                          class="ml-2 btn btn-xs btn-danger">
                    <i class="fa fa-trash-alt"></i>

                  </button>

                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </td>
      </tr>
      </tbody>
    </table>
  </div>

</div>

<div mdbModal #altModal="mdbModal" class="modal fade" tabindex="-1"

     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        Alts
      </div>
      <div class="modal-body">
        <table class="table table-striped table-sm">
          <thead>
          <tr>
            <th>AltID</th>
            <th>Alt Val</th>
            <th>Action</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let altItem of altList" [hidden]="altItem.hidden">
            <td>{{ altItem.id}}</td>
            <td>{{ altItem.altVal}}</td>
            <td>
              <button *ngIf="altItem.altVal.localeCompare(parentValue, 'en', { sensitivity: 'base'})" class="btn btn-xs"
                      (click)="altItem.hidden = true; removeAlt(altItem);"><i class="fa fa-trash-alt"></i></button>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<app-delete-confirm-modal
  [showDialog]="showDeleteAlt"
  (modalClosed)="showDeleteAlt = false"
  (confirmDelete)="confirmDeleteAlt()"
  (cancelDelete)="showDeleteAlt = false"></app-delete-confirm-modal>
