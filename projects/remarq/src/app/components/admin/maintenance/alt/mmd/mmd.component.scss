.mmd-table, .mmd-table td, .mmd-table th {

  padding: 0.25rem !important;
  font-size: 0.85rem;

  .form-control {

    font-size: 0.85rem;
    padding: 2px;
    line-height: 1.2rem;
    height: auto;
    font-weight: 500;
  }
}

.full-width {
  display: flex;
  flex-wrap: nowrap;
  width: 100%;
  padding-bottom: 100px;
}

.opacity-100 {
  opacity: 1;

}

.left-bar {
  flex: 0 0 200px
}

.sidebar td, .sidebar th {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem !important
}

.mmd-table .col-1 {
  min-width: 250px;
}

.mmd-table .col-2 {
  min-width: 170px;
}

.mmd-table .col-3 {
  min-width: 370px;
}

.mmd-table tr.row-selected {
  background-color: lightgreen !important;
}


.mmd-table tr.row-pending, .mmd-table tr.row-pending.row-selected {
  background-color: lightpink !important;
}

.table-striped tbody {
  tr.status-2 {
    background-color: lightblue !important;
  }

  tr.status-3 {
    background-color: lightpink !important;
  }
}

.please-wait {
  position: absolute;
  right: 15px;
  top: 7px;
}

.pending-checkbox {
  width: 25px !important;
  height: 25px !important;
  vertical-align: middle;
  margin-left: 3px;
  pointer-events: auto;
  position: relative;
}


.merge-cell {

  display: inline-block;
  width: 50px;

  &::-webkit-input-placeholder {

    font-size: 10px;
  }
}

.main-section {
  padding-left: 40px;
}
