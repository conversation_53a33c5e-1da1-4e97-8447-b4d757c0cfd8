<div class="d-flex flex-wrap mt-1" style="align-items: center">
  <div class="flex-grow-1">
    <h1 class="page-header">Country Products</h1>
  </div>

  <div class="text-right flex-shrink-1">
    <button class="btn btn-xs btn-primary" (click)="addCountryProduct()">
      <mdb-icon fas icon="plus-circle"></mdb-icon>
      Product
    </button>
  </div>
</div>

<div *ngIf="isLoading" class="mt-3 text-center">
  <app-loading-spinner loadingName="Country Products"></app-loading-spinner>
</div>

<div *ngIf="!isLoading" class="widget widget-border mt-1 mb-3" id="locations-widget">

  <table class="table table-striped table-hover table-condensed" mdbTable>
    <thead>
    <tr>
      <th>#</th>
      <th>Country</th>
      <th>Product</th>
      <th>Platform</th>
      <th>Default Tax</th>
      <th>Updated</th>
      <th>Added</th>
      <th>Status</th>
      <th>&nbsp;</th>
    </tr>
    </thead>
    <tbody>
    <tr *ngFor="let countryProduct of countryProducts; let i = index" class="" style="cursor: pointer"
        (click)="editCountryProduct($event, countryProduct)">
      <td class="shrink-cell">
        <div mdbTooltip="{{ countryProduct.id }}"
             style="width: 250px; white-space: nowrap; overflow-x: hidden; text-overflow: ellipsis;">{{ countryProduct.id }}
        </div>
      </td>
      <td class="row-content">{{ countryName[countryProduct.countryId] }}</td>
      <td class="row-content">{{ productName[countryProduct.productId] }}</td>
      <td class="row-content">{{ platformName[countryProduct.platformId] }}</td>
      <td class="row-content">{{ taxName[countryProduct.taxId] }}</td>
      <td class="row-content">{{ countryProduct.updated | date: "dd/MM/yy HH:mm" }}</td>
      <td class="row-content">{{ countryProduct.added | date: "dd/MM/yy HH:mm" }}</td>
      <td class="row-content">
        <button class="btn btn-xs status status-{{ countryProduct.statusId }}">{{ statusName[countryProduct.statusId] }}</button>
      </td>
      <td class="shrink-cell" style="vertical-align: middle">
        <button class="btn btn-xs btn-danger-outline" (click)="deleteCountryProduct($event, countryProduct)">Delete</button>
      </td>
    </tr>
    </tbody>
  </table>
</div>

<!-- modal for new vehicle -->
<div mdbModal #countryProductModal="mdbModal" class="modal fade" tabindex="-1"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">

        <div *ngIf="!isEdit">Adding new Country Product</div>
        <div *ngIf="isEdit">Editing Country Product</div>

      </div>
      <form [formGroup]="form" (ngSubmit)="saveCountryProduct()">
        <div class="modal-body">

          <input type="hidden" name="id" formControlName="id">

          <div class="select">

            <mdb-select-2 [outline]="true"
                          label="Product"
                          formControlName="productId"
                          placeholder="Select Product">
              <mdb-select-option *ngFor="let product of productOptions" [value]="product.value">
                {{ product.label }}
              </mdb-select-option>
            </mdb-select-2>
          </div>

          <div class="select mt-3">

            <mdb-select-2 [outline]="true"
                          label="Platform"
                          formControlName="platformId"
                          placeholder="Select Platform">
              <mdb-select-option *ngFor="let platform of platformOptions" [value]="platform.value">
                {{ platform.label }}
              </mdb-select-option>
            </mdb-select-2>
          </div>

          <div class="select mt-3">
            <mdb-select-2 [outline]="true"
                          label="Country"
                          formControlName="countryId"
                          placeholder="Select Country">
              <mdb-select-option *ngFor="let country of countryOptions" [value]="country.value">
                {{ country.label }}
              </mdb-select-option>
            </mdb-select-2>
          </div>

          <div class="select mt-3">
            <mdb-select-2 [outline]="true"
                          label="Default Tax"
                          formControlName="taxId"
                          placeholder="Select Default Tax">
              <mdb-select-option *ngFor="let tax of taxOptions" [value]="tax.value">
                {{ tax.label }}
              </mdb-select-option>
            </mdb-select-2>
          </div>

          <div class="select mt-3">
            <mdb-select-2 [outline]="true"
                          label="Status"
                          formControlName="statusId"
                          placeholder="Select Status">
              <mdb-select-option *ngFor="let status of statusOptions" [value]="status.value">
                {{ status.label }}
              </mdb-select-option>
            </mdb-select-2>
          </div>

        </div>
        <div class="text-right modal-footer">
          <button class="btn btn-tertiary-outline ml-2" type="button" (click)="hideModal()">Cancel</button>
          <button class="btn btn-tertiary ml-2" style="min-width: 80px;" type="submit">
            <span *ngIf="isEdit">Update</span>
            <span *ngIf="!isEdit">Add</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<app-delete-confirm-modal
  [showDialog]="showDeleteConfirm"
  (modalClosed)="showDeleteConfirm = false"
  (confirmDelete)="confirmDelete()"
  (cancelDelete)="cancelDelete()"></app-delete-confirm-modal>
