<div class="mt-2">
  <div class="d-flex flex-wrap" style="align-items: center">
    <div class="flex-grow-1">
      <h1 class="page-header">Fault Categories</h1>
    </div>
    <div class="flex-shrink-1">
      <button class="btn btn-xs btn-primary" (click)="addCategory()"><i class="fa fa-plus-circle"></i> Add</button>
    </div>
  </div>

  <div class="widget">
    <table class="table table-striped table-condensed" mdbTable>
      <thead>
      <th style="min-width: 180px;">Category</th>
      <th>Allow Claims</th>
      <th>Sequence</th>
      <th>Checks</th>
      <th>Status</th>
      <th></th>
      </thead>
      <tbody>
      <tr *ngFor="let category of faultCategories">
        <td class="valign-middle" (click)="editCategory($event, category)"
            [class.text-danger]="category.statusId == statusEnum.Deleted">
          <div class="table-line-1">{{ category.name }}</div>
          <div class="table-line-2">{{ category.description }}</div>
        </td>
        <td class="valign-middle"><i class="fa" [class.fa-check]="category.allowClaims"
                                     [class.fa-times]="!category.allowClaims"></i></td>
        <td class="valign-middle">{{ category.sequence }}</td>
        <td class="valign-middle">
          <div *ngIf="openChecklist != category.id" class="check-list"
               (click)="openChecklist = category.id">{{ checkList(category) }}
          </div>
          <div *ngIf="openChecklist == category.id" (click)="openChecklist = 0"
               [innerHTML]="checkList(category,'br')"></div>
        </td>
        <td class="valign-middle" [class.text-danger]="category.statusId == statusEnum.Deleted">
          <button class="btn btn-xs status status-{{ category.statusId }}">{{ statusName[category.statusId] }}</button></td>
        <td class="valign-middle" (click)="deleteCategory($event, category)">
          <button class="btn btn-xs btn-danger-outline">Delete</button>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</div>

<div mdbModal #editModal="mdbModal" class="modal fade" tabindex="-1"
     [config]="{ignoreBackdropClick: true}"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">

        <div *ngIf="!isEdit">Adding Fault Category</div>
        <div *ngIf="isEdit">Editing Fault Category</div>

      </div>
      <form [formGroup]="form" (ngSubmit)="submitForm()">
        <div class="modal-body">

          <input type="hidden" name="id" formControlName="id">

          <div class="md-form">
            <input mdbInput class="form-control float-label" style="font-weight: 500;"
                   type="text"
                   #name formControlName="name" id="name"/>
            <label for="name">Category Name</label>
          </div>

          <div class="md-form">
            <input mdbInput class="form-control float-label" style="font-weight: 500;"
                   type="text"
                   #description formControlName="description" id="description"/>
            <label for="description">Description</label>
          </div>

          <div class="md-form">
            <input mdbInput class="form-control float-label" style="font-weight: 500;"
                   type="text"
                   #sequence formControlName="sequence" id="sequence"/>
            <label for="sequence">Sequence</label>
          </div>


          <div>
            <ng-toggle formControlName="allowClaims"></ng-toggle>
            <div class="toggle-label">Allow Claims</div>
          </div>

          <div class="mt-3 select" *ngIf="statusOptions.length > 0">

            <mdb-select-2 [outline]="true"
                          formControlName="statusId"
                          [label]="'Status'"
                          placeholder="Select Status">
              <mdb-select-option *ngFor="let status of statusOptions" [value]="status.value">
                {{ status.label }}
              </mdb-select-option>
            </mdb-select-2>
          </div>
        </div>
        <div class="modal-footer text-right">
          <button class="btn btn-tertiary-outline ml-2" type="button" (click)="hideModal()">Cancel</button>
          <button class="btn btn-tertiary ml-2" style="min-width: 80px;" type="submit">
            <span *ngIf="isEdit">Update</span>
            <span *ngIf="!isEdit">Add</span>
          </button>

        </div>
      </form>
    </div>
  </div>
</div>

<app-delete-confirm-modal
  [showDialog]="showDeleteConfirm"
  (modalClosed)="showDeleteConfirm = false"
  (confirmDelete)="confirmDelete()"
  (cancelDelete)="cancelDelete()"></app-delete-confirm-modal>
