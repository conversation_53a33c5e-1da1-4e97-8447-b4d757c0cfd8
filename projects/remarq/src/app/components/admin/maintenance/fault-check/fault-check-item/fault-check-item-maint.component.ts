import {Component, OnInit, ViewChild} from '@angular/core';
import {UntypedForm<PERSON><PERSON>er, UntypedFormControl, UntypedFormGroup} from '@angular/forms';
import {FaultCategoryDTO, FaultItemDTO} from "../../../../../global/interfaces";
import {MechanicalFaultsService} from "../../../../../services/index";
import {HelpersService} from "../../../../../global/services";
import {AdminFaultCheckService} from "../../../services/index";
import {StatusEnum} from '../../../../../global/enums';
import {compare} from "fast-json-patch";
import {ModalDirective} from "ng-uikit-pro-standard";

@Component({
  selector: 'app-fault-check-item-maint',
  templateUrl: './fault-check-item-maint.component.html',
  styleUrls: ['./fault-check-item-maint.component.scss'],
  providers: []
})
export class FaultCheckItemMaintComponent implements OnInit {

  isEdit: boolean;
  readonly StatusEnum = StatusEnum;
  statusName: {} = {};
  form: UntypedFormGroup;
  statusOptions: any;
  showDeleteConfirm: boolean;
  faultItems: FaultItemDTO[] = [];
  private beforeEdit: any;
  @ViewChild("editModal") editModal: ModalDirective;
  updateWait: boolean;
  submitted: boolean;
  faultItemIdToDelete: number;
  faultCategories: { value: number; label: string }[] = [];
  categoryName: {} = {};
  filterCategoryId: number = null;
  filterCategories: { value?: number; label?: string }[] = [];

  constructor(
    private formBuilder: UntypedFormBuilder,
    private faultCheckService: MechanicalFaultsService,
    private helpersService: HelpersService,
    private adminFaultCheckService: AdminFaultCheckService) {
  }

  async ngOnInit() {
    this.loadFaultItems();
    this.loadStatuses();
    this.loadFaultCategories();
    this.initForm();
  }

  async loadStatuses() {
    // create status options from enum
    const statuses = this.helpersService.getNamesAndValues(StatusEnum);

    this.statusOptions = statuses.map(status => {
      this.statusName[status.value] = status.name;
      return {label: status.name, value: status.value};
    });
  }

  async loadFaultCategories() {

    this.faultCheckService.searchCategories({component: "fault-check-item-maint"}).then(res => {
      this.faultCategories = res.results.map(category => {
        this.categoryName[category.id] = category.name;
        return {label: category.name, value: category.id};
      });

      this.filterCategories = [{label: "All Categories", value: null}, ...this.faultCategories];
    });
  }

  async loadFaultItems() {
    this.faultCheckService.searchFaultItems({component: "fault-check-item-maint"}).then(res => {
      this.faultItems = res.results;
    });
  }

  initForm() {
    this.form = this.formBuilder.group({
      id: new UntypedFormControl(''),
      name: new UntypedFormControl(''),
      description: new UntypedFormControl(''),
      sequence: new UntypedFormControl(''),
      faultCategoryId: new UntypedFormControl(''),
      statusId: new UntypedFormControl(''),
    });
  }

  submitForm() {
    if (this.form.valid) {
      this.saveFaultItem();
    } else {
      return false;
    }
  }

  hideModal() {
    this.editModal.hide();
  }

  async confirmDelete() {

    this.showDeleteConfirm = false;

    console.log("CONFIRMED ", this.faultItemIdToDelete);

    await this.adminFaultCheckService.deleteFaultItem(this.faultItemIdToDelete);

    await this.loadFaultItems();
  }

  cancelDelete() {
    this.showDeleteConfirm = false;
  }

  addFaultItem() {
    this.form.reset();
    this.form.patchValue({statusId: 1, faultCategoryId: this.filterCategoryId});
    this.isEdit = false;

    // show modal that allows location name entry
    this.editModal.show();
  }

  editFaultItem($event: MouseEvent, faultItem: FaultItemDTO) {

    this.form.patchValue(({
      ...faultItem
    }));

    this.beforeEdit = Object.assign({}, this.form.value);

    this.isEdit = true;

    // show modal that allows location name entry
    this.editModal.show();

  }

  async saveFaultItem() {

    this.submitted = true;

    if (!this.form.valid) {
      return;
    }

    const itemDTO = this.form.value;

    this.updateWait = true;

    if (this.isEdit) {
      const patch = compare(this.beforeEdit, itemDTO);
      if (patch.length > 0) {
        await this.adminFaultCheckService.patchFaultItem(itemDTO.id, patch);
      }
    } else {
      await this.adminFaultCheckService.createFaultItem(itemDTO);
    }
    this.updateWait = false;

    this.editModal.hide();

    await this.loadFaultItems();
  }

  deleteFaultItem($event: MouseEvent, status: FaultItemDTO) {

    event.stopPropagation();
    this.faultItemIdToDelete = status.id;
    this.showDeleteConfirm = true;

  }

  filteredItems(faultItems: FaultItemDTO[]) {

    return faultItems
      .filter(x => x.faultCategoryId === this.filterCategoryId || this.filterCategoryId == null)
      .sort((a, b) => a.sequence - b.sequence);
  }

}

