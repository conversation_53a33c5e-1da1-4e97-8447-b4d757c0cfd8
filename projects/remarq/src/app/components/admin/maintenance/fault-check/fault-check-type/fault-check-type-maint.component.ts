import {Component, OnInit, ViewChild} from '@angular/core';
import {UntypedForm<PERSON>uilder, UntypedFormControl, UntypedFormGroup} from '@angular/forms';
import {StatusEnum} from "../../../../../global/enums";
import {HelpersService} from "../../../../../global/services";
import {MechanicalFaultsService} from "../../../../../services/index";
import {AdminFaultCheckService} from "../../../services/index";
import {compare} from "fast-json-patch";
import {CdkDragDrop, CdkDropList, CdkDrag, moveItemInArray} from '@angular/cdk/drag-drop';
import {FaultCategoryDTO, FaultCheckTypeCategoryDTO, FaultCheckTypeDTO} from "../../../../../global/interfaces";
import {ModalDirective} from "ng-uikit-pro-standard";

@Component({
  selector: 'app-fault-check-type-maint',
  templateUrl: './fault-check-type-maint.component.html',
  styleUrls: ['./fault-check-type-maint.component.scss'],
  providers: [CdkDropList, CdkDrag],
})
export class FaultCheckTypeMaintComponent implements OnInit {

  form: UntypedFormGroup;
  isEdit: boolean;
  submitted: boolean;
  updateWait: boolean;
  showDeleteConfirm = false;
  faultCheckTypeIdToDelete = 0;
  statusOptions: { label: string; value: number }[];
  faultCheckTypes: FaultCheckTypeDTO[];
  readonly StatusEnum = StatusEnum;

  statusName: any[] = [];

  @ViewChild("editModal") editModal: ModalDirective;
  @ViewChild("assignCategoriesModal") assignCategoriesModal: ModalDirective;

  beforeEdit: any;
  showAssignCategories: boolean;
  faultCategories: FaultCategoryDTO[];
  currentCheckType: FaultCheckTypeDTO;
  includedCategories: FaultCheckTypeCategoryDTO[] = [];
  excludedCategories: FaultCategoryDTO[] = [];

  constructor(
    private formBuilder: UntypedFormBuilder,
    private helpersService: HelpersService,
    private adminFaultCheckService: AdminFaultCheckService,
    private faultCheckService: MechanicalFaultsService) {
  }

  async ngOnInit() {

    this.form = this.formBuilder.group({
      id: new UntypedFormControl(''),
      name: new UntypedFormControl(''),
      description: new UntypedFormControl(''),
      isDefault: new UntypedFormControl(''),
      statusId: new UntypedFormControl(''),
    });

    this.loadStatuses();
    this.loadCategories();
    this.loadCheckTypes();
  }

  async loadStatuses() {
    // create status options from enum
    const statuses = this.helpersService.getNamesAndValues(StatusEnum);

    this.statusOptions = statuses.map(status => {
      this.statusName[status.value] = status.name;
      return {label: status.name, value: status.value};
    });
  }

  async loadCheckTypes() {
    this.faultCheckService.searchCheckTypes({component: 'fault-check-type-maint', ignoreCache: true}).then(result => {
      this.faultCheckTypes = result.results;
    });
  }

  async loadCategories() {
    this.faultCheckService.searchCategories({component: 'fault-check-type-maint', ignoreCache: true}).then(result => {
      this.faultCategories = result.results.filter(x => x.statusId === StatusEnum.Active);
      console.log("RESULTS ", this.faultCategories);
    });
  }

  editCheckType($event: MouseEvent, faultCheckTypeDTO: FaultCheckTypeDTO) {

    this.form.patchValue(({
      ...faultCheckTypeDTO
    }));

    this.beforeEdit = Object.assign({}, this.form.value);

    this.isEdit = true;

    // show modal that allows location name entry
    this.editModal.show();

  }

  addCheckType() {
    this.form.reset();
    this.form.patchValue({statusId: 1});
    this.isEdit = false;

    // show modal that allows location name entry
    this.editModal.show();
  }

  async saveCheckType() {

    this.submitted = true;

    if (!this.form.valid) {
      return;
    }

    const faultCheckTypeDTO = this.form.value;

    this.updateWait = true;

    if (this.isEdit) {
      const patch = compare(this.beforeEdit, faultCheckTypeDTO);
      if (patch.length > 0) {
        await this.adminFaultCheckService.patchCheckType(faultCheckTypeDTO.id, patch);
      }
    } else {
      await this.adminFaultCheckService.createCheckType(faultCheckTypeDTO);
    }
    this.updateWait = false;

    this.editModal.hide();
    await this.loadCheckTypes();
  }

  hideModal() {
    this.editModal.hide();
  }

  deleteCheckType(event, faultCheckType: FaultCheckTypeDTO) {

    event.stopPropagation();
    this.faultCheckTypeIdToDelete = faultCheckType.id;
    this.showDeleteConfirm = true;
  }

  async confirmDelete() {

    this.showDeleteConfirm = false;

    console.log("CONFIRMED ", this.faultCheckTypeIdToDelete);

    await this.adminFaultCheckService.deleteCheckType(this.faultCheckTypeIdToDelete);

    await this.loadCheckTypes();
  }

  cancelDelete() {
    this.showDeleteConfirm = false;
  }

  submitForm() {

    if (this.form.valid) {
      this.saveCheckType();
    } else {
      return false;
    }
  }

  categoryNames(checkType: FaultCheckTypeDTO) {

    return checkType?.faultCheckTypeCategories
      ?.filter(x => x.statusId === StatusEnum.Active)
      .sort((a, b) => a.sequence - b.sequence)
      .map(checkTypeCategory => {
        return checkTypeCategory.faultCategory.name;
      }).join(", ");
  }

  assignCategories(checkType: FaultCheckTypeDTO) {

    this.currentCheckType = checkType;

    this.includedCategories = [...this.currentCheckType?.faultCheckTypeCategories
      .filter(x => x.statusId == StatusEnum.Active)
      .sort((a, b) => a.sequence - b.sequence)];

    this.excludedCategories = this.faultCategories?.filter(x =>
      !this.currentCheckType?.faultCheckTypeCategories?.find(y => y.faultCategory.id == x.id && y.statusId == StatusEnum.Active))
      .sort((a, b) => a.sequence - b.sequence);

    if (this.currentCheckType.faultCheckTypeCategories == null) {
      this.currentCheckType.faultCheckTypeCategories = [];
    }

    this.assignCategoriesModal.show();
  }

  removeCategoryFromType(ctc: FaultCheckTypeCategoryDTO) {

    const patch = compare({}, {statusId: StatusEnum.Deleted});

    this.adminFaultCheckService.patchCheckTypeCategory(ctc.id, patch).then((x) => {
      ctc.statusId = StatusEnum.Deleted;
      this.includedCategories = this.includedCategories.filter(y => y.faultCategoryId != ctc.faultCategoryId);
      this.excludedCategories.push(ctc.faultCategory);
      this.updateSequences();
    });

  }

  addCategoryToType(ctc: FaultCategoryDTO) {

    const newSequence = this.includedCategories.length;

    // Do we already ahve this check type category, but not active
    let existing = this.currentCheckType.faultCheckTypeCategories.find(x => x.faultCategoryId == ctc.id);

    console.log("EXISTING ", existing);

    if (existing?.id != null) {

      const patch = compare({}, {
        statusId: StatusEnum.Active,
        sequence: newSequence
      });

      this.adminFaultCheckService.patchCheckTypeCategory(existing.id, patch).then(() => {

        existing.statusId = StatusEnum.Active;
        existing.faultCategory = ctc;
        existing.sequence = newSequence;

        this.includedCategories.push(existing);
      });

    } else {

      this.adminFaultCheckService.createCheckTypeCategory({
        faultCheckTypeId: this.currentCheckType.id,
        faultCategoryId: ctc.id,
        statusId: StatusEnum.Active,
        sequence: newSequence
      }).then(result => {

        const newCheckTypeCategory: FaultCheckTypeCategoryDTO = {
          id: result.dto.id,
          faultCheckTypeId: this.currentCheckType.id,
          sequence: newSequence,
          statusId: StatusEnum.Active,
          faultCategoryId: ctc.id,
          faultCategory: {
            name: ctc.name,
            id: ctc.id
          },
        };
        this.includedCategories.push(newCheckTypeCategory);
        this.currentCheckType.faultCheckTypeCategories.push(newCheckTypeCategory);
      });
    }

    this.excludedCategories = this.excludedCategories.filter(x => x.id != ctc.id);
  }

  onDrop(event: CdkDragDrop<string[]>) {

    moveItemInArray(this.includedCategories, event.previousIndex, event.currentIndex);

    console.log("NEW SEQUENCE ", this.currentCheckType.faultCheckTypeCategories);

    this.updateSequences();
  }

  updateSequences() {

    this.includedCategories
      .filter(x => x.statusId == StatusEnum.Active)
      .forEach((x, index) => {

        const newSequence = index;

        if (newSequence != x.sequence) {

          const patch = compare({}, {
            sequence: newSequence
          });

          this.adminFaultCheckService.patchCheckTypeCategory(x.id, patch).then(() => {
            x.sequence = newSequence;
          });
        }
      });
  }
}
