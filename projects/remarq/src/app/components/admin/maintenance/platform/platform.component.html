<div class="d-flex flex-wrap mt-1" style="align-items: center">
  <div class="flex-grow-1">
    <h1 class="page-header">Platforms</h1>
  </div>

  <div class="text-right flex-shrink-1">
    <button class="btn btn-xs btn-primary" (click)="addPlatform()">
      <mdb-icon fas icon="plus-circle"></mdb-icon>
      Platform
    </button>
  </div>
</div>

<div *ngIf="isLoading" class="mt-3 text-center">
  <app-loading-spinner loadingName="Platforms"></app-loading-spinner>
</div>

<div class="widget widget-border mt-1 mb-3" id="locations-widget">

  <table class="table table-striped table-hover table-condensed" mdbTable>
    <thead>
    <tr>
      <th>#</th>
      <th>Platform Name</th>
      <th>Updated</th>
      <th>Added</th>
      <th>Status</th>
      <th></th>
    </tr>
    </thead>
    <tbody>
    <tr *ngFor="let platform of platforms; let i = index" class="" style="cursor: pointer"
        (click)="editPlatform($event, platform)">
      <td class="row-content">{{ platform.id }}</td>
      <td class="row-content">{{ platform?.platformName }}</td>
      <td class="row-content">{{ platform?.updated | date: "dd/MM/yy HH:mm" }}</td>
      <td class="row-content">{{ platform?.added | date: "dd/MM/yy HH:mm" }}</td>
      <td class="row-content">
        <button class="btn btn-xs status status-{{ platform.statusId }}">{{ statusName[platform.statusId] }}</button>
      </td>
      <td class="shrink-cell row-content">
        <button class="btn btn-xs btn-danger-outline" (click)="deletePlatform($event, platform)">Delete</button>
      </td>
    </tr>
    </tbody>
  </table>
</div>

<!-- modal for new vehicle -->
<div mdbModal #platformModal="mdbModal" class="modal fade" tabindex="-1"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">

        <div *ngIf="!isEdit">Adding new platform</div>
        <div *ngIf="isEdit">Editing platform</div>

      </div>
      <form [formGroup]="form" (ngSubmit)="savePlatform()">
        <div class="modal-body">

          <input type="hidden" name="id" formControlName="id">

          <div class="md-form">
            <input mdbInput appAutofocus class="form-control float-label text-capitalize" style="font-weight: 500;"
                   type="text"
                   #platformName formControlName="platformName" id="platformName"/>
            <label for="platformName">Platform Name</label>
          </div>

          <div *ngIf="form.controls['platformName'].invalid && (form.controls['platformName'].dirty || submitted)"
               class="error-message" style="position: inherit">
            <div>Platform Name is required.</div>
          </div>

          <div class="select">

            <mdb-select-2 [outline]="true"
                          formControlName="statusId"
                          label="Status"
                          placeholder="Select Status">
              <mdb-select-option *ngFor="let status of statusOptions" [value]="status.value">{{ status.label }}
              </mdb-select-option>
            </mdb-select-2>
          </div>

        </div>
        <div class="modal-footer text-right">
          <button *ngIf="! updateWait" class="btn btn-tertiary-outline ml-2" type="button" (click)="hideModal()">Cancel
          </button>
          <button [disabled]="updateWait" class="btn btn-tertiary ml-2" style="min-width: 80px;" type="submit">
            <span *ngIf="isEdit">Update</span>
            <span *ngIf="!isEdit">Add</span>
            <span *ngIf="updateWait">&nbsp;<i class="fa fa-spin fa-spinner"></i></span>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<app-delete-confirm-modal
  [showDialog]="showDeleteConfirm"
  (modalClosed)="showDeleteConfirm = false"
  (confirmDelete)="confirmDelete()"
  (cancelDelete)="cancelDelete()"></app-delete-confirm-modal>
