@media all and (min-width: 768px) {
  tr  {
    td {
      vertical-align: middle !important;
    }
    span {
      &:not([class]) {
        padding: 0.7rem 0.7rem;
      }
    }
  }
}

.delete-button { font-size: 1.2rem; }

.status {
  color: #fff;

  &.status-1 {
    background-color: var(--successColour);
  }

  &.status-2, &.status-4 {
    background-color: var(--warningColour);
  }

  &.status-3, &.status-5 {
    background-color: var(--dangerColour);
  }

}
