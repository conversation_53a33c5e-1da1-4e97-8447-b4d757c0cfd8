.brokerage-block {
  background-color: white;
  min-height: 105px;
}

.broker-owner {
  font-weight: 500;
  line-height: 12px;
  padding-left: 10px;
}

.filter-box label {
  font-size: 0.75rem;
  font-weight: 500;
  margin-bottom: 0rem;
}

.brokerage {
  font-weight: 600;
  font-size: 0.8rem;

  &.filter-box {
    padding: 10px 15px !important;
    margin-bottom: 10px;
  }
}

.button-margin {
  margin-bottom: 6px;
}

.adminstatus-2 {
  color: var(--warningColour);
}

.adminstatus-3, .adminstatus-4 {
  color: var(--dangerColour);
}

.brokerage-status {
  display: inline-block;
  background-color: blue;
  min-width: 180px;
  text-align: center;
  color: #fff;

  &.status-color-1 {
    background-color: #FFC107;
  }

  &.status-color-2 {
    background-color: var(--successColour);
  }

  &.status-color-3 {
    background-color: var(--dangerColour);
  }
}

.last-updated {
  font-size: 0.675rem;
  display: inline-block;
  font-weight: 400;
  padding-left: 7px;
}

.brokerage-item {
  border-radius: var(--widgetBorderRadius);
  overflow: hidden;
  margin-bottom: 15px;
}
