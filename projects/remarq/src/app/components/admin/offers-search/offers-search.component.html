<div class="d-flex flex-wrap mb-1">
  <div class="flex-grow-1">
    <h1 class="page-header" style="line-height: 32px; margin-bottom: 0px">{{ title }}</h1>
  </div>
  <div class="flex-shrink-1">
    <form *ngIf="filterForm" [formGroup]="filterForm" (submit)="search()">
      <div class="d-flex grid-gap-5">
        <div>
          <div class="btn btn-sm btn-secondary" (click)="filterToMe()"><i [class]="filterForm.value.assignedTo == currentUser?.contactId ? 'fas fa-user-tie' : 'fa fa-users'"></i></div>
        </div>
        <div class="select select-sm no-margin" style="min-width: 200px;">
          <mdb-select-2 [outline]="true"
                        formControlName="assignedTo"
                        placeholder="Select Assigned">
            <mdb-select-option [value]="EMPTY_GUID">All Assignees</mdb-select-option>
            <mdb-select-option *ngFor="let ato of assignedOptions" [value]="ato.value">{{ ato.label }}
            </mdb-select-option>
          </mdb-select-2>
        </div>
        <div class="select select-sm no-margin" style="min-width: 200px;">

          <mdb-select-2 [outline]="true"
                        formControlName="daysAgo"
                        placeholder="Select Range">
            <mdb-select-option *ngFor="let dao of daysAgoOptions" [value]="dao.value">{{ dao.label }}
            </mdb-select-option>
          </mdb-select-2>
        </div>
      </div>
    </form>
  </div>
</div>

<div class="p-2 widget padding">

  <div *ngIf="isBusy">
    <div class="d-flex justify-content-center align-items-center h-100">
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
      </div>
    </div>
  </div>

  <div *ngIf="!isBusy">

    <div *ngIf="offers.length == 0">
      <div class="pt-4 pb-3 text-center">
        <h1>No results found</h1>
      </div>
    </div>

    <div *ngIf="offers.length > 0">


      <table
        mdbTable mdbTableScroll scrollX="true" maxWidth="400"
        class="table table-striped table-hover table-compressed table-narrow unpaid-bills">
        <thead>
        <tr>
          <th>Placed</th>
          <th>Vehicle</th>
          <th>Bidder</th>
          <th>Bidder Contact</th>
          <th>Vendor</th>
          <th>Vendor Contact</th>
          <th>Amount</th>
          <th>Expires</th>
          <th>Assigned</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let item of offers | paginate: {
              itemsPerPage: pageSize,
              currentPage: page,
              totalItems: count
            }" (click)="adminUrl.adminManageNegotiations(item.advertId)">
          <td class="shrink-cell">{{ item.bidPlaced | date: 'dd MMM YYYY' }}</td>
          <td>
            <div class="table-line-1">
              {{ item.advert.vehicle.plate.plateName }}
              {{ item.advert.vehicle.make.makeName }}
              {{ item.advert.vehicle.model.modelName }}
            </div>
            <div class="table-line-2">
              {{ item.advert.vehicle?.fuelType?.fuelTypeName }} &bull;
              {{ item.advert.vehicle?.transmissionType?.transmissionTypeName }} &bull;
              {{ item.advert.vehicle?.bodyType?.bodyTypeName }} &bull;
              {{ item.advert.vehicle?.odometer | mileage }} &bull;
              {{ item.advert.vehicle.vrm }}
            </div>
          </td>
          <td style="background-color: rgba(0,0,0,0.05)">
            <div class="table-line-1">{{ item.customer.customerName }}</div>
            <div class="table-line-2">{{ item.customer.email }}</div>
          </td>
          <td style="background-color: rgba(0,0,0,0.05)">
            <div class="table-line-1">{{ item.contact.contactName }}</div>
            <div class="table-line-2">{{ item.contact.email }}</div>
          </td>
          <td>
            <div class="table-line-1">{{ item.advert.vehicle.customer.customerName }}</div>
            <div class="table-line-2">{{ item.advert.vehicle.customer.email }}</div>
          </td>
          <td>
            <div class="table-line-1">{{ item.advert.vehicle.contact.contactName }}</div>
            <div class="table-line-2">{{ item.advert.vehicle.contact.email }}</div>
          </td>
          <td>{{ item.bidAmt | customCurrency: null:null: '1.0-0' }}</td>
          <td>{{ (item.expires | date: 'dd MMM YYYY') || "N/A" }}</td>
          <td class="shrink-cell">
            {{ assignedToName(item) || "Unassigned" }}
          </td>
        </tr>
        </tbody>
      </table>

      <div class="h-100 pagination-divider">
        <div class="row">
          <div class="col-6 pagination-page-size">
            Items per Page:
            <select (change)="handlePageSizeChange($event)" class="form-control w-auto inline-block">
              <option *ngFor="let size of pageSizes" [ngValue]="size">
                {{ size }}
              </option>
            </select>
          </div>

          <div class="col-6 text-right">
            <pagination-controls
              responsive="true"
              class="paginator"
              previousLabel="Prev"
              nextLabel="Next"
              (pageChange)="onTableDataChange($event)">
            </pagination-controls>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
