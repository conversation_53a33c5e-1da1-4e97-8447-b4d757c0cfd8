.stack-list {
  display: flex;
  flex-wrap: wrap;
  grid-gap: 15px;
}

.filter-container .form-check-label {
  font-size: 0.75rem;
  font-weight: 500;
}

.sale-name {
}

.cap-line {
  line-height: 1.25rem;
}

.timed-listing {
  background-color: var(--timedSaleColour);
  color: #fff;
}

.hpi-line {
  padding-right: 10px;
}

.hpi-status-desc {
  font-weight: 600;

}

.managed-sale {
  background-color: var(--managedSaleColour);
  color: #fff;
}

.buy-it-now-sale {
  background-color: var(--buyNowColour);
  color: #fff;
}

.cap-line .pct {
  color: #888;
}

.vendor-title {

  font-size: 0.75rem;
  line-height: 24px;
  font-weight: 600;
  background-color: #efefef;

  padding-left: 17px;

  .assignedTo {

    background-color: rgba(0,0,0,0.1);
    font-size: 12px;
    line-height: 15px;
    border-radius: 7px;
    display: inline-block;
    padding: 0 7px;
  }
}

.column-1 {
  width: 80px;
  min-width: 80px;
}

.column-2 {
  width: 80px;
  min-width: 80px;
}

.pricing-table {
  font-weight: 400;
  font-size: 13px;
}

.sale-type-line {
  font-size: 0.75rem;
  line-height: 24px;
  font-weight: 600;
  padding-left: 14px;
}

.sale-status {
  margin-right: 10px;
  padding: 0px 7px;
  line-height: 18px;
  border-radius: 9px;
  font-size: 13px;
  font-weight: bold;

  &.sale-status-0 {
    background-color: var(--dangerColour);
    color: #fff;
  }

  &.sale-status-1 {
    background-color: var(--successColour);
    color: #fff;
  }

  &.sale-status-2, &.sale-status- {
    background-color: #666;
    color: #fff;
  }

}

.list-container {
  flex: 1 0 200px;
  flex-direction: column;
}

.filter-container {
  flex: 1 0 24px;
  flex-direction: column;
}

.vrm-plate {
  position: absolute;
  opacity: 0.9;
  bottom: 0px;
  right: 0px;
  min-width: 10px;
  font-size: 12px;
  background: #FDC72F;
  border: 1px solid #F2C02D;
  border-radius: 5px 5px 0 5px;
  text-align: center;
  font-weight: 700;
  padding: 0 5px 0 5px;
  display: inline-block;
}

div.make-model-deriv {
  flex-grow: 1;
  padding-right: 10px;
  font-weight: 400;
}

.make-model {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--textColour);
  overflow-x: hidden;
}

.deriv {
  font-size: 13px;
  line-height: 14px;
  font-weight: 400;
  color: #777;
}

.fuel, .trans, .colour, .body {
  font-size: 0.75rem;
  font-weight: 400;
  color: var(--textColour);
  white-space: nowrap;
}

.odometer {
  font-size: 0.75rem;
  font-weight: 400;
  color: var(--textColour);
  white-space: nowrap;
}

.pricing-box {
  background-color: #fff;
  box-sizing: border-box;
  align-content: center;
  width: 250px;
  max-width: 250px;
  min-width: 250px;
}

.vendor-box {
  background-color: #fafafa;
}

textarea {
  font-family: "Courier", serif;
}

.listing-ended {
  background-color: #efefef;
  width: 100%;
  margin-bottom: 3px;
  font-size: 0.7rem;
  font-weight: 500;
}

.vendor-customer-name {
  font-weight: 600;
  font-size: 16px;
  line-height: 1.2rem;
}

.vendor-contact-name {
  font-weight: 400;
  font-size: 13px;
  line-height: 1.2rem;
}

.vendor-contact-email {
  font-weight: 400;
  font-size: 13px;
  line-height: 1.2rem;
}

.hpi-status {
  font-size: 0.75rem;
  font-weight: 500;
}

.vendor-contact-phone {
  font-weight: 400;
  font-size: 13px;
  line-height: 1.2rem;
}

.cap-line {
  font-weight: 400;
  padding: 0px 5px;
  font-size: 0.875rem;
}


.veh-attrib {
  margin-right: 5px;
  font-size: 0.75rem;
  font-weight: 400;
}

.best-bid {
  font-weight: 600
}

.no-data {
  font-size: 0.9em;
}

@media screen and (min-width: 1024px) and (max-width: 1600px) {
  .vendor-box {
    width: 300px;
  }
}

@media screen and (min-width: 1600px){
  .vendor-box {
    width: 400px;
  }
}

