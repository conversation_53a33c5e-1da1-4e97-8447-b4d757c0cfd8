<div class="widget padding">
  <form [formGroup]="form" (ngSubmit)="submitForm()">

    <input type="hidden" name="id" formControlName="id">

    <div class="">
      <h1>Advert Notes</h1>
    </div>
    <div class="form-outline" data-mdb-input-init>
      <textarea class="form-control float-label" id="note" rows="4" formControlName="note"></textarea>
    </div>

    <div class="d-flex flex-wrap grid-gap-10 mt-3">

      <div class="flex-grow-1">
        <div class="input-group input-group-sm">

          <div class="input-group-prepend">
            <span class="input-group-text cursor-pointer" (click)="toggleSetReminder()">Add Task</span>
          </div>

          <div class="input-group-item">
            <div class="input-group-text pr-3">
              <div class="input-checkbox-sm">
                <mdb-checkbox mdbInput formControlName="showReminder"
                              (change)="toggleSetReminder()" id="reminder-checkbox">
                  <label for="reminder-checkbox" class="checkbox-text"><span class="checkbox-title"></span></label>
                </mdb-checkbox>
              </div>
            </div>
          </div>

          <div class="input-group-item">
            <input mdbInput type="date" id="reminder" class="form-control float-label"
                   formControlName="reminderDate" *ngIf="showSetReminder">
          </div>

          <div class="input-group-item">
            <input mdbInput type="time" id="reminder-time" class="form-control float-label"
                   formControlName="reminderTime" *ngIf="showSetReminder">
          </div>

          <div class="input-group-append" *ngIf="showSetReminder">
                    <span class="input-group-text cursor-pointer" (click)="clearTime()"><i
                      class="fa fa-undo"></i></span>
            <span class="input-group-text cursor-pointer" (click)="addTime('15m')">15M</span>
            <span class="input-group-text cursor-pointer" (click)="addTime('1h')">1H</span>
            <span class="input-group-text cursor-pointer" (click)="addTime('2h')">2H</span>
          </div>
        </div>
      </div>
      <div class="flex-grow-1 text-right">
        <button
          [disabled]="savingNote"
          class="btn btn-tertiary btn-sm ml-2" style="min-width: 80px;" type="submit">
          <span *ngIf="!savingNote">Save</span>
          <span *ngIf="savingNote"><i class="fa fa-spinner fa-spin"></i></span>
        </button>
      </div>
    </div>

  </form>

  <div style="max-height: 400px; overflow-y: auto; border-top: 1px solid #ccc;"  class="mt-2">

    <div *ngFor="let adNote of advertNotes" class="striped-notes">
      <div [ngClass]="adNote.prospectId ? 'prospect-note' : ''" (click)="openProspect(adNote.prospectId)">
        <div class="d-flex">
          <div class="flex-grow-1">
            <div class="note-header">
              <div class="note-date">{{ adNote.added | date: "dd MMM YYYY HH:mm" }}&nbsp;</div>
              <div class="contact-name"><span class="assigned-to btn-xxs">{{ adNote.contact?.contactName }}</span></div>
              <div class="customer-name" *ngIf="adNote.prospectCustomerName?.length > 0">&nbsp; &bull;
                &nbsp;Re: {{ adNote.prospectCustomerName }}
              </div>
            </div>
            <div [innerHTML]="helperService.parseAdvertNote(adNote.note)"></div>
          </div>

          <div *ngIf="adNote.adminTask?.sleepUntil" class="text-right">
          <span [class]="(adNote.overdue) ? 'overdue' : 'reminder'">
            <button (click)="clearTask($event, adNote)" class="btn btn-xs btn-xxs btn-primary-outline mb-2">
              Clear Task
            </button>
            <button class="btn btn-xs btn-xxs btn-primary-outline ml-2 mb-2"
                    (click)="rescheduleReminder($event, adNote)">
              Reschedule
            </button>
            <div>
            {{ adNote.adminTask?.sleepUntil | date: 'dd MMM HH:mm' }}
            </div>
          </span>
          </div>

        </div>
      </div>
    </div>
  </div>
</div>

<div mdbModal #reschedule="mdbModal" class="modal fade top" id="frameModalTop" tabindex="-1" role="dialog"
     aria-labelledby="myModalLabel" aria-hidden="true" [config]="{keyboard: true}">
  <div class="modal-dialog modal-sm modal-notify modal-dialog-centered" role="document">
    <!--Content-->
    <div class="modal-content text-center">
      <!--Header-->
      <div class="modal-header">
        Update Reminder
      </div>

      <!--Body-->
      <div class="modal-body">
        <div class="d-flex flex-wrap grid-gap-10 mt-3">
          <div class="flex-grow-1">
            <div class="md-form narrowest">
              <input mdbInput type="time" id="reminderTime" class="form-control float-label"
                     [(ngModel)]="rescheduleTime" style="border: 0;">
            </div>
          </div>
          <div class="flex-grow-1">
            <div class="md-form narrowest">
              <input mdbInput type="date" id="reminderDate" class="form-control float-label"
                     [(ngModel)]="rescheduleDate" style="border: 0;">
              <label for="reminder">Date </label>
            </div>
          </div>
        </div>
      </div>

      <!--Footer-->
      <div class="modal-footer">
        <button type="button" class="btn btn-primary-outline waves-effect" mdbWavesEffect data-dismiss="modal"
                (click)="cancelReschedule()">Cancel
        </button>
        <button (click)="confirmReschedule()" class="waves-effect btn btn-primary" mdbWavesEffect>Update</button>
      </div>
    </div>
    <!--/.Content-->
  </div>
</div>
