.striped-notes {
  padding: 4px;
  font-weight: 500;
  font-size: 0.8rem;
  border-bottom: 1px solid #eee;

  &:nth-of-type(odd) {
    background-color: var(--tableStripedBackgroundColour);
  }

  .note-header {
    font-size: 0.6rem;
    font-weight: 500;
    color: #999;
  }
}
.overdue {
  font-weight: bold;
  color: var(--errorColour);
  text-align: right;
}
.reminder {
  font-weight: bold;
  text-align: right;
}

.prospect-note {
  cursor: pointer;
}

.customer-name {
  color: var(--textColour);
}

.note-date { display: inline-block; }
.customer-name { display: inline-block; }
.contact-name { display: inline-block; }
