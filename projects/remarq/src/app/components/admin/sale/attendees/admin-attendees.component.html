<div *ngIf="!upcomingSales" class="mt-3 text-center">
  <app-loading-spinner loadingName="Sales"></app-loading-spinner>
</div>

<div class="mt-1">
  <h1 class="page-header">Sale Attendees</h1>
</div>

<div class="widget mb-3" style="margin-right: 10px;">
  <app-auction-list [sales]="upcomingSales" [selectedSaleId]="selectedSaleId"
                    (saleSelected)="saleSelected($event)"></app-auction-list>
</div>

<div class="row" *ngIf="selectedSaleId">
  <div class="col-md-6">
    <h1>Attending This Sale</h1>
    <div class="widget padding mb-3 h-100">

      <div style="max-height: 500px; overflow-y: auto;">
        <table class="table table-striped table-hover table-condensed" mdbTable>
          <thead>
          <tr>
            <th class="align-middle">Bidder#</th>
            <th class="align-middle">Name</th>
            <th class="align-middle">Email</th>
            <th class="align-middle">Address</th>
            <th class="align-middle">&nbsp;</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let attendee of attendees" [class.table-primary]="selectedAttendee == attendee">
            <td class="align-middle text-center shrink-cell"><span
              class="bidder-number">{{ attendee.paddleNumber }}</span></td>
            <td class="align-left align-middle">
              <div class="table-line-1">{{ attendee.contact.contactName }}</div>
              <div class="table-line-2">{{ attendee.contact.customer.customerName }}</div>
            </td>
            <td class="align-middle">{{ attendee.contact.email }}</td>
            <td class="align-middle">
              <div class="table-line-2">
                {{ attendee.contact?.primaryAddress?.addressLine1 }}{{ attendee.contact?.primaryAddress?.addressLine2 ? ', '
                + attendee.contact?.primaryAddress?.addressLine2 : '' }}{{ attendee.contact?.primaryAddress?.addressCounty ? ', '
                + attendee.contact?.primaryAddress?.addressCounty : '' }} {{attendee.contact?.primaryAddress?.addressPostcode}}
              </div>
            </td>
            <td class="align-middle shrink-cell">
              <span (click)="confirmRemoveAttendee(attendee)" class="btn mr-2" mdbTooltip="Remove Attendee From Sale"><i
                class="fas fa-times-circle"></i></span>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <div class="col-md-6">
    <h1>Customer Lookup</h1>
    <div class="widget padding mb-3 h-100">

      <div>
        <input class="form-control my-0 py-1 search-text-input" type="text" [(ngModel)]="contactSearchText"
               (ngModelChange)="onContactSearch()"
               spellcheck="false"
               placeholder="Contact Search" aria-label="Contact Search">
      </div>

      <div style="max-height: 500px; overflow-y: auto; margin-top: 7px;">
        <table class="table table-striped table-hover table-condensed" mdbTable>
          <thead>
          <tr>
            <th class="align-middle">Name</th>
            <th class="align-middle">Email</th>
            <th class="align-middle">Address</th>
            <th class="align-middle">&nbsp;</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let contact of contacts">
            <td class="align-left">
              <div class="table-line-1">{{ contact.contactName }}</div>
              <div class="table-line-2">{{ contact.customer.customerName }}</div>
            </td>
            <td class="shrink-cell align-middle">{{ contact.email }}</td>
            <td class="align-middle">
              {{ contact?.primaryAddress?.addressLine1 }}{{ contact?.primaryAddress?.addressLine2 ? ', '
              + contact?.primaryAddress?.addressLine2 : '' }}{{ contact?.primaryAddress?.addressCounty ? ', '
              + contact?.primaryAddress?.addressCounty : '' }} {{ contact?.primaryAddress?.addressPostcode}}
            </td>
            <td class="align-middle shrink-cell">
              <span (click)="addAttendee(contact)" class="mr-2" mdbTooltip="Add Attendee To Sale"><i
                class="fas fa-plus-circle fa-15x text-primary"></i></span>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>


<div mdbModal #paddleNumberModal="mdbModal" class="modal fade top" id="frameModalTop" tabindex="-1" role="dialog"
     aria-labelledby="myModalLabel" aria-hidden="true" [config]="{backdrop: false, ignoreBackdropClick: true}">
  <div class="modal-dialog modal-sm" role="document">
    <!--Content-->
    <div class="modal-content text-center">
      <!--Header-->
      <div class="modal-header d-flex justify-content-center">
        <p class="heading">Enter Bidder Number</p>
      </div>

      <form [formGroup]="form" (ngSubmit)="assignPaddleNumber()" autocomplete="off">
        <!--Body-->
        <div class="modal-body">
          <mdb-icon fas icon="gavel" size="2x" class="animated rotateIn"
                    style="margin:5px; padding-top: 5px; vertical-align: sub;"></mdb-icon>
          <input #paddleInput id="paddleInput" formControlName="paddleNumber" maxlength="5" tabindex="0"
                 [autofocus]="true" [autocomplete]="false">
          <div class="inline-error-message" *ngIf="(f.paddleNumber.dirty || submitted) && f.paddleNumber.errors">
            <span *ngIf="f.paddleNumber.errors.required">Bidder Number is required</span>
            <span *ngIf="!f.paddleNumber.errors.required">Bidder Number must be a maximum 5 character number</span>
          </div>
        </div>

        <!--Footer-->
        <div class="modal-footer justify-content-center">
          <button type="button" (click)="paddleNumberModal.hide()" class="btn btn-primary-outline mr-3">Cancel</button>
          <button type="submit" class="btn btn-primary">Confirm</button>
        </div>
      </form>
    </div>
    <!--/.Content-->
  </div>
</div>

