<table class="table table-striped table-hover table-condensed" mdbTable>
  <thead>
    <tr>
      <th class="align-middle">Sale Name</th>
      <th class="align-middle">Start Date</th>
      <th class="align-middle">End Date</th>
      <th class="align-middle">No. Lots</th>
      <th class="align-middle">Live</th>
    </tr>
  </thead>
  <tbody>
    <tr *ngFor="let sale of sales" (click)="selectSale(sale)" [class.table-primary]="selectedSale == sale">
      <td class="align-middle">
        <div class="table-line-1">{{ sale.saleName }}</div>
        <div class="table-line-2">{{ sale.address.addressName }}</div>
      </td>
      <td class="shrink-cell align-middle">{{ sale.saleStart | date: 'EEE MMM d, h:mm a' }}</td>
      <td class="shrink-cell align-middle">{{ sale.saleEnd | date: 'EEE MMM d, h:mm a' }}</td>
      <td class="align-middle text-center">{{ sale.advertCount }}</td>
      <td class="align-middle">
        {{ sale.isLive ? 'Yes' : 'No' }}
      </td>
    </tr>
  </tbody>
</table>
