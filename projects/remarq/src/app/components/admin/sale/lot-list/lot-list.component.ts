import {Component, Input, OnInit} from '@angular/core';
import {AdvertService, SaleService, URLService} from '../../../../services';
import {AdminUrlService} from "../../services";
import {AdvertDTO, SaleDTO} from "../../../../global/interfaces";
import {ImageService} from "../../../../global/services";
import {NegotiationStateEnum} from "../../../../global/enums";

@Component({
  selector: 'app-lot-list',
  templateUrl: './lot-list.component.html',
  styleUrls: ['./lot-list.component.scss']
})
export class LotListComponent implements OnInit {

  constructor(private saleService: SaleService,
              public imageService: ImageService,
              private urlService: URLService,
              public adminUrlService: AdminUrlService,
              private advertService: AdvertService) {
  }

  _saleId: string;
  @Input('saleId') set saleId(value: string) {
    if (this._saleId != value) {
      this._saleId = value;
    }

    if (this._saleId) {
      this.loadSale();
    }
  }

  get saleId(): string {
    return this._saleId;
  }

  selectedSale: SaleDTO;
  currentLots: AdvertDTO[];
  loadingSale = false;
  provisionalOnly = false;

  public NegotiationState = NegotiationStateEnum;

  ngOnInit(): void {
  }

  loadSale() {
    this.loadingSale = true;
    this.saleService.getSale(this.saleId, {component: 'AdminSaleDetails'}).then(result => {
      this.selectedSale = result.sale;
      this.setCurrentLots();

      this.loadingSale = false;
    });
  }

  viewAdvert(advert: AdvertDTO) {
    this.urlService.advertView(advert.id);
  }

  reserveMetIcon(advert: AdvertDTO) {
    if (advert?.reserveMet) {
      return "<i class='fa fa-check reserve-met text-success'></i>";
    }
    return "-";
  }

  toggleProvisional(event: MouseEvent) {
    // change filtered lots to show only provisional or all depending on model state
    this.setCurrentLots();
  }

  setCurrentLots() {
    if (this.provisionalOnly) {
      this.currentLots = this.selectedSale.adverts.filter(x => x.negotiations && x.negotiations.length > 0);
    } else {
      this.currentLots = this.selectedSale.adverts;
    }
  }

  manageNegotiations(event: MouseEvent, id: string) {
    event.stopPropagation();

    // navigate to manage negotiations screen
    this.adminUrlService.adminManageNegotiations(id);
  }
}
