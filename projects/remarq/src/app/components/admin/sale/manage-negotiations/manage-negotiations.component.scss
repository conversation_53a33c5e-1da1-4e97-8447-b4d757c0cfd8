.stack-list {
  display: flex;
  flex-wrap: wrap;
  grid-gap: 15px;
}

.vehicle-sold-bg {
  background: var(--successColour);
  color: #fff;
}

.disabled {
  cursor: not-allowed;
}

.filter-container .form-check-label {
  font-size: 0.75rem;
  font-weight: 500;
}

.status-banner {
  border-radius: var(--widgetBorderRadius) var(--widgetBorderRadius) 0 0;

}

.bidders-table td {
  vertical-align: middle;
}

.explain {
  font-size: 0.875rem;
  font-weight: 500;
}

.explain-customer {
  margin-top: 7px;
  font-weight: 700;
}

.explain-contact {
  font-size: 0.75rem;
  font-weight: 500;
}

.bid-placed {
  div {
    line-height: 14px;
    font-size: 12px;
    white-space: nowrap;
  }
}

.sale-name {
  font-size: 13px;
  line-height: 18px;
  display: inline-block;
  font-weight: 500;
}

.cap-line td {
  line-height: 1.25rem;
}

.cap-line .pct {
  padding-left: 5px;
  font-size: 0.75rem;
  line-height: 0.75rem;
  color: #888;
}

.reserve-line {
  td {
    font-size: 18px;
    line-height: 18px;
    font-weight: 600;
    padding-bottom: 10px;
    padding-top: 5px;
  }
}

.sale-status {
  margin-right: 10px;
  padding: 0px 7px;
  line-height: 18px;
  border-radius: 9px;
  font-size: 13px;
  font-weight: bold;
  display: inline-block;

  &.sale-status-1 {
    background-color: var(--successColour);
    color: #fff;
  }

  &.sale-status-2, &.sale-status- {
    background-color: #666;
    color: #fff;
  }
}

.filter-container {
  flex: 1 0 24px;
  flex-direction: column;
}

.vrm-plate {
  font-size: 12px;
  background: #FDC72F;
  border: 1px solid #F2C02D;
  border-radius: 4px;
  text-align: center;
  font-weight: 700;
  padding: 0 5px 0 5px;
  display: inline-block;
}

div.make-model-deriv {
  flex-grow: 1;
  padding-right: 10px;
  font-weight: 400;
}

.make-model {
  font-size: 15px;
  font-weight: 600;
  color: var(--textColour);
}

.deriv {
  font-size: 13px;
  font-weight: 500;
  color: #777;
}

.fuel, .trans, .colour, .body {
  font-size: 13px;
  font-weight: 500;
  color: var(--textColour);
}

.odometer {
  font-size: 13px;
  font-weight: 500;
  color: var(--textColour);
}

.vendor-box {
  background-color: #f1f1f1;
  border-left: 1px solid var(--widgetBorderColour);
  padding-left: 10px;
}

textarea {
  font-family: "Courier", serif;
}

.vehicle-sold {
  background-color: var(--dangerColour);
  color: var(--colour3);
  width: 100%;
  padding: 8px;
  text-align: center;
  margin-bottom: 8px;
}

.vendor-customer-name {
  font-weight: 600;
  font-size: 16px;
}

.vendor-contact-name {
  font-weight: 400;
  font-size: 13px;
}

.vendor-contact-email {
  font-weight: 400;
  font-size: 13px;
}

.vendor-contact-phone {
  font-weight: 400;
  font-size: 13px;
}

.small-print {
  font-size: 12px;
  font-weight: 400;
}

.status-name {
  background-color: #0d181c;
  color: #fff;
  padding-bottom: 5px;
  padding-left: 17px;
  padding-right: 5px;
  padding-top: 5px;
  font-weight: 600;
  font-size: 16px;
}

.not-bold {
  opacity: 0.4;
  font-size: 14px;
  padding-top: 5px;
  padding-left: 10px;
}

.status-name {

  &.status-color-1, &.status-color- {
    background-color: #FFC107;
  }

  &.status-color-2 {
    background-color: var(--successColour);
  }

  &.status-color-3 {
    background-color: var(--dangerColour);
  }
}

.admin-status-name {

  border: 1px solid #ccc;
  border-radius: 4px;

  &.admin-status-color-1 {
    border-color: var(--successColour);
    color: var(--successColour);
  }

  &.admin-status-color-2, &.admin-status-color-3, &.admin-status-color-4 {
    border-color: var(--dangerColour);
    color: var(--dangerColour);
  }
}

td.darker, th.darker {
  background-color: rgba(15, 108, 189, 0.05);
}
