<div class="row">

  <div class="col-md-6">

    <div class="d-flex flex-wrap">
      <div class="flex-grow-1">
        <h1 class="page-header">Sale Profiles
          <i class="fa fa-info-circle"
             placement="bottom"
             mdbTooltip="Sale Profiles are used to auto-populate managed sales. Vehicles matching the Search Profiles inside the Sale Profile will be selected and added to the Sale"
          ></i></h1>
      </div>
      <div class="flex-shrink-1 pt-1">
        <button (click)="addProfile()" class="btn btn-xs btn-primary" mdbTooltip="Create a new Sale Profile"><i
          class="fas fa-plus-circle"></i> Sale Profile
        </button>
      </div>
    </div>

    <div class="widget mb-3 h-100">

      <div style="max-height: 500px; overflow-y: auto;">
        <table class="table table-striped table-hover table-condensed" mdbTable>
          <thead>
          <tr>
            <th class="align-middle shrink-cell">Name</th>
            <th class="align-middle shrink-cell">Max Adverts</th>
            <th class="align-middle">Search Profiles</th>
            <th class="align-middle">&nbsp;</th>
          </tr>
          </thead>
          <tbody class="table">
          <tr *ngFor="let profile of profiles" (click)="onProfileSelected(profile)"
              [class.table-primary]="selectedProfile == profile">
            <td class="align-middle text-center shrink-cell"><div class="table-line-1">{{ profile.profileName }}</div></td>
            <td class="align-middle align-left medium-input">
              <input [(ngModel)]="profile.maxAdverts" class="form-control"
                     (blur)="saveProfileMaxAdverts(profile)">
              <!--              <span>{{ profile.maxAdverts }}</span>-->
            </td>
            <td class="align-middle">
              <div *ngFor="let search of profile.saleSearchProfiles" style="margin-bottom: 4px;">
                <div>
                  <span class="selected-item" mdbTooltip="Remove Search Profile"
                        (click)="removeSearchProfile(profile, search)">
                    <div class="table-line-1">
                    <i class="fa fa-times-circle"></i>&nbsp;
                      {{ search.savedSearch.searchName }}</div>
                  </span>
                </div>
                <div style="padding-left: 19px;" class="table-line-2">
                  <span *ngFor="let desc of search.savedSearch.parsedDesc">{{desc}}</span>
                </div>
              </div>
            </td>
            <td class="align-middle shrink-cell">
              <span (click)="deleteProfile(profile)" class="btn mr-2" mdbTooltip="Delete this Profile"><i
                class="fas fa-trash-alt fa-12x"></i>&nbsp;</span>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- search profiles list -->
  <div class="col-md-6">
    <div class="d-flex">
      <div class="flex-grow-1">
        <h1 class="page-header">Search Profiles <i class="fa fa-info-circle"
                                                   mdbTooltip="A search profile is a type of 'Saved Search' that can be used to populate Sale Profiles. You may use one or more Search Profiles in a Sale Profile"></i>
        </h1>
      </div>
      <div class="flex-shrink-1 pt-1">
        <button class="btn btn-primary btn-xs" (click)="url.search()"><i class="fa fa-plus-circle"></i> Search Profile
        </button>
      </div>
    </div>
    <div class="widget mb-3 h-100">

      <div style="max-height: 500px; overflow-y: auto;">
        <table class="table table-striped table-hover table-condensed" mdbTable>
          <thead>
          <tr>
            <th class="align-middle">&nbsp;</th>
            <th class="align-middle">Search Name</th>
            <th class="align-middle">Max.</th>
            <th class="align-middle">Filters</th>
            <th class="align-middle">&nbsp;</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let search of searchProfiles" [class.table-primary]="selectedSearchProfile == search">
            <td class="align-middle text-center shrink-cell" (click)="addSearchToSaleProfile(search)"
                mdbTooltip="Add Search Profile to selected Sale Profile">
              <button class="btn btn-xs btn-primary">Add</button>
            </td>
            <td class="align-middle text-left"><div class="table-line-1">{{ search.searchName }}</div></td>
            <td class="align-middle align-left shrink-cell">
              <span>{{ !search.maxVehicles ? "N/A" : search.maxVehicles }}</span>
            </td>
            <td class="align-middle">
              <div>
                <span *ngFor="let desc of search.parsedDesc">{{desc}}</span>
              </div>
              <!--              <div *ngFor="let desc of search.parsedDesc">-->
              <!--                {{ desc.name }}: {{ desc.description }}-->
              <!--              </div>-->
            </td>
            <td class="align-middle shrink-cell" (click)="viewVehicles(search)" placement="left"
                mdbTooltip="View vehicles returned with this search">
              <div class="btn btn-xs btn-outline-primary">
                View
              </div>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<div mdbModal #saleProfileModal="mdbModal" class="modal fade top" id="frameModalTop" tabindex="-1" role="dialog"
     aria-labelledby="myModalLabel" aria-hidden="true" [config]="{backdrop: false, ignoreBackdropClick: true}">
  <div class="modal-dialog modal-MMD modal-dialog-centered" role="document">
    <!--Content-->
    <div class="modal-content text-center">
      <!--Header-->
      <div class="modal-header d-flex">
        Sale Profile Details
      </div>

      <form [formGroup]="form" (ngSubmit)="createProfile()" autocomplete="off">
        <!--Body-->
        <div class="modal-body">
          <div style="margin-bottom: 8px; text-align: left" class="md-form">
            <input #profileName id="profileName" formControlName="profileName" maxlength="64" tabindex="0"
                   mdbInput
                   class="form-control float-label"
                   [autofocus]="true" [autocomplete]="false">
            <label for="profileName">Profile Name</label>
            <div class="inline-error-message" *ngIf="(f.profileName.dirty || submitted) && f.profileName.errors">
              <span>Profile Name is required</span>
            </div>
          </div>

          <div style="text-align: left" class="md-form">
            <input type="number" #maxAdverts id="maxAdverts" formControlName="maxAdverts" maxlength="3" tabindex="1"
                   mdbInput
                   class="form-control float-label"
                   [autofocus]="true" [autocomplete]="false">
            <label for="maxAdverts">Max Adverts </label>
            <div class="inline-error-message" *ngIf="(f.maxAdverts.dirty || submitted) && f.maxAdverts.errors">
              <span>Max adverts should be a number between 0 and 999</span>
            </div>
          </div>
        </div>

        <!--Footer-->
        <div class="modal-footer text-right">
          <button type="button" (click)="saleProfileModal.hide()" class="btn btn-tertiary-outline mr-3">Cancel</button>
          <button type="submit" class="btn btn-tertiary">Create</button>
        </div>
      </form>
    </div>
    <!--/.Content-->
  </div>
</div>
