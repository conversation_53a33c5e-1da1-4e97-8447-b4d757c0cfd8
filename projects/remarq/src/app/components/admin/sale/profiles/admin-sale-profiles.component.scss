.stack-list {
  display: flex;
  flex-wrap: wrap;
}

.list-container {
  flex: 1 1 200px;
  flex-direction: column;
}

.selected-item {

  cursor: pointer !important;
  line-height: 20px;

  &:hover {
    background-color: rgba(50, 50, 50, 0.1);
  }

  .fa-times-circle:before {
    background-color: #fff;
    border-radius: 6px;
    box-shadow: inset 0 0 0 2px #c00;
    box-sizing: border-box;
    height: 12px;
    width: 12px;
    font-size: 12px;
    line-height: 12px;
    color: #c00 !important;
  }
}
