<span style="display: inline-block; padding-left: 5px;">
  <button (click)="messageRostrum()" class="btn btn-primary-outline btn-sm"><i class="fa fa-comment"></i> Message <span
    *ngIf="unreplied > 0" class="badge">{{ unreplied }}</span></button>
  <div mdbModal #messageRostrumModal="mdbModal" class="modal" role="dialog" aria-labelledby="myBasicModalLabel" [config]="{keyboard: true}">
    <div class="modal-dialog modal-side modal-bottom-right" role="document">
      <div class="modal-content">
        <div class="modal-body">
          <div class="container-fluid" style="max-height: 400px; overflow-y: auto;" id="container-fluid">

            <div *ngFor="let message of messages">
              <div class="fromto">{{ message.contactName }} ({{ message.customerName }}):</div>
              <div class="message-box">{{ message.messageText }}</div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-sm btn-outline-primary  waves-effect" mdbWavesEffect
                  (click)="messageRostrumModal.hide()">Close
          </button>
          <button (click)="sendMessage({})" class="waves-effect btn btn-sm btn-primary" mdbWavesEffect>Send</button>
        </div>
      </div>
    </div>
  </div>
</span>
