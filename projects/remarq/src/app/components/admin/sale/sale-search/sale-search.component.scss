.sale-status {
  width: 70px;
  text-align: center;
  color: #fff;

  &.sale-status-1 {
    background-color: var(--successColour) !important;
  }
  &.sale-status-2 {
    background-color: var(--dangerColour) !important;
  }
}

.sale-type {

  border-radius: 13px;
  padding: 0 5px;
  display: inline-block;
  text-align: center;
  width: 100%;
  color: #fff;

  &.sale-type-1 {
    background-color: var(--buyNowColour);
  }
  &.sale-type-2 {
    background-color: var(--managedSaleColour);
  }
  &.sale-type-3 {
    background-color: var(--timedSaleColour);
  }
  &.sale-type-4 {
    background-color: var(--underwriteColour);
  }

}

