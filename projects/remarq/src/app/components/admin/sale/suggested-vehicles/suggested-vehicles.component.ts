import {Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>nit, ViewChild} from '@angular/core';
import {AdvertSearchFilters} from '../../../main/advert/advert-search-v2/advert-search-filters';
import {AdvertSearchService, EventService, RoleGuardService, SaleService} from '../../../../services';
import {ModalDirective, ToastService} from 'ng-uikit-pro-standard';
import {AdminSaleService} from '../../services';
import {
  AdvertDTO,
  AdvertSearchFiltersDTO,
  IAdvertData,
  ISuggestedVehicle,
  SaleDTO,
  SaleProfileDTO,
  SavedSearchDTO
} from "../../../../global/interfaces";
import {LoggerService, UserService, ImageService} from "../../../../global/services";
import {} from "../../../../global/enums";

@Component({
  selector: 'app-suggested-vehicles',
  templateUrl: './suggested-vehicles.component.html',
  styleUrls: ['./suggested-vehicles.component.scss']
})
export class SuggestedVehiclesComponent implements OnInit, OnDestroy {

  constructor(
      private advertSearchService: AdvertSearchService
    , private userService: UserService
    , private authService: RoleGuardService
    , private eventService: EventService
    , private toast: ToastService
    , public imageService: ImageService
    , private saleService: SaleService
    , private adminSaleService: AdminSaleService,
      private logService: LoggerService) {
  }

  logger = this.logService.taggedLogger(this.constructor?.name);

  SearchFilters = new AdvertSearchFilters(this.advertSearchService, this.authService, this.logService);
  eventSub;

  @ViewChild('advertsModal') mdbModal: ModalDirective;

  sale: SaleDTO;
  saleProfile: SaleProfileDTO;

  // all search profiles in current sale profile
  searchProfiles: SavedSearchDTO[];

  isBusy = false;

  suggestedVehicles: ISuggestedVehicle[];

  toastOpts: { opacity: 0.98 };

  existingLotCount = 0;
  totalLotCount = 0;

  async ngOnInit() {
    this.eventSub = this.eventService.ShowSaleProfileSuggestedVehicles.subscribe(async (sale: SaleDTO) => {
      // if there is no associated sale profile, exit with message
      if (!sale.saleProfile) {
        this.toast.error("Selected Sale has no associated Sale Profile", "Error", this.toastOpts);
        return;
      }

      this.sale = sale;
      this.saleProfile = sale.saleProfile;

      this.searchProfiles = this.saleProfile.saleSearchProfiles?.map(sp => {
        return sp.savedSearch;
      });

      // if we have no search profiles we can ignore the event and do nothing
      if (this.searchProfiles && this.searchProfiles.length > 0) {
        // show the modal dialog (spinner will inform user that adverts are loading)
        this.mdbModal.show();

        this.isBusy = true;

        // get the count of existing lots to prevent allocating more than max for sale (if a max is set)
        this.existingLotCount = await this.saleService.getSaleLotCount(this.sale.id);
        this.totalLotCount = this.existingLotCount;

        this.isBusy = false;

        // clear cached data
        this.suggestedVehicles = [];

        // load adverts for each search profile
        for (const item of this.searchProfiles) {
          const sv = {searchProfile: null, advertData: null, loading: true};
          this.suggestedVehicles.push(sv);

          const sp = item;
          const ads = await this.getAdvertsForSearchProfile(sp);

          // de-duplicate
          // ads = ads.map(ad => {
          //   // ensure ad does not exist in any suggestedVehicle list
          //   if (!this.suggestedVehicles.flatMap(sv => sv.adverts)?.find(x => x.id == ad.id)) {
          //     return ad;
          //   }
          // })

          sv.searchProfile = sp;
          sv.advertData = ads;
          sv.loading = false;
        }

        this.setTotalCount();
        // this.loadingAdverts = false;

        this.logger.debug("SUGGESTED: ", this.suggestedVehicles, " - lot counts: ", this.totalLotCount, this.existingLotCount);
      }
    });
  }

  ngOnDestroy() {
    if (this.eventSub) {
      this.eventSub.unsubscribe();
    }
  }

  async getAdvertsForSearchProfile(profile: SavedSearchDTO): Promise<IAdvertData[]> {

    let adverts = [];
    const search = this.createSearchDTO(profile);
    search.component = "SuggestedVehicle";

    // use limit in the search DTO if maxVehicles are set for this profile
    if (profile.maxVehicles > 0) {
      search.limit = profile.maxVehicles;
    }

    await this.advertSearchService.getSearchResults_v2(search)
      .then(result => {
        adverts = result.adminListings.map(sa => {
            return {advert: sa.advert, customer: sa.customer};
          }
        );
      })
      .catch(err => {
        this.toast.error(`Could not load vehicles from search profile ${profile.searchName}`, "Error", this.toastOpts);
        this.logger.error(`Failed to load vehicles from profile ${profile.searchName}`, err);
      });

    // remove adverts already lotted to the sale
    adverts = adverts.filter(x => !this.sale.adverts.includes(x));

    // remove adverts already in the list
    // adverts = adverts.filter(x => this.suggestedVehicles?.flatMap(sv => sv?.advertData?.map(y => y.advert.id)?.indexOf(x.id) < 0 ));

    // adverts are selected by default
    adverts.forEach(ad => {
      if (ad.advert) {
        ad.advert.selected = true;
      }
    });

    return adverts;
  }

  createSearchDTO(searchProfile: SavedSearchDTO): AdvertSearchFiltersDTO {
    // this.loadingAdverts = true;

    const search = JSON.parse(searchProfile.search.searchJSONValue) as AdvertSearchFiltersDTO;

    this.SearchFilters.Keywords = search.filters.keywords;
    this.SearchFilters.SearchOptions.runner = search.filters.runner;
    this.SearchFilters.SearchOptions.catStatus = search.filters.catStatus;

    this.SearchFilters.EnsureValidSearchDTO(search);

    return search;
  }

  selectAdvert(advert: AdvertDTO) {
    this.logger.info("Inverting selected");
    advert.selected = !advert.selected;
  }

  addSelectedVehicles() {
    this.isBusy = true;

    // lot the selected vehicles into the sale
    const lots = this.suggestedVehicles?.flatMap(sv => sv?.advertData).filter(x => x.advert.selected)?.map(y => y.advert);
    if (!lots || lots.length === 0) {
      this.toast.error("No lots are selected", "Error", this.toastOpts);
      this.isBusy = false;
      return;
    }

    let updates = [];
    lots.forEach((x) => {
      updates.push({advertId: x.id, customerId: x.customerId, saleId: this.sale.id});
    });

    this.adminSaleService.setSaleLots(updates).then(() => {
      this.isBusy = false;
      this.mdbModal.hide();
    });
  }

  setTotalCount() {
    this.totalLotCount = this.existingLotCount + this.suggestedVehicles
      ?.flatMap(sv => sv.advertData)?.filter(x => x && x.advert && x.advert.selected)?.length;
  }
}
