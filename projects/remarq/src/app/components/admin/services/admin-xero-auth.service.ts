import {Injectable} from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { DataServiceInterface } from '../../../global/services';

@Injectable()
export class AdminXeroAuthService {

  constructor(private http: HttpClient, private data: DataServiceInterface) { }

  initiateXeroAuth(forceRefresh: boolean = false, showURL: boolean = false): void {
    const url = `${this.data.apiUrl}/xeroAuth?forceRefresh=${forceRefresh ? 1 : 0}`;
    
    if (showURL) {
      window.open(url, '_blank', 'width=800,height=600');
    }
  }
}
