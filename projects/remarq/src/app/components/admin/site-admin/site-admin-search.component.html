<h1 class="page-header">Site Admins</h1>

<div class="widget mt-2">
  <table style="width: 100%" class="table table-striped table-condensed vertical-centre-cells" mdbTable>
    <thead>
    <tr>
      <th>Name</th>
      <th>Phone</th>
      <th>Extension</th>
      <th>Customer</th>
      <th>Role</th>
      <th>Last Login</th>
      <th>Added</th>
      <th>&nbsp;</th>
    </tr>
    </thead>
    <tbody>
    <tr *ngFor="let admin of siteAdmins" (click)="editContact(admin)">
      <td>
        <div class="table-line-1">{{ admin.contactName }}</div>
        <div class="table-line-2">{{ admin.email }}</div>
      </td>
      <td>
        <div class="table-line-1">{{ admin.phone1 }}</div>
        <div class="table-line-2">{{ admin.phone2 }}</div>
      </td>
      <td>
        <div class="table-line-1">{{ admin.customer?.customerName }}</div>

      </td>
      <td><div class="table-line-1">{{ admin.contactInternalInfo?.extension }}</div></td>
      <td><span *ngFor="let role of admin.roles" class="role">{{ role.roleName }}</span></td>
      <td>{{ admin.lastLogin | date: "dd/MM/YY HH:mm" }}</td>
      <td>{{ admin.added | date: "dd/MM/YY HH:mm" }}</td>
      <td>
        <div class="btn btn-xxs status status-{{ admin.statusId }}">{{ statusName[admin.statusId] }}</div>
      </td>
    </tr>
    </tbody>
  </table>
</div>

<app-admin-contact-edit
  (reloadContactEvent)="reloadContacts()"
  [editingContact]="editingContact"
  [showContactModalTime]="showContactModalTime"
></app-admin-contact-edit>
