<div class="d-flex flex-wrap">
  <div class="flex-fill">
    <h1 class="page-header">Scan Configure</h1>
  </div>
</div>

<div class="mb-2" id="locations-widget">

  <div class="d-flex flex-wrap">
    <div class="flex-grow-1">
      <div class="mr-2 select select-sm" *ngIf="serviceOptions?.length > 0">
        <mdb-select-2
          [(ngModel)]="setServiceId"
          (selected)="serviceSelected($event)"
          placeholder="Select Service">
          <mdb-select-option *ngFor="let xoption of serviceOptions" [value]="xoption.value">{{ xoption.label }}
          </mdb-select-option>
        </mdb-select-2>
      </div>
    </div>
    <div class="flex-grow-1">
      <div class="mr-2 select select-sm" *ngIf="styleOptions?.length > 0">
        <mdb-select-2
          [(ngModel)]="setStyleId"
          (selected)="styleSelected($event)"
          placeholder="Select Style">
          <mdb-select-option *ngFor="let xoption of styleOptions" [value]="xoption.value">{{ xoption.label }}
          </mdb-select-option>
        </mdb-select-2>
      </div>
    </div>
    <div class="flex-grow-1">
      <table class="w-100">
        <tr>
          <td>
            <div class="select select-sm" *ngIf="stageOptions?.length > 0">
              <mdb-select-2
                [(ngModel)]="setStageId"
                (selected)="stageSelected($event)"
                placeholder="Select Stage">
                <mdb-select-option *ngFor="let xoption of stageOptions" [value]="xoption.value">{{ xoption.label }}
                </mdb-select-option>
              </mdb-select-2>
            </div>
          </td>
          <td class="shrink-cell">
            <button (click)="createStage()" class="btn btn-sm btn-primary mr-2 ml-1" *ngIf="styleId != null">Add Stage
            </button>
          </td>
        </tr>
      </table>
    </div>
    <div class="flex-grow-1">
      <div class="mr-2 select select-sm" *ngIf="scanCustomerOptions?.length > 0">
        <mdb-select-2
          [outline]="true"
          [(ngModel)]="setCustomerId"
          (selected)="scanCustomerSelected($event)"
          placeholder="Preview Customer">
          <mdb-select-option *ngFor="let xoption of scanCustomerOptions" [value]="xoption.value">{{ xoption.label }}
          </mdb-select-option>
        </mdb-select-2>
      </div>
    </div>
  </div>
</div>

<div *ngIf="scanCustomer" class="mb-2" style="font-size: 13px; font-weight: 700;">
  <table class="w-100">
    <tr>
      <td>
        <span class="previewUrl">Preview URL: <a
          href="{{ scanPreviewUrlDisplay }}">{{ scanPreviewUrlDisplay }}</a></span>
      </td>
      <td class="text-right">
        <a href="#">Recent Scans</a> |
        <a href="#">View ScanVehicles</a> |
        <div class="link-style" (click)="adminUrl.adminScanCustomerWithId(scanCustomerId)">Edit ScanCustomer</div>
      </td>
    </tr>
  </table>
</div>

<div *ngIf="styleId && stageId && stage">
  <h2>Scan Stage Info</h2>

  <div class="widget widget-border widget-padding widget-darker">

    <div class="row">

      <div class="col-md-7">
        <div class="input-group input-group-sm">
          <div class="input-group-prepend">
            <div class="input-group-text"><label class="mb-0 pr-2" for="setStageLabel">Stage Name</label></div>
          </div>
          <input class="form-control" id="setStageLabel" name="stageLabel"
                 (change)="saveStageSetting('stageName', $event)"
                 [(ngModel)]="stage.stageName">
        </div>
      </div>
      <div class="col-md-5">
        <table class="w-100">
          <tbody>
          <tr>
            <td>
              <div class="d-flex flex-wrap" style="grid-gap: 15px;">

                <div *ngIf="usedByCount > 0">
                  <div class="link-style pb-1" *ngFor="let usedByStage of usedByStages">
                    <span (click)="loadStage(usedByStage.id)">Used by: {{ usedByStage.stageName }}</span>
                  </div>
                </div>
                <div>
                  <div class="input-group input-group-sm" *ngIf="usedByCount == 0">
                    <div class="input-group-prepend">
                      <div class="input-group-text pr-2">Search Entry Point?</div>
                    </div>
                    <div class="input-group-append">
                      <div class="custom-control custom-checkbox">
                        <input class="custom-control-input form-control"
                               (change)="saveIsEntryPoint()"
                               type="checkbox" [checked]="style?.entryPoint == stageId" id="isEntryPoint">
                        <label class="custom-control-label" for="isEntryPoint"></label>
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  <div class="input-group input-group-sm" *ngIf="usedByCount == 0">
                    <div class="input-group-prepend">
                      <div class="input-group-text pr-2">Detail Entry Point?</div>
                    </div>
                    <div class="input-group-append">
                      <div class="custom-control custom-checkbox">
                        <input class="custom-control-input form-control"
                               (change)="saveIsDetailEntryPoint()"
                               type="checkbox" [checked]="style?.detailEntryPoint == stageId" id="isDetailEntryPoint">
                        <label class="custom-control-label" for="isDetailEntryPoint"></label>
                      </div>
                    </div>
                  </div>
                </div>
                <div *ngIf="style?.detailEntryPoint == stageId">
                  <div class="input-group select select-sm">
                    <div class="input-group-append d-flex" style="width: 150px">
                      <mdb-select-2
                        [outline]="true"
                        class="flex-grow-1"
                        [(ngModel)]="setContentType"
                        placeholder="HTML/JSON"
                        (ngModelChange)="saveStageSetting('contentType',$event)">
                        <mdb-select-option *ngFor="let xoption of contentTypeOptions"
                                           [value]="xoption.value">{{ xoption.label }}
                        </mdb-select-option>
                      </mdb-select-2>
                    </div>
                  </div>
                </div>
              </div>
            </td>
            <td class="text-right">
              <button *ngIf="canDeleteStage(stageId)" class="btn btn-primary" (click)="deleteStage(stageId)">
                <i class="fa fa-trash-alt"></i> Delete
              </button>
            </td>
          </tr>
          </tbody>
        </table>

        <div class="mb-2 mt-2">

          <div class="input-group select select-sm">
            <div class="input-group-prepend">
              <div class="input-group-text pr-2">
                Preview Entry Point
              </div>
            </div>

            <div class="input-group-item">

              <mdb-select-2
                [outline]="true"
                class="flex-grow-1"
                style="width: 200px"
                [(ngModel)]="setPreviewEntryPoint"
                placeholder="Entry Point"
                (ngModelChange)="saveStageSetting('previewEntryPoint',$event)">
                <mdb-select-option *ngFor="let xoption of stageOptions"
                                   [value]="xoption.value">{{ xoption.label }}
                </mdb-select-option>
              </mdb-select-2>
            </div>
          </div>
        </div>

      </div>

      <div class="col-md-7">
        <div class="input-group input-group-sm select select-sm">

          <div class="input-group-prepend" *ngIf="active">
            <div class="input-group-text pr-2">
              Sends
            </div>
          </div>

          <div class="input-group-item">
            <mdb-select-2 [outline]="true"
                          class="flex-grow-1 stageInfoDropdown"
                          style="width: 150px"
                          (ngModelChange)="saveStageSetting('outputFieldId', $event)"
                          placeholder="Sends" [(ngModel)]="setOutputFieldId">
              <mdb-select-option *ngFor="let xoption of activeOptions"
                                 [value]="xoption.value">{{ xoption.label }}
              </mdb-select-option>
            </mdb-select-2>
          </div>

          <div class="input-group-item" *ngIf="stageOptions">
            <div class="input-group-text">
              to Stage
            </div>
          </div>

          <div class="input-group-item">
            <mdb-select-2 *ngIf="stageOptions" placeholder="to Stage"
                          [outline]="true"
                          [(ngModel)]="setNextScanStageId"
                          (ngModelChange)="saveStageSetting('nextScanStageId', $event)">
              <mdb-select-option *ngFor="let xoption of stageOptions"
                                 [value]="xoption.value">{{ xoption.label }}
              </mdb-select-option>
            </mdb-select-2>
          </div>

          <div class="input-group-append">
            <button *ngIf="stage?.nextScanStageId == null" class="btn btn-primary" (click)="createStage()">Create Next
            </button>
            <button *ngIf="stage?.nextScanStageId != null" class="btn btn-secondary"
                    (click)="loadStage(setNextScanStageId)"><i class="fa fa-arrow-right"></i></button>
          </div>
        </div>
      </div>

      <div class="col-md-5">

        <div class="input-group input-group-sm" *ngIf="createRecordOptions">

          <div class="input-group-prepend">
            <div class="input-group-text pr-2">Creates</div>
          </div>

          <div class="input-group-item select select-sm">
            <mdb-select-2
              *ngIf="createRecordOptions"
              [multiple]="true"
              [(ngModel)]="setCreateRecords"
              (ngModelChange)="saveStageSetting('createRecords', $event)"
              placeholder="Creates Records"
              class="flex-grow-1">
              <mdb-select-option *ngFor="let xoption of createRecordOptions"
                                 [value]="xoption.value">{{ xoption.label }}
              </mdb-select-option>
            </mdb-select-2>
          </div>

        </div>

      </div>

    </div>
  </div>
</div>

<div *ngIf="stageId > 0 && scanCustomerId > 0 && serviceId > 0 && styleId> 0 "
     class="form-inline flex-fill mt-2">

  <div class="flex-fill">
    <div class="input-group">
      <input class="form-control" name="previewUrl" [(ngModel)]="scanPreviewUrl">
      <div class="input-group-append">
        <div class="input-group-text">
          <a href="{{ scanPreviewUrl }}" target="_new">
            <i class="fa fa-link"></i>
          </a>
        </div>
      </div>
    </div>
  </div>

  <div class="flex-shrink-1">
    <input name="vehicleId" [(ngModel)]="scanVehicleId" class="form-control" placeholder="Vehicle ID"
           style="width: 110px">
  </div>

  <div class="flex-shrink">
    <button (click)="requestScanPreview()" [disabled]="requestingPreview" class="btn btn-primary ml-2 mr-2">
      Request Preview <span *ngIf="requestingPreview"><i class="fa fa-spin fa-spinner"></i></span>
    </button>
  </div>

  <div class="flex-shrink"> <!-- *ngIf="scanQueueId"> -->
    <button class="btn btn-secondary" (click)="queueScan()">
      <i class="fa fa-spin fa-spinner" *ngIf="waitingToQueue"></i> Queue Scan
    </button>
    <button class="ml-2 btn btn-secondary" (click)="viewCache()">Cached Response</button>
    <button class="ml-2 btn btn-secondary" (click)="refreshPreview()">Show Preview</button>
  </div>

</div>

<form #f="ngForm" (ngSubmit)="onSubmit(f)" method="post" *ngIf="styleId && stageId">

  <div class="d-flex flex-wrap">
    <div class="flex-grow-1">
      <h2 class="pagetitle mt-3">
        Active in this Stage
      </h2>
    </div>
    <div class="flex-grow-1 text-right pt-3" *ngIf="scanSampleUrl?.length > 0"
         style="font-size: 13px; font-weight: 600">
      <a target="_new" href="{{ scanSampleUrl }}">{{ scanSampleUrl }}</a>
    </div>
  </div>

  <!---  -->

  <div class="widget mt-2">

    <table class="table no-top-line">
      <thead>
      <tr>
        <th>Column</th>
        <th>Code</th>
        <th>Preview {{ scanQueueId }}</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let scanField of active">
        <td class="shrink-cell">
          <div>
            <strong>{{ scanField.description }}</strong>
          </div>
          <div>{{ scanField.fieldName }}</div>
          <div>Field: {{ scanField.id }}</div>
          <div>Seq: {{ scanField.sequence }}</div>
          <div *ngIf="scanField.newScan" class="newScan">Schedules new<br/>scan if sent<br/>to another stage</div>
        </td>
        <td>
          <div>
              <textarea style="position: relative; min-width: 500px; min-height: 80px;"
                        [(ngModel)]="scanCode[scanField.id]"
                        class="form-control"
                        name="scanfield-{{ scanField.id }}"></textarea>
          </div>
          <div class="pr-5 pt-2">
            <button value="Update" (click)="setScanConfig(scanField.id)" class="update-button btn btn-xs btn-secondary">
              Update
            </button>
          </div>
        </td>
        <td>

          <div style="max-height: 120px; overflow-y: auto">
            <div *ngIf="scanComment(scanSample, setStageId, scanField.id)?.length > 0">
              <b>{{ scanComment(scanSample, setStageId, scanField.id) }}</b>
            </div>

            <div *ngIf="scanField.isImage">
              <div *ngIf="scanCommentIsList(scanSample, setStageId, scanField.id)">
                <div *ngFor="let image of listOfImages(scanSample, setStageId, scanField.id)"
                     style="display: inline-block; margin-right: 2px; margin-bottom: 2px;">
                  <img src="{{ image }}" width="50">
                </div>
              </div>
              <div *ngIf="!scanCommentIsList(scanSample, setStageId, scanField.id)">
                <img src="{{ scanValue(scanSample, setStageId, scanField?.id) }}" alt="{{ scanField.fieldName }}"
                     width="50">
              </div>
            </div>

            <div *ngIf="scanField.isUrl">
              <a
                href="{{ scanValue(scanSample, setStageId, scanField?.id) }}">{{ scanValue(scanSample, setStageId, scanField?.id) }}</a>
            </div>

            <div *ngIf="!scanField.isUrl">
              {{ scanValue(scanSample, setStageId, scanField?.id) }}
            </div>

            <span *ngIf="scanSample && scanSample[setStageId] && scanSample[setStageId][scanField.id]">
            {{ scanSample[setStageId][scanField.id] }}
              <a *ngIf="scanField.isImage" href="{{ scanSample[setStageId][scanField.id] }}"><img
                alt="{{ scanField.fieldName }}" src="{{ scanSample[setStageId][scanField.id] }}" width="50"></a>
          </span>

            <span *ngIf="scanErrorField && scanErrorField[scanField.id]">
            <div class="text-danger"><small>{{ scanErrorField[scanField.id] }}</small></div>
          </span>
          </div>
        </td>
      </tr>
      </tbody>
    </table>

  </div>

  <h2 class="pagetitle mt-2">
    Inactive Code
  </h2>

  <div class="widget mt-2">

    <table class="table no-top-line">
      <thead>
      <tr>
        <th>Column</th>
        <th>Code</th>
        <th>Example</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let scanField of inactive">
        <td class="shrink-cell">
          <div>
            <strong>{{ scanField.description }}</strong>
          </div>
          <div>{{ scanField.fieldName }}</div>
          <div>Field: {{ scanField.id }}</div>
          <div>Seq: {{ scanField.sequence }}</div>
        </td>
        <td>
           <textarea style="min-width: 500px; min-height: 80px;"
                     class="form-control"
                     [(ngModel)]="scanCode[scanField.id]"
                     name="scanfield-{{ scanField.id }}"></textarea>
          <div class="pr-5 pt-2">
            <button value="Update" (click)="setScanConfig(scanField.id)" class="btn btn-secondary btn-xs">Update
            </button>
          </div>
        </td>
        <td>
          <span *ngIf="scanSample">
          {{ scanSample[scanField?.fieldName] }}
            <a *ngIf="scanField.isImage" href="{{ scanSample[scanField?.fieldName] }}"><img
              alt="{{ scanField.fieldName }}" src="{{ scanSample[scanField?.fieldName] }}" width="50"></a>
          </span>
        </td>
      </tr>
      </tbody>
    </table>

  </div>

  <h2 class="pagetitle mt-2">
    Used In Another Stage
  </h2>

  <div class="widget mt-2">

    <table class="table no-top-line">
      <thead>
      <tr>
        <th>Column</th>
        <th>Code</th>
        <th>Example</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let scanField of elsewhere">
        <td>
          <div>
            <strong>{{ scanField.description }}</strong>
          </div>
          <div>{{ scanField.fieldName }}</div>
          <div>Field
            {{ scanField.id }}
          </div>
        </td>
        <td>
          <textarea disabled style="min-width: 500px; min-height: 80px;"
                    class="form-control"
                    name="scanfield-{{ scanField.id }}-{{ scanField?.scanConfig?.id">{{ scanField?.scanConfig?.scanCode }}</textarea>
          <div><input type="submit" value="Update" class="btn btn-primary btn-xs"></div>
        </td>
        <td>
          <span *ngIf="scanSample">
          {{ scanSample[scanField?.fieldName] }}
            <a *ngIf="scanField.isImage" href="{{ scanSample[scanField?.fieldName] }}"><img
              alt="{{ scanField.fieldName }}" src="{{ scanSample[scanField?.fieldName] }}" width="50"></a>
          </span>
        </td>
      </tr>
      </tbody>
    </table>

  </div>

  <input type="hidden" name="scanCustomerID" value="{{ scanCustomerId }}">
  <input type="hidden" name="scanQueueId" value="{{ scanQueueId }}">
  <input type="hidden" name="action" value="scanConfEdit">
  <input type="hidden" name="update" value="1">
  <input type="hidden" name="serviceId" value="{{ serviceId }}">
  <input type="hidden" name="styleId" value="{{ styleId }}">
  <input type="hidden" name="stageId" value="{{ stageId }}">

</form>


<div style="overflow-y: auto" class="modal fade right" mdbModal #scanCacheModal="mdbModal" tabindex="-1" role="dialog"
     aria-labelledby="imageModal" aria-hidden="true">
  <div class="modal-dialog-full-width modal-dialog modal-fluid" role="document">
    <div class="modal-content-full-width modal-content">
      <div class="modal-body" style="font-size: 13px;">
        <button (click)="toggleCacheHtml()">JSON/HTML</button>

        <span *ngIf="cacheHtml" [innerHTML]="scanCache?.body"></span>
        <span *ngIf="!cacheHtml">
          {{ scanCache | json }}
        </span>
      </div>
    </div>
  </div>
</div>
