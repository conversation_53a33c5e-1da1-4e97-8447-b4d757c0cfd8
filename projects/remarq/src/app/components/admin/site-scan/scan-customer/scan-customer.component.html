<div class="d-flex flex-wrap">
  <div class="flex-fill">
    <h1 class="page-header">Scan Customers</h1>
  </div>
</div>

<div class="widget mt-1" id="locations-widget">

  <div class="pt-3 px-3">
    <form [formGroup]="createForm" (submit)="submitCreateForm()">
      <div class="d-flex flex-wrap">
        <div class="flex-grow-1">
          <div style="width: 100%" class="select select-sm">
            <mdb-select-2 [outline]="true"
                          (selected)="customerSelected($event)"
                          formControlName="customerId"
                          placeholder="Trade Customer">
              <mdb-select-option *ngFor="let customer of customerOptions"
                                 [value]="customer.value">{{ customer.label }}
              </mdb-select-option>
            </mdb-select-2>
          </div>
        </div>

        <div class="flex-shrink-1">
          <button class="btn btn-primary btn-sm ml-2" id="actionButton"
                  (click)="editScanCustomer(selectedCustomer.scanCustomerId)">
            <span *ngIf="scanCustomerConfigured">Edit</span>
            <span *ngIf="!scanCustomerConfigured">Create</span>
          </button>
        </div>
      </div>
    </form>

    <form [formGroup]="searchForm" (submit)="submitSearchForm()" class="mt-3">

      <div class="d-flex flex-wrap select select-sm">
        <div class="flex-grow-1">
          <div class="pr-2">
            <mdb-select-2 [outline]="true"
                          (selected)="loadScanStyles(sf.filterServiceId.value)"
                          formControlName="filterServiceId"
                          placeholder="Service">
              <mdb-select-option *ngFor="let scanService of scanServiceOptions"
                                 [value]="scanService.value">{{ scanService.label }}
              </mdb-select-option>
            </mdb-select-2>
          </div>
        </div>
        <div class="flex-grow-1">
          <div class="pr-2">
            <mdb-select-2 [outline]="true"
                          formControlName="filterScanStyleId"
                          placeholder="Style">
              <mdb-select-option *ngFor="let scanStyle of scanStyleOptions"
                                 [value]="scanStyle.value">{{ scanStyle.label }}
              </mdb-select-option>
            </mdb-select-2>
          </div>
        </div>
        <div class="flex-grow-1">
          <mdb-select-2 [outline]="true"
                        formControlName="filterStatusId"
                        placeholder="Status">
            <mdb-select-option *ngFor="let status of statusOptions" [value]="status.value">{{ status.label }}
            </mdb-select-option>
          </mdb-select-2>
        </div>
        <div class="flex-shrink-1">
          <button class="btn btn-secondary btn-sm ml-2" type="submit">Filter</button>
        </div>
      </div>
    </form>
  </div>

  <div class="mt-3">

    <table class="table table-striped table-hover table-condensed" mdbTable>
      <thead>
      <tr>
        <th>ID</th>
        <th>Customer</th>
        <th>Status</th>
        <th>Vehicles</th>
        <th>LastScan</th>
        <th>Service</th>
        <th>Style</th>
        <th>Pages</th>
        <th>IdxRequested</th>
        <th>Created</th>
        <th>Updated</th>
        <th>Actions</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let sc of scanCustomerList; let i = index" style="cursor: pointer"
          (click)="selectThenEditCustomer(sc.id)">
        <td>{{ sc.id }}</td>
        <td>
          <div class="table-line-1">{{ sc.companyName }}</div>
          <div *ngIf="sc.customer != null" class="table-line-2">{{ sc.customer?.customerName || sc.companyName }}</div>
        </td>
        <td>{{ statusName[sc.statusId] }}</td>
        <td (click)="$event.stopPropagation();viewVehicles(sc.id)"
            class="link-style vind vehiclesIndexed{{ sc.vehiclesIndexed }} text-center">
          <span *ngIf="sc.vehiclesIndexed > 0">{{ sc.vehiclesIndexed }}</span>
          <span *ngIf="!(sc.vehiclesIndexed > 0)"><i class="fa fa-times"></i></span>
        </td>
        <td>{{ sc.lastScan | date: "d/M/yy, h:mma" }}</td>
        <td>{{ sc?.scanStyle?.scanService.serviceName }}</td>
        <td>{{ sc?.scanStyle?.styleName }}</td>
        <td>{{ sc.maxPages }}</td>
        <td>{{ sc.indexRequested | date: "d/M/yy, h:mma" }}</td>
        <td>{{ sc.added | date: "d/M/yy, h:mma" }}</td>
        <td>{{ sc.updated | date: "d/M/yy, h:mma" }}</td>
        <td>
          <button class="btn btn-xs btn-danger-outline">Delete</button>
        </td>
      </tr>
      </tbody>
    </table>

  </div>
</div>

<div mdbModal #basicModal="mdbModal" class="addVehicleModal modal fade" tabindex="-1"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true" [config]="{backdrop: 'static'}">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        Scan Customer
      </div>
      <form [formGroup]="form" (submit)="saveChanges()">
        <div class="modal-body">
          <table cellpadding="3" class="w-100 select select-sm pb-2">
            <tr>
              <td class="label shrink-cell">Trade Customer:</td>
              <td>
                <mdb-select-2 [outline]="true"
                              formControlName="customerId"
                              placeholder="Trade Customer">
                  <mdb-select-option *ngFor="let customer of customerOptions"
                                     [value]="customer.value">{{ customer.label }}
                  </mdb-select-option>
                </mdb-select-2>
              </td>
            </tr>
            <tr>
              <td class="label shrink-cell">Company Name:</td>
              <td class="input-sm md-form"><input mdbInput class="form-control mb-0" formControlName="companyName"></td>
            </tr>
            <tr>
              <td class="label shrink-cell">ScanService:</td>
              <td>
                <mdb-select-2 [outline]="true"
                              (selected)="loadScanStyles(f.scanServiceId.value)"
                              formControlName="scanServiceId"
                              placeholder="Scan Service">
                  <mdb-select-option *ngFor="let scanService of scanServiceOptions"
                                     [value]="scanService.value">{{ scanService.label }}
                  </mdb-select-option>
                </mdb-select-2>
              </td>
            </tr>
            <tr *ngIf="f.scanServiceId != null">
              <td class="label shrink-cell">ScanStyle:</td>
              <td>
                <mdb-select-2 [outline]="true"
                              formControlName="scanStyleId"
                              (selected)="scanStyleChosen(f.scanStyleId.value)"
                              placeholder="Scan Style">
                  <mdb-select-option *ngFor="let scanStyle of scanStyleOptions"
                                     [value]="scanStyle.value">{{ scanStyle.label }}
                  </mdb-select-option>
                </mdb-select-2>
              </td>
            </tr>
            <tr *ngIf="scanStyle?.requestVarName1.length > 0">
              <td class="label shrink-cell">{{ scanStyle.requestVarName1 }}:</td>
              <td class="md-form input-sm pt-2"><input mdbInput class="form-control mb-0"
                                                       formControlName="requestVarValue1"></td>
            </tr>
            <tr *ngIf="scanStyle?.requestVarName2.length > 0">
              <td class="label shrink-cell">{{ scanStyle.requestVarName2 }}:</td>
              <td class="md-form input-sm pt-2"><input mdbInput class="form-control mb-0"
                                                       formControlName="requestVarValue2"></td>
            </tr>
            <tr *ngIf="scanStyle?.requestVarName3.length > 0">
              <td class="label shrink-cell">{{ scanStyle.requestVarName3 }}:</td>
              <td class="md-form input-sm pt-2"><input mdbInput class="form-control mb-0"
                                                       formControlName="requestVarValue3"></td>
            </tr>
            <tr>
              <td class="label shrink-cell">HomePage:</td>
              <td class="md-form input-sm pt-2"><input mdbInput class="form-control mb-0" formControlName="homepage">
              </td>
            </tr>
            <tr>
              <td class="label shrink-cell">LinkPrefix:</td>
              <td class="md-form input-sm pt-2"><input mdbInput class="form-control mb-0" formControlName="domainName">
              </td>
            </tr>
            <tr>
              <td class="label shrink-cell">Scan URL:</td>
              <td class="md-form input-sm pt-2"><input mdbInput class="form-control mb-0" formControlName="scanUrl">
              </td>
            </tr>
            <tr>
              <td class="label shrink-cell">Max Pages:</td>
              <td class="md-form input-sm pt-2"><input mdbInput type=number class="form-control mb-0"
                                                       formControlName="maxPages"></td>
            </tr>
            <tr>
              <td class="label shrink-cell">Comments:</td>
              <td class="md-form input-sm pt-2"><input mdbInput class="form-control mb-0" formControlName="comments">
              </td>
            </tr>
            <tr>
              <td class="label shrink-cell">Status:</td>
              <td>
                <mdb-select [outline]="true"
                            [options]="statusOptions"
                            formControlName="statusId"
                            placeholder="Status"></mdb-select>
              </td>
            </tr>
            <tr>
              <td class="label shrink-cell">Custom Header:</td>
              <td>
                <div class="d-flex mt-1 grid-gap-5" *ngFor="let customHeader of headers().controls; let i = index"
                     [formGroup]="customHeader">
                  <div class="flex-grow-1 md-form input-sm mb-0">
                    <input mdbInput class="w-100 form-control" placeholder="Header" formControlName="label">
                  </div>
                  <div class="flex-grow-1 md-form input-sm mb-0">
                    <input mdbInput class="w-100 form-control" placeholder="Value" formControlName="value">
                  </div>
                  <div class="flex-shrink-1">
                    <button class="btn btn-sm btn-outline-danger" (click)="removeHeader(customHeader, i)">Delete
                    </button>
                  </div>
                </div>
                <div class="mb-1">
                  <div class="btn btn-xs btn-secondary" (click)="addCustomHeader()">Add</div>
                </div>
              </td>
            </tr>
          </table>
        </div>
        <div class="modal-footer">
          <table class="w-100">
            <tr>
              <td>
                <button class="btn btn-sm btn-secondary" (click)="configureScan()"><i class="fa fa-cogs"></i> Configure
                  Scan
                </button>
              </td>
              <td>
                <div class="text-right">
                  <div (click)="basicModal.hide()" class="btn btn-sm btn-tertiary-outline mr-2">Cancel</div>
                  <button type="submit" class="btn btn-sm btn-tertiary">Save</button>
                </div>
              </td>
            </tr>
          </table>

        </div>
      </form>
    </div>
  </div>
</div>
