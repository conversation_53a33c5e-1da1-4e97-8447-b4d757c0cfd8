import {Component, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild} from '@angular/core';
import {Router} from '@angular/router';
import {ScanStatsDTO} from '../../../../global/interfaces';
import {ScanningService, AdminUrlService} from "../../services";


@Component({
  selector: 'app-admin-scan-dashboard',
  templateUrl: './scan-dashboard.component.html',
  styleUrls: ['./scan-dashboard.component.scss'],
  providers: []
})
export class ScanDashboardComponent implements OnInit, OnDestroy {

  stats: ScanStatsDTO;
  refreshing: any = false;

  constructor(
    private router: Router,
    private scanService: ScanningService,
    public adminUrl: AdminUrlService,
  ) {

  }

  async ngOnInit() {
    this.getScanStats().then(() => {
    });
  }

  getScanStats() {

    return this.scanService.getScanStats().then((response) => {
      this.stats = response;
    });

  }

  ngOnDestroy(): void {
  }

  refreshStats() {

    this.refreshing = true;

    this.scanService.refreshScanStats().then((response) => {

      this.getScanStats().then(() => {

        this.refreshing = false;
      });
    });
  }
}
