<div class="d-flex flex-wrap">
  <div class="flex-fill">
    <h1>Scan <PERSON>rs</h1>
    <h2>Style {{ scanStyle?.styleName }}</h2>
  </div>
</div>

<div class="widget">

  <table class="table error-table table-condensed" mdbTable>
    <thead>
    <tr>
      <th>View</th>
      <th>Customer</th>
      <th>Field</th>
      <th>Error</th>
      <th>Count</th>
      <th>Last Error</th>
      <th class="shrink-cell">Clear Errors</th>
    </tr>
    </thead>
    <tbody>
    <ng-container *ngFor="let style of errors">
      <tr>
        <td rowspan="2"><a target="_new"
                           href="{{ adminUrlService.scanConfigure(true, style.scanStyleId, style.scanStageId, style.scanCustomerId) }}"
                           class="btn btn-xs btn-outline-primary"><i class="fa fa-cogs"></i> Config</a></td>
        <td>{{ style.companyName }}</td>
        <td>{{ scanField(style.scanFieldId) }}</td>
        <td>{{ style.reason }}</td>
        <td>{{ style.errorCount }}</td>
        <td>{{ style.lastError | date: "dd/MM/yy HH:mm" }}</td>
        <td class="shrink-cell">
          <div class="btn btn-xs btn-outline-primary btn-block"
               (click)="clearErrors('customer', style.scanCustomerId,  style.scanFieldId)">Clear Customer
          </div>
        </td>
      </tr>
      <tr>
        <td colspan="5" style="font-size: 12px;">URL <a href="_new" href="{{ style.url }}">{{ style.url }}</a></td>
        <td>
          <div class="btn btn-xs btn-outline-primary btn-block"
               (click)="clearErrors('style', style.scanStyleId, style.scanFieldId)">Clear Style
          </div>
        </td>
      </tr>
    </ng-container>
    </tbody>
  </table>
</div>
