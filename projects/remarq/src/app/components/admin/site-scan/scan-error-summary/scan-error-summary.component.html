<div class="d-flex flex-wrap">
  <div class="flex-fill">
    <h1 class="page-header"><PERSON>an <PERSON></h1>
  </div>
</div>

<div class="widget">

  <table class="table table-condensed" mdbTable>
    <thead>
    <tr>
      <th>Service</th>
      <th>Style</th>
      <th>Customers</th>
      <th>Errors</th>
      <th>Last Error</th>
    </tr>
    </thead>
    <tbody>
    <tr *ngFor="let style of errors" (click)="adminUrlService.adminScanErrorDetail(style.scanStyleId, false)">
      <td>{{ style.scanServiceName }}</td>
      <td>{{ style.scanStyleName }}</td>
      <td>{{ style.customerCount }}</td>
      <td>{{ style.errorCount }}</td>
      <td>{{ style.lastError | date: "dd/MM/yy HH:mm" }}</td>
    </tr>
    </tbody>
  </table>
</div>


