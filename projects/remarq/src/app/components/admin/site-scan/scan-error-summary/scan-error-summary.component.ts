import {Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild} from '@angular/core';
import {AdminUrlService, ScanningService} from "../../services";
import {ScanErrorSummaryDTO} from 'projects/common/interfaces';

@Component({
  selector: 'app-admin-scan-error-summary',
  templateUrl: './scan-error-summary.component.html',
  styleUrls: ['./scan-error-summary.component.scss'],
  providers: []
})
export class ScanErrorSummaryComponent implements OnInit, OnDestroy {

  errors: ScanErrorSummaryDTO[] = [];

  constructor(private scanningService: ScanningService, public adminUrlService: AdminUrlService) {
  }

  async ngOnInit() {

    this.loadErrors();
  }

  async ngOnDestroy() {
  }

  loadErrors = () => {

    this.scanningService.getScanErrorSummary({}).then((x) => {

      this.errors = x;

    });
  }
}

