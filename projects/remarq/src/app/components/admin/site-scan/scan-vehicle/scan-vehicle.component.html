<div class="d-flex flex-wrap">
  <div class="flex-fill">
    <h1 class="page-header">Scan Vehicles</h1>
  </div>
</div>

<div class="widget widget-border widget-padding mt-1 pt-3 pb-3 mb-3" id="filter-widget">
  Filters go here
</div>

<div class="widget widget-border widget-padding mt-1 pt-3 pb-3 mb-3" id="vehicles-widget">

  <div class="text-right mb-2">
    <ng-toggle
      [labels]="{ checked: 'Values On', unchecked: 'Values Off' }"
      [width]="100"
      [(ngModel)]="showValues"></ng-toggle>
  </div>

  <ng-template #noValues>
    <table class="w-100 table table-compressed vehicle-table">
      <thead>
      <td>Id</td>
      <td>V</td>
      <td>I</td>
      <td>Mk</td>
      <td>Md</td>
      <td>Dv</td>
      <td>Ft</td>
      <td>Tr</td>
      <td>Bd</td>
      <td>Odo</td>
      <td>Img</td>
      <td>Imgs</td>
      </thead>
      <tbody>
      <tr *ngFor="let vehicle of vehicles">
        <td>{{ vehicle.id }}</td>
        <td><i class="fa" [class]="vehicle.vrm?.length > 0 ? 'fa-check' : 'fa-times'"></i></td>
        <td><i class="fa" [class]="vehicle.uniqueId?.length > 0 ? 'fa-check' : 'fa-times'"></i></td>
        <td><i class="fa" [class]="vehicle.makeName?.length > 0 ? 'fa-check' : 'fa-times'"></i></td>
        <td><i class="fa" [class]="vehicle.modelName?.length > 0 ? 'fa-check' : 'fa-times'"></i></td>
        <td><i class="fa" [class]="vehicle.derivName?.length > 0 ? 'fa-check' : 'fa-times'"></i></td>
        <td><i class="fa" [class]="vehicle.fuelTypeName?.length > 0 ? 'fa-check' : 'fa-times'"></i></td>
        <td><i class="fa" [class]="vehicle.transmissionTypeName?.length > 0 ? 'fa-check' : 'fa-times'"></i></td>
        <td><i class="fa" [class]="vehicle.bodyTypeName?.length > 0 ? 'fa-check' : 'fa-times'"></i></td>
        <td><i class="fa" [class]="vehicle.odometer > 0 ? 'fa-check' : 'fa-times'"></i></td>
        <td><i class="fa" [class]="vehicle.primaryImageUrl?.length > 0 ? 'fa-check' : 'fa-times'"></i></td>
        <td><i class="fa" [class]="vehicle.imageCount?.length > 0 ? 'fa-check' : 'fa-times'"></i></td>
      </tr>
      </tbody>
    </table>
  </ng-template>

  <div *ngIf="showValues; else noValues">
    <table class="w-100 table table-compressed vehicle-table">
      <thead>
      <td>Id</td>
      <td>Customer</td>
      <td>VRM</td>
      <td>TheirId</td>
      <td>Make</td>
      <td>Model</td>
      <td>Deriv</td>
      <td>FuelType</td>
      <td>Transmission</td>
      <td>Body</td>
      <td>Odometer</td>
      <td>Primary</td>
      <td>Images</td>
      </thead>
      <tbody>
      <tr *ngFor="let vehicle of vehicles">
        <td>{{ vehicle.id }}</td>
        <td>{{ customerName[vehicle.scanCustomerId] }}</td>
        <td>{{ vehicle.vrm }}</td>
        <td>{{ vehicle.uniqueId }}</td>
        <td>{{ vehicle.makeName }}</td>
        <td>{{ vehicle.modelName }}</td>
        <td>{{ vehicle.derivName }}</td>
        <td>{{ vehicle.fuelTypeName }}</td>
        <td>{{ vehicle.transmissionTypeName }}</td>
        <td>{{ vehicle.bodyTypeName }}</td>
        <td>{{ vehicle.odometer }}</td>
        <td>
          <div *ngIf="vehicle.primaryImageUrl"><img src="{{ vehicle.primaryImageUrl }}" class="primary-image"></div>
          <div *ngIf="!vehicle.primaryImageUrl">N/A</div>
        </td>
        <td>{{ vehicle.imageCount }}</td>
      </tr>
      </tbody>
    </table>
  </div>
</div>


