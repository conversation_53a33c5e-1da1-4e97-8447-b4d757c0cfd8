import {AfterViewInit, Component, On<PERSON><PERSON>roy, OnInit} from "@angular/core";
import {ActivatedRoute, Router} from "@angular/router";
import {AdminUrlService} from "../services";

@Component({
  selector: 'app-scan',
  templateUrl: './scan.component.html',
  styleUrls: ['./scan.component.scss']
})
export class ScanComponent implements OnInit, OnDestroy, AfterViewInit {

  menuItems: {}[] = [];

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private url: AdminUrlService,
  ) {

    this.menuItems = [
      {itemLabel: "Dashboard", itemRoute: this.url.adminScanDashboard(true)},
      {itemLabel: "Configure", itemRoute: this.url.adminScanConfigure(true)},
      {itemLabel: "ScanFields", itemRoute: this.url.adminScanField(true)},
      {itemLabel: "Services", itemRoute: this.url.adminScanService(true)},
      {itemLabel: "Styles", itemRoute: this.url.adminScanStyle(true)},
      {itemLabel: "ScanCustomers", itemRoute: this.url.adminScanCustomer(true)},
      {itemLabel: "Queue", itemRoute: this.url.adminScanQueue(true)},
    ];
  }

  ngOnDestroy(): void {
    throw new Error("Method not implemented.");
  }

  async ngOnInit() {

  }

  async ngAfterViewInit() {
  }
}

