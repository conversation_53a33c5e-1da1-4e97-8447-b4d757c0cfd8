import {Component, Input, <PERSON><PERSON><PERSON>roy, OnInit, SecurityContext, ViewChild} from "@angular/core";
import {AdminMovementService} from "../services";
import {ModalDirective} from "ng-uikit-pro-standard";
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, SafeUrl} from "@angular/platform-browser";
import {BookMovementDTO, DealDTO, DealSearchDTO, MovementSearchDTO} from "../../../global/interfaces";
import {DealService} from "../../../services";

@Component({
  selector: 'app-vehicle-movement',
  templateUrl: './vehicle-movement.component.html',
  styleUrls: ['./vehicle-movement.component.scss'],
})
export class VehicleMovementComponent implements OnInit, OnDestroy {

  @Input() advertId: string;

  loading: boolean = true;
  movements: any[] = [];

  constructor(private movementService: AdminMovementService) {}

  ngOnInit(): void {
    this.loadMovements();
  }

  ngOnDestroy() {
  }

  private loadMovements(): void {
    this.loading = true;
    const dto = {} as MovementSearchDTO;
    this.movementService.search(dto).then((x) => {
      this.movements = x.results;
    }).catch((error) => {
      console.error('Error loading movements:', error);
      // Handle error (e.g., show error message to user)
    }).finally(() => {
      this.loading = false;
    });
  }
}
