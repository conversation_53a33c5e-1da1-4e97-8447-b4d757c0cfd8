<h1 class="page-header">Vehicle Checker
  <span class="m-1">
      <button class="btn-xs btn btn-secondary" (click)="selectAll()">
        <i class="fa fa-plus-circle"></i> Select All</button>
    </span>

  <span class="m-1">
      <button class="btn-xs btn btn-secondary" (click)="selectNone()">
        <i class="fa fa-minus-circle"></i> Select None</button>
    </span>

  <span class="m-1">
      <button class="btn-xs btn btn-tertiary-outline" (click)="selectInverse()">
        <i class="fa fa-window-restore"></i> Invert Selection</button>
  </span>

  <span class="m-1">
    <button [disabled]="isInOp" class="btn-xs btn btn-danger" (click)="deleteSelection()">
      Delete Selection
    </button>
  </span>

  <span class="m-1">
    <button [disabled]="isInOp" class="btn-xs btn btn-tertiary-outline" (click)="motSelection()">
      MOT Selection
    </button>
  </span>

  <span class="m-1">
    <button [disabled]="isInOp" class="btn-xs btn btn-tertiary-outline" (click)="updateProvenanceSelection()">
      Update Provenance
    </button>
  </span>

  <span class="m-1">
    <button [disabled]="isInOp" class="btn-xs btn btn-tertiary-outline" (click)="relistSelected()">
      Relist Selected
    </button>
  </span>

  <span class="m-1">
    <span class="switch blue-white-switch">
        <span class="switch-label">Re-listable Only &nbsp;</span>
      <ng-toggle [(ngModel)]="showRelistable" (ngModelChange)="onShowRelistableChange()"></ng-toggle>
    </span>
  </span>

  <span class="m-1">
    <span class="switch blue-white-switch">
        <span class="switch-label">No MOT Only &nbsp;</span>
      <ng-toggle [(ngModel)]="showNoMOT" (ngModelChange)="onShowNoMOTChange()"></ng-toggle>
    </span>
  </span>
</h1>

<ng-container [ngTemplateOutlet]="paginator"></ng-container>

<div *ngIf="isLoading" class="text-center mt-5 mb-5">
  <p>Loading vehicle data... <i class="la la-spinner la-spin"></i></p>
</div>
<div *ngIf="isBusy" class="text-center mt-5 mb-5">
  <p>Please wait... <i class="la la-spinner la-spin"></i></p>
</div>
<div *ngIf="isInOp" class="text-center mt-5 mb-5">
  <p>Please wait... <i class="la la-spinner la-spin"></i></p>
</div>

<div class="widget mt-1 px-2 py-3" id="vehicle-checker-widget">

  <form [formGroup]="form">

    <div style="position: sticky; top: 0;">
      <div class="d-flex flex-wrap grid-gap-10">
        <div class="flex-shrink-1">
          <div class="md-form input-sm">
            <input class="form-control float-label inline-block"
                   type="text" mdbInput name="vrm"
                   formControlName="vrm" id="vrm"/>
            <label for="vrm">VRM</label>
          </div>
        </div>
        <div class="flex-shrink-1">
          <div class="select select-sm">
            <mdb-select-2 [outline]="true"
                          formControlName="customerId"
                          style="min-width: 300px"
                          [placeholder]="'Customer'">
              <mdb-select-option *ngFor="let customer of customers" [value]="customer.value">{{ customer.label }}
              </mdb-select-option>
            </mdb-select-2>
          </div>
        </div>
        <div class="flex-shrink-1">
          <div>
            <button class="btn btn-inline-colour" (click)="searchVehicles()"><i class="fa fa-search"></i></button>
          </div>
        </div>
      </div>
    </div>
  </form>

  <table class="table table-striped table-hover vehicle-list table-narrow"
         mdbTable mdbTableScroll scrollX="true" maxWidth="400"
         *ngIf="!isLoading">
    <thead>
    <tr>
      <th>#</th>
      <th class="stick-column">VRM</th>
      <th *ngIf="allowDelete"></th>
      <th>VehType</th>
      <th>Make</th>
      <th>Model</th>
      <th>Deriv</th>
      <th>Customer</th>
      <th>Body</th>
      <th>Fuel</th>
      <th>Trans</th>
      <th>Plate</th>
      <th>Advert</th>
      <th>AdStatus</th>
      <th>SphLink</th>
      <th>Sale</th>
      <th>V.Address</th>
      <th>A.Address</th>
      <th>Platform</th>
      <th>SaleType</th>
      <th>MOT</th>
    </tr>
    </thead>
    <tbody>
    <ng-template ngFor let-vehicle [ngForOf]="vehicles" let-i="index">
      <tr>
        <td rowspan="2"
            (click)="url.advertEdit(vehicle.advertId);"
            class="checker-column-1">{{ i + 1 }}
        </td>
        <td class="row-content stick-column align-middle">
          <a class="vrm" [href]="url.advertView(vehicle.advertId, true)" target="_blank">{{ vehicle.vrm }}</a>
          <div *ngIf="allowDelete" class="pl-3">
            <mdb-checkbox mdbInput class="mb-1"
                          [(ngModel)]="vehicle.selected">
            </mdb-checkbox>
          </div>
          <div
            class="adStatus adStatus{{ vehicle?.advertStatus }}">{{ vehicle?.advertId == null ? 'Draft' : statusName[vehicle?.advertStatus] }}
          </div>
        </td>
        <td *ngIf="allowDelete" class="align-middle">
          <div class="mb-2">
            <button [disabled]="isRelisting" class="btn-xs btn btn-outline-primary" (click)="relistVehicle(vehicle)"
                    style="min-width: 70px;">
              Relist
            </button>
          </div>
          <div>
            <button [disabled]="isInOp" class="btn-xs btn btn-primary" (click)="createBrokerage(vehicle)"
                    style="min-width: 70px;">
              Broker
            </button>
          </div>
        </td>
        <td class="row-content align-middle">
          <i class="fa" [ngClass]="(vehicle?.vehicleTypeId) ? 'fa-check' : 'fa-times'"></i>
          <div class="desc">{{ vehicle?.vehicleTypeName }}</div>
        </td>
        <td class="row-content align-middle">
          <i class="fa" [ngClass]="(vehicle?.makeId) ? 'fa-check' : 'fa-times'"></i>
          <div class="desc">{{ vehicle?.makeName || "&nbsp;" }}</div>
        </td>
        <td class="row-content align-middle">
          <i class="fa" [ngClass]="(vehicle?.modelId) ? 'fa-check' : 'fa-times'"></i>
          <div class="desc">{{ vehicle?.modelName || "&nbsp;" }}</div>
        </td>
        <td class="row-content align-middle">
          <i class="fa" [ngClass]="(vehicle?.derivId) ? 'fa-check' : 'fa-times'"></i>
          <div class="desc">{{ vehicle?.derivName || "&nbsp;" }}</div>
        </td>
        <td class="row-content align-middle">
          <i class="fa" [ngClass]="(vehicle?.customerId) ? 'fa-check' : 'fa-times'"></i>
          <div class="desc">{{ vehicle?.customerName || "&nbsp;" }}</div>
        </td>
        <td class="row-content align-middle">
          <i class="fa" [ngClass]="(vehicle?.bodyTypeId) ? 'fa-check' : 'fa-times'"></i>
          <div class="desc">{{ vehicle?.bodyTypeName || "&nbsp;" }}</div>
        </td>
        <td class="row-content align-middle">
          <i class="fa" [ngClass]="(vehicle?.fuelTypeId) ? 'fa-check' : 'fa-times'"></i>
          <div class="desc">{{ vehicle?.fuelTypeName || "&nbsp;" }}</div>
        </td>
        <td class="row-content align-middle">
          <i class="fa" [ngClass]="(vehicle?.transmissionTypeId) ? 'fa-check' : 'fa-times'"></i>
          <div class="desc">{{ vehicle?.transmissionTypeName || "&nbsp;" }}</div>
        </td>
        <td class="row-content align-middle"><i class="fa" [ngClass]="(vehicle?.plateId) ? 'fa-check' : 'fa-times'"></i>
          <div class="desc">{{ vehicle?.plateName || "&nbsp;" }}</div>
        </td>

        <td class="row-content align-middle"><i class="fa"
                                                [ngClass]="(vehicle?.advertId) ? 'fa-check' : 'fa-times'"></i>
          <div>&nbsp;</div>
        </td>


        <td class="row-content align-middle" [mdbTooltip]="AdvertStatusEnum[vehicle?.advertStatus]"><i class="fa"
                                                                                                       [ngClass]="(vehicle?.advertStatus == 1) ? 'fa-check' : 'fa-times'"></i>
          <div class="desc">{{ vehicle?.advertStatus || "&nbsp;" }}</div>
        </td>

        <td class="row-content align-middle"><i class="fa"
                                                [ngClass]="(vehicle?.sphLinkId) ? 'fa-check' : 'fa-times'"></i>
          <div class="desc">{{ vehicle?.sphLinkId }}</div>
        </td>

        <td class="row-content align-middle"><i class="fa" [ngClass]="(vehicle?.saleId) ? 'fa-check' : 'fa-times'"></i>
          <div>&nbsp;</div>
        </td>

        <td class="row-content align-middle">
          <i class="fa" [ngClass]="(vehicle?.addressId) ? 'fa-check' : 'fa-times'"></i>
          <div class="desc">{{ vehicle?.addressName || "&nbsp;" }}</div>
        </td>

        <td class="row-content align-middle"><i class="fa"
                                                [ngClass]="(vehicle?.advertAddressId) ? 'fa-check' : 'fa-times'"></i>
          <div>&nbsp;</div>
        </td>

        <td class="row-content align-middle">
          <i class="fa" [ngClass]="(vehicle?.platformId) ? 'fa-check' : 'fa-times'"></i>
          <div class="desc">{{ vehicle?.platformName }}</div>
        </td>
        <td class="row-content align-middle">
          <i class="fa" [ngClass]="(vehicle?.saleTypeId) ? 'fa-check' : 'fa-times'"></i>
          <div class="desc">{{ vehicle?.saleTypeName }}</div>
        </td>
        <td class="row-content align-middle">
          <i class="fa" [ngClass]="(vehicle?.motId) ? 'fa-check' : 'fa-times'"></i>
        </td>
        <!-- TODO: check that vehicle has no live advert before showing this option -->
      </tr>
      <tr>
        <td colspan="20" style="font-size: 0.7rem; ">
          Vehicle: {{ vehicle?.id }} &nbsp; Advert: {{ vehicle?.advertId }} &nbsp; Customer: {{ vehicle?.customerId }}
        </td>
      </tr>
    </ng-template>
    </tbody>
  </table>

  <ng-container [ngTemplateOutlet]="paginator"></ng-container>

  <ng-template #paginator>
    <div class="mt-2 d-flex flex-wrap">
      <div class="flex-grow-1">&nbsp;</div>
      <div class="flex-shrink-1">

        <ul class="pagination" *ngIf="pages > 0">
          <li><a [ngClass]="(page <= 1) ? 'disabled' : ''" (click)="fetchPage(page - 1)">&laquo; Prev</a></li>
          <li *ngFor="let a of [].constructor(pages); let i = index" (click)="fetchPage(i + 1)"
              [class]="(i + 1) == page ? 'current' : ''"><a
            class="">{{ i + 1 }}</a></li>
          <li><a [ngClass]="(page >= pages) ? 'disabled' : ''" (click)="fetchPage(page + 1)">Next &raquo;</a></li>
        </ul>
        <!--      <div style="text-align: center; font-size: 0.8em"> Total Pages: {{ pages }}</div>-->
      </div>
    </div>

  </ng-template>

</div>

<app-delete-confirm-modal
  [showDialog]="showDeleteConfirm"
  (modalClosed)="showDeleteConfirm = false"
  (confirmDelete)="confirmDeleteVehicle()"
  (cancelDelete)="showDeleteConfirm = false;"></app-delete-confirm-modal>
