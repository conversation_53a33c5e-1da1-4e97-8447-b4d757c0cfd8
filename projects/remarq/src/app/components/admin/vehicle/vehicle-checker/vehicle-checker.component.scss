.side-label {
  font-size: 12px;
}

#vehicle-checker-widget {

  form {
    background-color: var(--widgetBgColour) !important;
    z-index: 999;
    position: sticky;
    top: 0;
  }

}

.switch-label {
  font-size: 0.875rem;
  font-weight: 400;
}

.addVehicleModal {

  .confirm-vrm {
    font-size: 2rem;
    display: inline-block;
    line-height: 2rem;
    padding: 5px 10px;
    border-radius: 8px;
    border: solid 1px #CEB83B;
  }

  div.make-model-deriv {
    padding-right: 10px;
  }

  .make-model {
    font-size: 25px;
    font-weight: bold;
    margin-top: 7px;
  }

  .deriv {
    font-size: 14px;
    font-weight: 400;
    color: #777;
  }

  .confirm-text {
    margin-top: 8px;
    font-weight: 600;
  }
}

.vrm {
  background-color: var(--vrmBackgroundColour) !important;
  font-family: var(--vrmFontFamily);
  font-size: 0.75rem;
  font-weight: bold;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  display: block;
}

table th {
  text-align: center;
}

.vehicle-list {


  .make-model {
    font-weight: 500;
    line-height: 1rem;
  }

  .deriv-name {
    font-size: 0.9rem;
  }
}


.fa-times {

  color: var(--dangerColour);

}

.fa-check {

  color: var(--successColour);

}

.row-content {

  text-align: center;
  vertical-align: middle;

  .desc {

    font-size: 11px;
    white-space: nowrap;
    overflow-x: hidden;
    max-width: 80px;
    text-overflow: ellipsis;
    margin: 0 auto;

  }
}

.checker-column-1 {
  cursor: pointer;
  background-color: rgb(51, 51, 51);
  color: white;
  font-size: 1.1em;
  text-align: center;
  vertical-align: middle;
}

.stick-column {
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  background: #ffff !important;
  z-index: 9;
}

.adStatus {
  font-size: 11px;
  background-color: #e9fdee;
  font-weight: 500;
  line-height: 1rem;
}

.adStatus1 {
  background-color: #E9FDEE;
}

.pagination {
  margin-bottom: 0 !important;
}
