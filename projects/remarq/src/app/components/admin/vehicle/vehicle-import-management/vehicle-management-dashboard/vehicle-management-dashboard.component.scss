// Filters Container - Flexbox Layout
.filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: flex-start;
  margin-bottom: 1.5rem;

  .filter-item {
    display: flex;
    flex-direction: column;
    min-width: 200px;
    flex: 1;
    max-width: 300px;

    .filter-label {
      font-size: 0.875rem;
      font-weight: 500;
      color: #495057;
      margin-bottom: 0.25rem;
      white-space: nowrap;
    }

    .form-control {
      height: 38px;
    }

    .select {
      // Ensure the select wrapper aligns with other form controls
      display: flex;
      align-items: stretch;

      mdb-select-2 {
        height: 38px;
        width: 100%;
        display: block;

        .select-wrapper {
          margin-bottom: 0;
          height: 38px;
          display: flex;
          align-items: stretch;
        }

        .form-control {
          height: 38px;
          border: 1px solid #ced4da;
          border-radius: 0.375rem;
          line-height: 1.5;
          padding: 0.375rem 2.25rem 0.375rem 0.75rem;

          //&:focus {
          //  border-color: #007bff;
          //  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
          //}
        }

        .mdb-select-arrow,
        .mdb-select-toggle {
          height: calc(100% - 2px);
          top: 1px;
          right: 1px;
          border-left: 1px solid #ced4da;
          width: 36px;
          display: flex;
          align-items: center;
          justify-content: center;

          &::before {
            content: '\f107';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            color: #6c757d;
            font-size: 14px;
          }
        }
      }
    }
  }

  // Responsive behavior
  @media (max-width: 768px) {
    .filter-item {
      min-width: 150px;
      max-width: 100%;
    }
  }

  @media (max-width: 576px) {
    .filter-item {
      min-width: 100%;
      max-width: 100%;
    }
  }
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;

  h1 {
    margin: 0;
    font-size: 1.75rem;
    font-weight: 600;
  }
}

.filter-tabs {
  margin-bottom: 1.5rem;

  .nav-tabs {
    border-bottom: 1px solid #dee2e6;

    .nav-link {
      cursor: pointer;
      color: #495057;
      border: 1px solid transparent;
      border-top-left-radius: 0.25rem;
      border-top-right-radius: 0.25rem;
      padding: 0.75rem 1.25rem;
      font-weight: 500;

      &:hover {
        border-color: #e9ecef #e9ecef #dee2e6;
      }

      &.active {
        color: #007bff;
        background-color: #fff;
        border-color: #dee2e6 #dee2e6 #fff;
      }
    }
  }
}

.search-filter-bar {
  background-color: #f8f9fa;
  padding: 1.25rem;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.batch-actions {
  margin-bottom: 1.5rem;

  .batch-actions-bar {
    background-color: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;

    .selected-vehicles-info {
      .badge {
        font-size: 0.9rem;
        padding: 0.5rem 0.75rem;
      }
    }

    .btn-white-text {
      color: white !important;
    }
  }

  .alert {
    margin-bottom: 0;
  }
}

.vehicle-table-container {
  margin-bottom: 2rem;
  overflow-x: auto;

  .select-all-container {
    padding: 0.5rem 0;

    .form-check {
      display: flex;
      align-items: center;

      .form-check-input {
        margin-right: 0.5rem;
      }

      .form-check-label {
        font-weight: 500;
      }
    }
  }

  .table {
    min-width: 1000px;

    th, td {
      vertical-align: middle;
      padding: 5px !important;
    }

    .select-column {
      position: relative;
      z-index: 10; // Bring the entire column forward

      .form-check {
        position: relative;
        z-index: 11;

        .form-check-input {
          opacity: 1 !important;
          position: relative;
          z-index: 12; // Highest z-index for the checkbox
          pointer-events: auto !important; // Force pointer events
        }
      }
    }

    .select-column {
      width: 50px;
      min-width: 50px;
      max-width: 50px;
      text-align: center;
      padding: 8px;

      .form-check {
        margin: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 20px;

        .form-check-input {
          margin: 0;
          width: 18px;
          height: 18px;
          border: 2px solid #6c757d;
          border-radius: 3px;
          background-color: #fff;
          cursor: pointer;

          //&:checked {
          //  background-color: #007bff;
          //  border-color: #007bff;
          //}

          //&:focus {
          //  border-color: #007bff;
          //  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
          //}
        }
      }
    }

    .image-column {
      width: 100px;

      .image-container {
        position: relative;
        display: inline-block;

        &.clickable {
          cursor: pointer;

          &:hover {
            .vehicle-thumbnail {
              opacity: 0.8;
              transform: scale(1.05);
            }

            .image-overlay {
              opacity: 1;
            }
          }
        }
      }

      .vehicle-thumbnail {
        width: 80px;
        height: 60px;
        object-fit: cover;
        border-radius: 4px;
        transition: opacity 0.2s, transform 0.2s;
        display: block;
      }

      .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 123, 255, 0.9);
        color: white;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        opacity: 0;
        transition: opacity 0.2s;
        font-size: 0.7rem;

        i {
          font-size: 1rem;
          margin-bottom: 2px;
        }

        span {
          font-weight: 500;
        }
      }
    }

    .vehicle-info-column {
      min-width: 150px;

      &.clickable {
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
          background-color: rgba(0, 123, 255, 0.05);
        }
      }

      .vehicle-name {
        font-weight: 600;
        margin-bottom: 0.25rem;
      }

      .vehicle-variant {
        font-size: 0.8rem;
        color: #6c757d;
        margin-bottom: 0.25rem;
      }

      .importing-badge {
        margin-left: 0.5rem;

        .fa-spinner {
          font-size: 0.9rem;
        }
      }
    }

    .specs-column {
      min-width: 180px;
      font-size: 0.85rem;
      white-space: normal;
      line-height: 1.3;
    }

    .price-column {
      min-width: 100px;
      font-weight: 600;
    }

    .date-column {
      min-width: 140px;
      font-size: 0.85rem;
      white-space: nowrap;
    }

    .reserve-price-column {
      .editable-field {
        cursor: pointer;
        display: flex;
        align-items: center;
        padding: 4px 8px;
        border-radius: 4px;
        transition: background-color 0.2s;

        &:hover {
          background-color: rgba(0, 123, 255, 0.1);
        }

        .edit-icon {
          margin-left: 6px;
          font-size: 0.8rem;
          color: #6c757d;
          opacity: 0.7;
        }

        &:hover .edit-icon {
          opacity: 1;
          color: #007bff;
        }
      }
    }

    .status-column {
      min-width: 120px;

      .status-badge {
        display: inline-block;
        margin-bottom: 0.25rem;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
      }

      .new-badge {
        margin-left: 0.25rem;
      }
    }

    .actions-column {
      min-width: 120px;
      white-space: nowrap;
      text-align: center;

      .importing-indicator {
        display: flex;
        justify-content: center;
        align-items: center;

        .badge {
          font-size: 0.75rem;
          padding: 0.5rem 0.75rem;

          .fa-spinner {
            margin-right: 0.25rem;
          }
        }
      }

      .btn-group {
        display: flex;
        gap: 0.25rem;
      }

      .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
      }

      .btn-white-text {
        color: white !important;
      }
    }
  }
}

.pagination-controls {
  .pagination {
    font-size: 1.2rem;

    .page-link {
      color: #007bff;
      padding: 0.5rem 0.75rem;

      &:focus {
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      }
    }

    .page-item.active .page-link {
      background-color: #007bff;
      border-color: #007bff;
      font-weight: bold;
    }
  }
}

// Status badge styles
.status-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  display: inline-block;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
}

// Modal styles
.modal-backdrop {
  z-index: 1040;
}

.modal {
  z-index: 1050;
}

// Vehicle Details Modal
.vehicle-image-carousel {
  .current-image-container {
    position: relative;
    margin-bottom: 1rem;

    .vehicle-detail-image {
      height: 300px;
      object-fit: cover;
      border-radius: 0.375rem;
      border: 1px solid #dee2e6;
      width: 100%;
    }
  }

  .no-image-placeholder {
    .vehicle-detail-image {
      height: 300px;
      object-fit: contain;
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
    }
  }

  .carousel-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0 1rem;

    .carousel-btn {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .image-counter {
      font-weight: 500;
      color: #495057;
      font-size: 0.9rem;
    }
  }

  .thumbnail-nav {
    display: flex;
    gap: 0.5rem;
    overflow-x: auto;
    padding: 0.5rem 0;

    .thumbnail-item {
      flex-shrink: 0;
      cursor: pointer;
      border: 2px solid transparent;
      border-radius: 0.25rem;
      overflow: hidden;
      transition: border-color 0.2s;

      &.active {
        border-color: #007bff;
      }

      &:hover {
        border-color: #6c757d;
      }

      .thumbnail-image {
        width: 60px;
        height: 45px;
        object-fit: cover;
        display: block;
      }
    }
  }
}

.vehicle-details {
  h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 0.5rem;
  }

  .table {
    margin-bottom: 0;

    td {
      padding: 0.5rem 0.75rem;
      border-top: 1px solid #dee2e6;

      &:first-child {
        width: 40%;
        background-color: #f8f9fa;
      }
    }

    tr:first-child td {
      border-top: none;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;

    .user-controls {
      margin-top: 1rem;
      width: 100%;

      button {
        width: 100%;
      }
    }
  }

  .search-filter-bar {
    form {
      flex-direction: column;

      .col-md-4, .col-md-3, .col-md-2 {
        width: 100%;
        margin-bottom: 1rem;
      }
    }
  }
}

// Accessibility focus styles
//button:focus, a:focus, input:focus, select:focus {
//  outline: 2px solid #007bff;
//  outline-offset: 2px;
//}
