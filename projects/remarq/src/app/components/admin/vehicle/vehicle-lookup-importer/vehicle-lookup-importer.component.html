<div *ngIf="loadingVehicles">
  <p>Loading vehicle data... <i class="la la-spinner la-spin"></i></p>
</div>

<div class="d-flex flex-wrap">
  <div class="flex-grow-1">
    <h1 class="page-header">Vehicle File Import</h1>
  </div>
  <div class="flex-shrink-1">
    <button class="btn btn-sm btn-primary"><i class="fas fa-folder-open"></i> Select file</button>
  </div>
</div>

<div class="widget widget-border padding" id="imported-lookup-widget">

  <h2>Imported</h2>

  <table class="table table-striped table-hover table-sm">
    <thead>
    <tr>
      <th style="text-align: center;"><input type="checkbox" (click)="changeAll()"></th>
      <th>VRM</th>
      <th>Ref</th>
      <th>Make</th>
      <th>Model</th>
      <th>VIN</th>
      <th colspan="2">&nbsp;</th>
    </tr>
    </thead>
    <tbody>
    <tr *ngFor="let vehicle of vehicles">
      <td align="center"><input [(ngModel)]="vehicle.checked" type="checkbox" class="import-checkbox"></td>
      <td><span class="vrm" (click)="url.vehicleView(vehicle.id);">{{vehicle.vrm}}</span></td>
      <td>{{ vehicle.customerRef }}
      <td>{{ vehicle.make.makeName }}</td>
      <td>{{ vehicle.model.modelName }}</td>
      <td>{{ vehicle.vin }}</td>
      <td class="text-right">
        <button class="btn btn-sm btn-danger" (click)="deleteVRM(vehicle)"><i class="fa fa-times-circle Delete"></i>
          Delete
        </button>
      </td>
    </tr>
    </tbody>
  </table>

  <div class="mt-3">
    <button class="btn btn-sm btn-danger"><i class="fa fa-times-circle"></i> Delete All</button>
  </div>
</div>

<div class="widget padding mt-2 mb-3">

  <h2>Not Imported</h2>
  <button class="btn btn-md btn-primary" (click)="SelectAllToImport()">
  <i class="fa fa-plus-circle"></i> Select All</button>
  <button class="btn btn-md btn-secondary" (click)="SelectNoneToImport()">
    <i class="fa fa-minus-circle"></i> Select None</button>

  <div class="imported-container">
    <table class="table table-striped">
      <tr *ngFor="let vehicle of vehicleDatas, let i = index">
        <td class="checkbox">
          <label>
            <input *ngIf="!vehicleDatas[i].dataNotFound" type="checkbox" [(ngModel)]="vehicleDatas[i].export" value="">
            &nbsp;
            <span class="vrm">{{vehicle.vrm}}</span>
            <p *ngIf="vehicleDatas[i].isExporting">Importing... <i class="la la-spinner la-spin"></i></p>
            <p *ngIf="vehicleDatas[i].dataNotFound"> Could not find lookup data</p>
          </label>
        </td>
      </tr>
    </table>
  </div>

  <div>
    <button type="button" class="btn btn-sm btn-primary" (click)="importSelected()"><i class="fas fa-file-import"></i>
      Import Selected
    </button>
  </div>
</div>


&nbsp;
