<div class="d-flex flex-wrap">
  <div class="flex-grow-1">
    <h1 class="page-header">Vehicles</h1>
  </div>

  <div class="text-right flex-shrink-1">
    <button class="btn btn-sm btn-primary" (click)="addVehicle()">
      <mdb-icon fas icon="plus-circle"></mdb-icon>
      Vehicle
    </button>
  </div>
</div>

<div class="widget widget-border mt-1" id="vehicle-search-widget">

  <div *ngIf="isLoading">
    <p>Loading vehicle data... <i class="la la-spinner la-spin"></i></p>
  </div>

  <form>
    <div class="table-responsive">
      <table mdbTable #tableEl="mdbTable" class="z-depth-1">
        <thead>
        <tr>
          <td colspan="6">
            <mdb-table-pagination [tableEl]="tableEl" [searchDataSource]="vehicles"></mdb-table-pagination>
          </td>
        </tr>
        <tr>
          <th *ngFor="let head of headElements; let i = index" scope="col">{{head}}
          </th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let vehicle of vehicles; let i = index" style="cursor: pointer"
            (click)="selectVehicle($event, vehicles[i])">
          <th *ngIf="i+1 >= mdbTablePagination.firstItemIndex && i < mdbTablePagination.lastItemIndex"
              scope="row">{{vehicle.id}}</th>
          <td *ngIf="i+1 >= mdbTablePagination.firstItemIndex && i < mdbTablePagination.lastItemIndex"
              class="row-content">{{vehicle.customerRef}}</td>
          <td *ngIf="i+1 >= mdbTablePagination.firstItemIndex && i < mdbTablePagination.lastItemIndex"
              class="row-content">{{vehicle.vrm}}</td>
          <td *ngIf="i+1 >= mdbTablePagination.firstItemIndex && i < mdbTablePagination.lastItemIndex"
              class="row-content">
            <div class="make-model">{{vehicle.make.makeName}} {{vehicle.model.modelName}}</div>
            <div class="deriv-name">{{vehicle.deriv.derivName}}</div>
          </td>
          <td *ngIf="i+1 >= mdbTablePagination.firstItemIndex && i < mdbTablePagination.lastItemIndex"
              class="shrink-cell row-content">{{vehicle.status.statusName}}</td>
        </tr>
        </tbody>
<!--        <tfoot class="grey lighten-5 w-100">-->
<!--        <tr>-->
<!--          <td colspan="6">-->
<!--            <mdb-table-pagination [tableEl]="tableEl"></mdb-table-pagination>-->
<!--          </td>-->
<!--        </tr>-->
<!--        </tfoot>-->
      </table>
    </div>
  </form>
</div>

<!-- modal for new vehicle -->
<div mdbModal #vehicleModal="mdbModal" class="addVehicleModal modal fade" tabindex="-1"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true" [config]="{backdrop: 'static'}">
  <div class="modal-dialog modal-md" role="document">
    <div class="modal-content">
      <div class="modal-header">

        <div *ngIf="gettingVehicleInfo">Getting vehicle data... <i class="la la-spinner la-spin"></i></div>
        <div *ngIf="!gettingVehicleInfo">Adding new vehicle</div>

      </div>
      <div class="modal-body">
        <form [formGroup]="form" (ngSubmit)="vrmEntered()">

          <div class="col-md-6 mb-3">
            <span>Location</span>
            <mdb-select [outline]="true"
                        [options]="addresses"
                        formControlName="addressId"
                        placeholder="Select Location"></mdb-select>
            <!--            <div class="pair-value tba">TBA</div>-->
          </div>

          <div class="md-form">
            <input mdbInput id="vrm" class="float-label form-control text-uppercase" style="font-weight: 500;"
                   type="text"
                   formControlName="vrm"/>
            <label for="vrm">Vehicle Registration</label>
          </div>

          <div class="mt-3 text-right">

            <button class="btn btn-secondary ml-2" type="button" (click)="cancelVRMInput()">Cancel</button>
            <button class="btn btn-primary ml-2" style="min-width: 80px;" type="submit">OK</button>

          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<div mdbModal #vehicleInfoModal="mdbModal" class="addVehicleModal modal fade" tabindex="-1"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true" [config]="{backdrop: 'static'}">
  <div class="modal-dialog modal-md" role="document">
    <div class="modal-content" *ngIf="newVehicleData">
      <div class="modal-header">
        <div *ngIf="importingVehicle">Importing vehicle... <i class="la la-spinner la-spin"></i></div>
        <div *ngIf="!importingVehicle">Confirm</div>
      </div>
      <div class="modal-body">
        <div class="text-center">
          <div class="vrm-style confirm-vrm">{{newVRM}}</div>
          <div class="make-model-deriv infobar-cell">
            <div class="make-model">{{newVehicleData.make}} {{newVehicleData.model}}</div>
            <div class="deriv">{{newVehicleData.derivative}}</div>
          </div>
          <div class="confirm-text">Is this correct?</div>
        </div>
        <div class="mt-3 text-center">
          <button class="btn btn-primary-outline ml-2" type="button" (click)="cancelVRMInput()">Cancel</button>
          <button class="btn btn-primary ml-2" style="min-width: 80px;" type="submit"
                  (click)="addNewVehicle()">Yes, continue
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
