import {AfterViewInit, ChangeDetectorRef, Component, OnInit, ViewChild} from '@angular/core';
import {URLService, VehicleService} from '../../../../services';
import {Router} from '@angular/router';
import {LookupService} from '../../../../services';
import {UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators} from '@angular/forms';
import {MdbTableDirective, MdbTablePaginationComponent, ModalDirective, ToastService} from 'ng-uikit-pro-standard';
import {User, VehicleDTO} from "../../../../global/interfaces";
import {UserService, VehicleLookupService} from "../../../../global/services";
import {} from "../../../../global/enums";

@Component({
  selector: 'app-vehicle-search',
  templateUrl: './vehicle-search.component.html',
  styleUrls: ['./vehicle-search.component.scss']
})
export class AdminVehicleSearchComponent implements OnInit, AfterViewInit {

  public searchTerm: string;
  isLoading: boolean;
  vehicles: any[];

  currentUser: User;

  @ViewChild('vehicleModal') vehicleModal: ModalDirective;
  @ViewChild('vehicleInfoModal') vehicleInfoModal: ModalDirective;

  @ViewChild(MdbTablePaginationComponent, { static: true }) mdbTablePagination: MdbTablePaginationComponent;
  @ViewChild(MdbTableDirective, { static: true }) mdbTable: MdbTableDirective
  previous: any = [];
  headElements = ['#', 'Ref', 'VRM', 'Description', 'Status'];

  newVehicleData: any;
  gettingVehicleInfo = false;
  importingVehicle = false;

  form: UntypedFormGroup;
  addresses: any;
  newVRM: string;
  private vehicleTypeId: number;

  constructor(private vehicleService: VehicleService,
              private router: Router,
              public lookupService: LookupService,
              private userService: UserService,
              private url: URLService,
              private vehicleLookupService: VehicleLookupService,
              private formBuilder: UntypedFormBuilder,
              private toast: ToastService,
              private cdRef: ChangeDetectorRef) {}

  async ngOnInit() {

    this.isLoading = true;
    this.vehicleTypeId = 1;

    this.form = this.formBuilder.group({
      vrm: new UntypedFormControl('', Validators.required),
      addressId: new UntypedFormControl(1)
    });

    await this.userService.loadCurrentUser().then(() => {
      this.currentUser = this.userService.CurrentUser;
    });

    await this.lookupService.getAll('address').then(result => {
      this.addresses = result.map(x => {
        return { label: x.addressName, value: x.id }
      });
    });

    await this.loadVehicles();
    await this.lookupService.loadVehicleLookups(null, null, this.vehicleTypeId, this.currentUser.customerId);

    this.isLoading = false;
  }

  ngAfterViewInit() {
    this.mdbTablePagination.setMaxVisibleItemsNumberTo(8);

    this.mdbTablePagination.calculateFirstItemIndex();
    this.mdbTablePagination.calculateLastItemIndex();
    this.cdRef.detectChanges();
  }

  loadVehicles() {

    return this.vehicleService.loadImportedVehicles(this.currentUser.customerId).then(result => {
      this.vehicles = result;

      this.mdbTable.setDataSource(this.vehicles);
      this.vehicles = this.mdbTable.getDataSource();
      this.previous = this.mdbTable.getDataSource();
    });
  }

  getVehicleDesc(vehicle): string {
    return vehicle.make.makeName;
  }

  selectVehicle($event: MouseEvent, vehicle) {
    this.router.navigate([this.url.vehicleView(vehicle.id)])
      .then(() => { });
  }

  addVehicle() {
    // open the vehicle modal to get details from vrm
    this.vehicleModal.show();
  }

  vrmEntered() {
    this.gettingVehicleInfo = true;
    let vrm = this.form.controls['vrm'].value.toUpperCase();

    this.newVRM = vrm;

    // get info for the selected vrm
    this.vehicleLookupService.getVehicleInfo(vrm).then((result) => {

      if (!result.makeName) {
        const options = { opacity: 0.9 };
        this.toast.error(`Vehicle '${vrm}' could not be found`, "Error", options);
        this.gettingVehicleInfo = false;
        return;
      }

      this.newVehicleData = result;
      this.gettingVehicleInfo = false;

      // close the vehicle modal and open the vehicle info modal
      this.vehicleModal.hide();
      this.vehicleInfoModal.show();
    }).catch(() => {

      const options = { opacity: 1 };
      this.toast.error("An error occurred in the vehicle lookup", "Error", options);

      this.gettingVehicleInfo = false;
    });
  }

  cancelVRMInput() {
    // close vrm modal
    this.vehicleModal.hide();
    this.vehicleInfoModal.hide();
  }

  addNewVehicle() {
    this.importingVehicle = true;

    const vehicleData = {
      customerId: this.currentUser.customerId,
      contactId: this.currentUser.contactId,
      addressId: this.form.controls['addressId'].value,
      vehicleLookupInfoDTO: {...this.newVehicleData}
    };

    this.vehicleService.createVehicleFromLookup(vehicleData).then((newVehicle: VehicleDTO) => {
      this.importingVehicle = false;
      // navigate to the vehicle edit screen with new VehicleId
      this.router.navigate([this.url.vehicleView(newVehicle.id, true)])
        .then(() => { });
    }).catch(() => {
      this.vehicleInfoModal.hide();
    });
  }
}
