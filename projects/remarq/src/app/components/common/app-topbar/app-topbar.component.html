<nav id="navbar" [class.devmode]="!isLive" class="navbar navbar-expand-md navbar-light bg-light sticky-top" [class.not-logged-in]="!isLoggedIn">
  <div class="container header-size">
    <span class="navbar-brand" (click)="isLoggedIn ? url.dashboard() : url.homepage()"></span>

    <button *ngIf="!isLoggedIn" class="navbar-toggler" type="button" aria-controls="navbarContent"
            [attr.aria-expanded]="!navbarCollapsed"
            aria-label="Toggle navigation" (click)="navbarCollapsed = !navbarCollapsed">
      <!-- <span class="toggler-text">Menu</span> <span class="fa fa-chevron-down"></span> -->
      <span class="toggler-text"><i class="fa fa-bars"></i></span>
    </button>

    <div *ngIf="!isLoggedIn" class="navbar-collapse" [class.collapse]="navbarCollapsed" id="navbarContent">
      <ul class="navbar-nav ml-auto text-right">

        <li class="nav-item order-3 order-md-1 inline-block mx-1">
          <a class="nav-link btn btn-tertiary-outline menu-buttons" routerLink="/contact-us">Contact</a>
        </li>

        <li class="nav-item login-link order-2 order-md-2 inline-block mx-1">
          <a class="nav-link btn btn-tertiary-outline login menu-buttons" [href]="url.login(true)">Log in</a>
        </li>

        <li class="nav-item signup-link order-1 order-md-3 inline-block pt-3 pt-md-0 mx-1">
          <a class="nav-link btn btn-tertiary sign-up menu-buttons" [href]="url.signup(true)">Sign up</a>
        </li>

        <li class="nav-item d-none d-md-inline-block order-md-5" style="line-height: 40px;">
          <i class="ml-3 fab fa-twitter"></i>
          <i class="ml-3 fab fa-linkedin"></i>
        </li>
      </ul>
    </div>

    <div *ngIf="isLoggedIn" style="display: flex; flex-grow: 1; flex-basis: auto; align-items: center">

      <div class="search-container px-lg-4 px-md-3 d-md-flex d-none" *ngIf="!hideSearchBar">
        <ng-container [ngTemplateOutlet]="searchbar"
                      [ngTemplateOutletContext]=""></ng-container>
      </div>

      <ul class="navbar-nav inline ml-auto order-1">
        <li class="nav-item d-md-none pt-2" *ngIf="!hideSearchBar">
          <span class="nav-link search-link mr-3 md-mr-2" (click)="url.search()">
            <i class="fa fa-search"></i>
          </span>
        </li>
        <li class="nav-item pt-2" *ngIf="!hideSearchBar">
          <a class="nav-link email-link mr-3 md-mr-2" href="#" [routerLink]="url.mail(0, true)">
            <span *ngIf="folderCounts && folderCounts.Inbox > 0" class="email-count">{{ folderCounts?.Inbox }}</span>
            <i class="far fa-envelope"
               [ngClass]="[folderCounts && folderCounts?.Inbox > 0 ? 'inbox-not-empty' : 'inbox-empty']"></i>
          </a>
        </li>
        <li class="nav-item dropdown remarq-dropdown pt-2" dropdown *ngIf="isLoggedIn">

          <a dropdownToggle class="nav-link dropdown-toggle waves-light user-dropdown">
            <i class="fas fa-user"></i>
            <span class="d-none d-md-inline user-name">
              {{ user.name }}
            </span>
          </a>

          <div *dropdownMenu class="dropdown-menu dropdown-menu-right dropdown dropdown-primary" role="menu">
            <div *ngFor="let dropDownMenuItem of dropDownMenu">
              <div [ngClass]="(dropDownMenuItem.itemLine)?'dropdown-line':''">
                <a *ngIf="dropDownMenuItem.isURL" class="dropdown-item waves-light" href="{{ dropDownMenuItem.itemRoute }}">{{ dropDownMenuItem.itemLabel }}</a>
                <a *ngIf="!dropDownMenuItem.isURL" class="dropdown-item waves-light" routerLink="{{ dropDownMenuItem.itemRoute }}">{{ dropDownMenuItem.itemLabel }}</a>
              </div>
            </div>
            <div>
              <a class="dropdown-item waves-light" (click)="logout()">Logout</a>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</nav>
<ng-template #searchbar>
  <div class="search-box input-group input-group-sm">
    <div class="input-group-prepend">
            <span
              style="padding: 0 1px 0 0.75rem;"
              class="input-group-text search-submit" id="vehicle-search" (click)="onVehicleSearch()">
              <i class="fa fa-search" aria-hidden="true"></i>
            </span>
    </div>
    <input class="form-control search-text-input" type="text" [(ngModel)]="vehicleSearchInput"
           (keyup.enter)="onVehicleSearch()"
           spellcheck="false"
           placeholder="Vehicle Search. Eg. Ford" aria-label="Vehicle Search. Eg. Ford">
  </div>
</ng-template>
