.menu-logo-text {
  font-size: 48px;
}

.menu-logo-text-loggedin {
  font-size: 32px;
}

.user-name {
  font-size: 0.75rem;
  line-height: 20px;
  vertical-align: text-top;
}

.side-nav {

  background-color: #0d181c !important;

}

.dropdown-line {

  border-top: 1px solid #ddd;

}

#vehicle-search {
  cursor: pointer;

  &:hover {
    color: rgba(0, 0, 0, 0.3);
  }
}


a.dropdown-item, a.dropdown-item:visited {

  color: var(--textColour);

}

.search-submit {
  border: 0 solid transparent !important;
  cursor: pointer;
  color: var(--floatLabelColour) !important;
}


nav {
  background-color: var(--navbarBgColour) !important;

  .login-link, .signup-link {
    .nav-link {
      font-weight: 600 !important;
    }
  }
}

nav .nav-link, nav {
  color: var(--navbarTextColour) !important;
  font-size: 16px;
  font-weight: 500;

  &:hover {
    color: var(--navbarTextHoverColour);
  }

}

.navbar {
  padding-top: 0.5rem;
  min-height: var(--headerHeight);
}

nav .nav-link:hover, .navbar-light .navbar-nav .show > .nav-link, nav {
  color: var(--navbarTextHoverColour);
}

.navbar-nav.inline {
  flex-direction: row !important;
}

nav .navbar-brand, nav .navbar-brand a, .navbar-brand a:visited {
  font-weight: bold;
  color: var(--navbarBrandColour) !important;
  cursor: pointer;
}

nav .navbar-brand a {
  color: inherit;
  text-decoration: inherit;
}

.navbar-light .navbar-toggler {

  color: var(--navbarTextHoverColour);

  .toggler-text {

    font-size: 1rem;
    font-weight: bold;
    vertical-align: top;
    line-height: 1.2rem;
    padding-right: 5px;
    display: inline-block;
  }

  padding-bottom: 0 !important;
  border: 0;
}

.search-container {
  display: flex;
  justify-content: center;
  width: 100%;
  text-align: center;
}

nav {
  .nav-link.menu-buttons {
    padding-left: 15px;
    padding-right: 15px;
  }
  .nav-link.btn-tertiary.sign-up {
    color: #fff !important;
    &:hover {
      background-color: #444 !important;
    }
  }

  .nav-link.btn-tertiary-outline.login {
    font-weight: 500 !important;
  }
}

.search-box {

  max-width: 650px;

  .search-text-input {

    background-color: var(--navSearchBgColour);
    color: var(--navSearchTextColour) !important;
    border: 0 !important;
    font-size: 0.85rem;
    font-weight: 500;

    &::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
      color: var(--colour10);
      opacity: 1; /* Firefox */
      font-weight: 400 !important;
    }

    &:-ms-input-placeholder { /* Internet Explorer 10-11 */
      color: var(--colour10);
      font-weight: 400 !important;
    }

    &::-ms-input-placeholder { /* Microsoft Edge */
      color: var(--colour10);
      font-weight: 400 !important;
    }
  }
}

:focus {
  outline-width: 0 !important;
}


.user-dropdown {

  .fa-user {
    color: var(--topbarIconColour) !important;
  }

  font-size: 0.9rem;
  font-weight: 500;

  span {
    color: var(--linkColour);
  }
}

.contact-message {
  display: inline-block;
  width: 32px;
  height: 32px;
}

.search-link {

  .fa-search {
    font-size: 25px;
    vertical-align: middle;
    margin-left: -4px;
    color: var(--topbarIconColour);
  }
}

.email-link {

  white-space: nowrap;
  padding: 0.4rem;

  .fa-envelope {
    font-size: 25px;
    vertical-align: middle;
    margin-left: -4px;
    color: var(--topbarIconColour);
  }

  .email-count {
    display: inline-block;
    position: relative;
    background-color: var(--errorColour);
    color: var(--colour3);
    padding: 0px 8px;
    font-size: 16px;
    font-weight: bold;
    border-radius: 12px;
    vertical-align: middle;
    min-width: 24px;
    min-height: 24px;
  }
}



