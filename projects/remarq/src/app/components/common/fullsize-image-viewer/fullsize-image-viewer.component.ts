import {Compo<PERSON>, HostListener, OnD<PERSON>roy, OnInit, ViewChild} from '@angular/core';
import {EventService} from '../../../services';
import {Subscription} from 'rxjs';
import {} from "../../../global/interfaces";
import {LoggerService} from "../../../global/services";
import {AdvertEventEnum} from "../../../global/enums";
import {ModalDirective} from "ng-uikit-pro-standard";

@Component({
  selector: 'app-fullsize-image-viewer',
  templateUrl: './fullsize-image-viewer.component.html',
  styleUrls: ['./fullsize-image-viewer.component.scss']
})
export class FullsizeImageViewerComponent implements OnInit, OnDestroy {

  constructor(private eventService: EventService,
  private logService: LoggerService) { }

  logger = this.logService.taggedLogger(this.constructor?.name);

  imageEventSub: Subscription;
  currentImageUrl: string;
  @ViewChild('imageModal') imageModal: ModalDirective;

  ngOnInit(): void {
    this.imageEventSub = this.eventService.AdvertActionEvent.subscribe(action => {
      switch (action.eventType) {
        case AdvertEventEnum.ViewImageFullSizeEvent:
          this.logger.log("VIEW IMAGE EVENT RECEIVED: ", action.object);
          this.currentImageUrl = action.object;
          this.imageModal.show();
          break;
      }
    });
  }

  @HostListener('window:beforeunload')
  ngOnDestroy() {
    if (this.imageEventSub) {
      this.imageEventSub.unsubscribe();
    }
  }
}
