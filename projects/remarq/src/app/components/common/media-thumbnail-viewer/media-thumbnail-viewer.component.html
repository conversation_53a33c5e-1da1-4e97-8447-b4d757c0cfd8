<div class="thumb-container" *ngIf="medias">
  <div class="thumb-link-container" *ngFor="let media of medias">

    <div class="thumb-link-container-item">
      <div *ngIf="media.mediaTypeId == MediaType.Image"
           class="thumb-link-image"
           style="background-image: url({{ media.mediaURL }}?tr=h-200)">
        <div *ngIf="!readOnly" class="delete-icon">
          <i class="fa fa-trash-alt thumb-link-icon" (click)="deleteMedia($event, media)"></i>
        </div>

      </div>
      <div *ngIf="media.mediaTypeId == MediaType.Document"
        class="thumb-link-image image-contain" style="background-image: url('../../../../assets/images/svg/PDF_file_icon.svg')"
        (click)="goToLink(media.mediaURL);">
        <div *ngIf="!readOnly" class="delete-icon">
          <i class="fa fa-trash-alt thumb-link-icon" (click)="deleteMedia($event, media)"></i>
        </div>
      </div>
    </div>
  </div>
</div>

<app-fullsize-image-viewer></app-fullsize-image-viewer>
