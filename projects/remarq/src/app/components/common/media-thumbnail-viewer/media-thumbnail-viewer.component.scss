.thumb-container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
}

.thumb-link-container {
  cursor: pointer;
  position: relative;
  margin-right: 12px;
}

.thumb-link-image {

  width: 105px;
  height: calc(105px * var(--aspectRatio));
  display: inline-block;
  border-radius: 3px;
  outline: 1px solid var(--imageOutlineColour);
  background-position: center center;
  background-repeat: no-repeat;

  &.image-cover {
    background-size: var(--vehicleMediaCoverOrContain);
  }

  &.image-contain {
    background-size: contain;
  }
}

.thumb-link-container-item {
  padding-bottom: 5px;
}

.thumb-link-container img {
  display: block;
  outline: 1px solid var(--softInputBorderColour);
}

.delete-icon {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1;
  color: rgba(51, 51, 51, 0.9);
  background-color: rgba(244, 244, 244, 0.8);
  width: 20px;
  border-top-right-radius: 3px;
  text-align: center;
  border-bottom-left-radius: 3px;
  color: #888;
}

.thumb-link-icon {
}

.thumb-link {
  width: 100%;
  border-radius: 3px;
}
