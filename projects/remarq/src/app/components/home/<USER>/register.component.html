<app-topbar [hideSearchBar]="true"></app-topbar>
<div class="page" style="height: 100%">
  <div class="narrow-container d-flex tell-us-more" *ngIf="!applied && pageLoaded">

    <div *ngIf="step == 1">

      <div class="mt-5 mb-5 align-self-center">

        <div class="widget widget-border p-4" id="register-widget">

          <h1 style="font-size: 24px;">Tell us about you</h1>

          <div class="letsget">Let's get to know you and your business to setup the best account for you.</div>

          <form [formGroup]="registrationForm" (ngSubmit)="onNextStep()">

            <!--
            <div class="signup-field">
              <div class="label">Full Name</div>
              <input type="text" id="contactName" formControlName="contactName"
                     class="form-control"
                     placeholder="Full Name"
                     spellcheck="false"
                     required>
            </div>
            -->
            <input type="hidden" id="contactName" formControlName="contactName">


            <div class="signup-field">
              <div class="label">Company Name</div>
              <input mdbInput type="text" id="companyName" formControlName="companyName" name="companyName"
                     spellcheck="false"
                     placeholder="Company Name"
                     class="form-control" required>
            </div>

            <div class="signup-field">
              <div class="label">Mobile Number</div>
              <input type="text" id="telephoneNumber" formControlName="telephoneNumber" name="companyName"
                     spellcheck="false"
                     placeholder="Mobile Number"
                     class="form-control" required>
            </div>

            <div class="mt-3">
              <div class="d-flex grid-gap-5">

                <div class="flex-shrink-1">
                  <mdb-checkbox id="tsandcs" formControlName="tsandcs" style="display: inline-block"></mdb-checkbox>
                </div>
                <div class="flex-grow-1 flex-shrink-1 is-trader">
                  I am a full-time motor trader and can provide a valid VAT number.
                </div>
              </div>
            </div>

            <div class="inline-error-message mt-2 ml-0 mb-2"
                 *ngIf="(f.tsandcs.dirty && f.tsandcs.errors && f.tsandcs.errors.required)"> Confirm you are in the
              motor trade
            </div>

            <div class="mt-3">
              <button class="btn btn-primary btn-block" [disabled]="pleaseWait || ! registrationForm.valid" type="submit">
                <span *ngIf="!pleaseWait">Continue</span>
                <span *ngIf="pleaseWait">
                <div>Please wait <i class="fas fa-spinner fa-spin"></i></div>
              </span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
    <div *ngIf="step == 2">

      <div class="mt-5 mb-5 align-self-center">

        <div class="widget widget-border p-4" id="register-widget">

          <h1 style="font-size: 24px;">Tell us about you</h1>

          <div class="letsget">Let's get to know you and your business to setup the best account for you.</div>

          <div class="howdoyou">How do you plan to use {{ globals.platformName }}?</div>

          <form [formGroup]="customerTypeForm" (ngSubmit)="onSubmit()">

            <div class="d-flex flex-wrap grid-gap-15">

              <div
                [class.chosen]="customerTypeForm.controls.buyer.value"
                class="flex-grow-1 howbox cursor-pointer text-center" (click)="toggleBuy()">
                <mdb-checkbox formControlName="buyer" id="buyer" style="width: 30px;"></mdb-checkbox>
                <div>I would like to</div>
                <div style="font-weight: 700">buy vehicles</div>
              </div>

              <div
                [class.chosen]="customerTypeForm.controls.seller.value"
                class="flex-grow-1 howbox cursor-pointer text-center" (click)="toggleSell()">
                <mdb-checkbox
                  formControlName="seller" id="seller"></mdb-checkbox>
                <div>I would like to</div>
                <div style="font-weight: 700">sell vehicles</div>
              </div>
            </div>

            <div style="margin-top: 30px;">
              <div class="d-flex">
                <div class="flex-shrink-1">
                  <button class="btn btn-outline-primary mr-2" [disabled]="pleaseWait" (click)="onPrevStep()">Back
                  </button>
                </div>
                <div class="flex-grow-1">
                  <button class="btn btn-primary btn-block" [disabled]="pleaseWait || ! validForm()" type="submit">
                    <span *ngIf="!pleaseWait">Continue</span>
                    <span *ngIf="pleaseWait"><div>Please wait <i class="fas fa-spinner fa-spin"></i></div></span>
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="thank-you narrow-container d-flex" id="applied" *ngIf="applied">
  <div class="align-self-center pb-5">
    <div class="widget widget-border p-4" id="application-received-widget">
      <h1>Thank you !</h1>
      <p>Your account request has been received</p>
      <p>Our customer support team will contact you shortly to welcome you to our service.</p>
      <p>Feel free to call us on <a href="tel:***********">01327 777222</a></p>
    </div>
  </div>
</div>
