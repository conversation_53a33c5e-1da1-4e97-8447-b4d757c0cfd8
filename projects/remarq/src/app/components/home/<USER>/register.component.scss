.narrow-container {
  max-width: 480px;
  margin: 0 auto;
}

.howdoyou {
  font-weight: 600;
  margin-bottom: 30px;
}

.tell-us-more {
  height: max(500px, var(--centeringHeight))
}

.thank-you {
  height: max(240px, var(--centeringHeight))
}

.howbox {
  border: 1.5px solid #dfdfdf;
  border-radius: 4px;
  font-weight: 400;
  padding: 15px;

  &:hover {
    background-color: #F1F7FD;
  }

  &.chosen {
    border: 1.5px solid var(--linkColour);
    background-color: #F1F7FD;
  }
}

.letsget {
  padding-top: 20px;
  padding-bottom: 20px;
}

.letsget, .tsandcs {
  font-weight: 400;
}

.inline-error-message {
  font-weight: 500;
}

.ng-dirty input.ng-invalid {
  border: 2px solid var(--errorColour) !important;
}

.signup-field {

  .label {
    font-weight: 500;
  }

  input.form-control {
    margin-top: 5px;
    margin-bottom: 15px;
    font-size: 1.1rem;
    padding: 0.5rem 0.75rem;

    &:focus {
      border: 2px solid var(--linkColour) !important;
    }
  }
}

.is-trader {
  font-weight: 400;

}
