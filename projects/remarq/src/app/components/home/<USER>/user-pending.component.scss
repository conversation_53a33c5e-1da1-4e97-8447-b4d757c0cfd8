.narrow-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  padding-bottom: 100px;

  .widget {
    border: 1px solid #89949f !important;
    box-shadow: rgba(13, 26, 38, 0.15) 0px 2px 6px 0px !important;
  }
}

.tell-us-more {
  height: max(500px, var(--centeringHeight))
}

.image-button {
  padding: 10px 15px;
  border: 1.5px solid #d4d4d4;
  border-radius: 5px;
  margin-bottom: 10px;

  &:hover {
    background-color: #F1F7FD;
  }

  &.selected {
    background-color: #F1F7FD;
    border: 1.5px solid var(--linkColour);
  }
}

.thank-you {
  height: max(240px, var(--centeringHeight))
}

.md-form {
  margin-bottom: 0.1rem !important;
  margin-top: 0.9rem !important;
}

.almost-there-text {
  font-size: 0.875rem;
  font-weight: 400;
}

.radio-label {
  font-size: 1rem;
  font-weight: 500;
}

.verification-image-label {
  font-weight: 600;
  font-size: 19px;
  margin-bottom: 15px;
}

.error-message {
  margin-left: 10px;
  position: relative;
  height: 5px !important;
  margin-bottom: 20px;
}

.doc-type-form, .uploaded-docs {

  .fa.fa-check {
    font-size: 15px;
    color: var(--successColour)
  }

  .custom-control-label {
    font-size: 15px;
  }
}

.check-mark {
  display: inline-block;
  width: 25px;
}

.upload-disabled {
  opacity: 0.5;
  filter: grayscale(100%);
}

.spinner-icon {
  position: absolute;
  left: 40px;
  top: 35px;
  font-size: 20px;
  z-index: 1
}

.uploaded-image-img {
  object-fit: cover;
  height: 100%;
  width: 100%;
  border-radius: 5px;
}

.uploaded-image-item {
  height: 100%;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
  border-radius: 5px;
  z-index: 2

}

.uploaded-image-container {
  background-color: #ddd;
  position: relative;
  border-radius: 5px;
  height: 100px;
  width: 100px;
}

.remove-image-button {
  z-index: 3;
  position: absolute;
  background-color: #ddd;
  color: #333;
  display: inline-block;
  height: 22px;
  border-radius: 0 4px 0 5px;
  text-align: center;
  right: 0px;
  width: 22px;
  line-height: 22px;
  text-align: center;
  cursor: pointer;
}

.uploaded-label {
  font-size: 15px;
  font-weight: 500;
  display: inline-block;
}

.category-name {
  font-size: 0.775rem;
  line-height: 1.6rem;
  height: 1.8rem;
  font-weight: 500;
}

.box-header {
  background-color: #888;
  color: #fff;
  border-radius: 5px 5px 0 0;
}
