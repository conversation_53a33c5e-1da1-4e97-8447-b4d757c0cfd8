<div [class.ghost]="loadingAdvert">

  <div id="bidbox-widget" class="widget">

    <div id="bidbox"
         class="ghost-waiting"
    >

      <div class="text-center" [class.loadingSpacer]="loadingAdvert">

        <div *ngIf="info?.saleTypeId == saleTypeEnum.BuyItNow">

          <div *ngIf="showDealLimitReached" class="bid-error">
            Buy limit reached, call {{ globals?.supportPhone }} for assistance
          </div>


          <div *ngIf="b.mb && b.mb.bidStatus == BidStatusEnum.BID_WITHVENDOR" class="bid-with-vendor bidding-position">
            <div (click)="showOfferModal()">You have offered {{ b.mb.bidAmt | customCurrency }}</div>
          </div>
          <div *ngIf="b.mb && b.mb.bidStatus == BidStatusEnum.BID_TOOLATE" class="bid-too-late">
            Bid received after sale ended
          </div>

          <div *ngIf="wonAuction()" class="won" (click)="url.myPurchases()">
            <i class="fa fa-info-circle" style="opacity: 0.7;"></i> You have bought this vehicle
          </div>

          <div class="row">
            <div class="col-xl-6 bid-state">
              <div class="bid-state-inner">
                <div class="price-label text-center">Buy Now:</div>
                <div class="price text-center mt-2">{{ (info?.buyItNowPrice | customCurrency) || "N/A" }}</div>
                <div class="fees link-style text-center" (click)="openRateCard()">excl. fees</div>

                <div *ngIf="info?.soldStatus == soldStatusEnum.Active" class="text-center mt-3 px-3">

                  <div>
                    <button [disabled]="awaitingBidConfirmation || yourAdvert" type="button"
                            (click)="confirmBuyNowModal.show()"
                            class="btn btn-primary confirm-bin-button btn-block ">
                      <span *ngIf="!awaitingBidConfirmation">Buy Now</span>
                      <span *ngIf="awaitingBidConfirmation"><i class="fa fa-spin fa-spinner"></i></span>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-xl-6">


              <div style="display: flex; width: 100%; align-items: center; height: 100%;">


                <div class="widget-padding-style flex-grow-1">

                  <div *ngIf="countdownExpired()" class=""><i
                    class="fa fa-exclamation-triangle text-danger-alt pb-2"></i>&nbsp;<span style="font-weight: 600;">Lot Ended</span><br/><span
                    style="font-size: 12px; font-weight: 400">{{ info?.endDateTime | date: "HH:mm dd/MM/yy" }}</span>
                  </div>

                  <div *ngIf="!countdownExpired()" class="text-right">


                    <div *ngIf="info?.soldStatus == soldStatusEnum.Active" class="text-center">

                      <div *ngIf="canMakeOffer" class="mb-2">
                        <button type="button" [disabled]="yourAdvert || offersRemaining <= 0"
                                class="make-offer-button btn-block align-buttons btn btn-outline-primary"
                                (click)="showOfferModal()">Make An Offer
                        </button>
                      </div>

                      <div class="mb-2">
                        <ng-container [ngTemplateOutlet]="messageSellerButton"></ng-container>
                      </div>
                      <div class="mb-2">
                        <ng-container [ngTemplateOutlet]="watchlistButton"></ng-container>
                      </div>

                      <div class="buy-now-time-left">
                        Listing Ends: <span class="timer-words">{{ timerLeftWords }}</span>
                      </div>

                      <div class="timer-date" *ngIf="secondsLeft > 3600">
                        {{ info?.endDateTime | date: "d/M/yy, h:mm a" }}
                      </div>
                    </div>

                    <div *ngIf="info?.soldStatus == soldStatusEnum.Withdrawn" class="text-center">
                      <div class="sold-status-title">This listing has been</div>
                      <span class="sold-status-withdrawn">Withdrawn</span>
                    </div>
                    <div *ngIf="info?.soldStatus == soldStatusEnum.Sold" class="text-center">
                      <span class="sold-status-sold">Sold</span>
                    </div>
                    <div *ngIf="info?.soldStatus == soldStatusEnum.Unsold" class="text-center">
                      <div class="sold-status-title">This listing has</div>
                      <div class="sold-status-unsold">Ended</div>
                    </div>
                    <div *ngIf="info?.soldStatus == soldStatusEnum.Provisional" class="text-center">
                      <span class="sold-status-provisional">Provisional</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div *ngIf="info?.saleTypeId == saleTypeEnum.ManagedSale">

          <ng-container [ngTemplateOutlet]="bidBoxTopLine"
                        [ngTemplateOutletContext]="{ b: b, secondsLeft: secondsLeft}"></ng-container>

          <div class="row">
            <div class="col-xl-6 bid-state">

              <div class="bid-state-inner">

                <div class="text-center">
                  <div class="price-label">Current bid</div>
                  <div class="price">
                    <span *ngIf="info?.bidCount == 0">{{ info?.startPrice | customCurrency }} </span>
                    <span *ngIf="info?.bidCount > 0">{{ info?.currentPrice | customCurrency }} </span>
                  </div>
                  <div class="reserve-status text-center">
                    <div class="provisional" *ngIf="info?.reserveMet">Reserve Met</div>
                    <div class="reserve-not-met" *ngIf="!info?.reserveMet">Reserve Not Met</div>
                  </div>
                </div>
                <div class="text-center" style="width: 30%">
                  <div class="ggg ggg-{{ info?.ggg }}" *ngIf="info?.ggg > 0">
                    <i class="fa fa-gavel gavel gavel-{{ info?.ggg }}"></i>
                    <br/>
                    <span *ngIf="info?.ggg == gggEnum.GoingOnce">
                Going Once</span>
                    <span *ngIf="info?.ggg == gggEnum.GoingTwice">
                Going Twice</span>
                    <span *ngIf="info?.ggg == gggEnum.Gone">
                Ended</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-xl-6">

              <div class="flex-grow-1 widget widget-no-border" *ngIf="canProxyBid">
                <div class="text-center pt-3 pb-3">
                  <app-advert-proxy-bid [advert]="info" [user]="user"></app-advert-proxy-bid>
                  <div class="px-3">
                    <div class="mt-2">
                      <ng-container [ngTemplateOutlet]="messageSellerButton"></ng-container>
                    </div>
                    <div class="mt-2">
                      <ng-container [ngTemplateOutlet]="watchlistButton"></ng-container>
                    </div>
                  </div>
                </div>
              </div>

              <div class="mt-2" *ngIf="canBid()">

                <div *ngIf="b?.mb?.bidAmt > info?.currentPrice && b?.mb?.bidStatus == BidStatusEnum.BID_ACCEPTED"
                     class="proxy-bid-amt">
                  Proxy Bid {{ b?.mb?.bidAmt | customCurrency }}
                </div>

                <div *ngIf="!awaitingBidConfirmation">

                  <div *ngIf="info && info.bidCount == 0; else haveBids" class="pb-3">
                    <button
                      [disabled]="info?.ggg == gggEnum.Gone || (info?.reserveMet && b.fa) || yourAdvert"
                      (click)="managedBidSubmit(info?.startPrice)"
                      type="button" class="btn btn-primary">Bid <span
                      class="next-price">{{ (info?.startPrice || 0) + (info?.bidIncrement || 0) | customCurrency }}</span>
                    </button>

                  </div>

                  <ng-template #haveBids>

                    <div class="pb-3">

                      <ng-container [ngTemplateOutlet]="bidButton"
                                    [ngTemplateOutletContext]="{bidAmt: 25, cutOff: 0}"></ng-container>
                      <ng-container [ngTemplateOutlet]="bidButton"
                                    [ngTemplateOutletContext]="{bidAmt: 50, cutOff: 25}"></ng-container>
                      <ng-container [ngTemplateOutlet]="bidButton"
                                    [ngTemplateOutletContext]="{bidAmt: 100, cutOff: 25}"></ng-container>
                      <ng-container [ngTemplateOutlet]="bidButton"
                                    [ngTemplateOutletContext]="{bidAmt: 200, cutOff: 50}"></ng-container>
                      <ng-container [ngTemplateOutlet]="bidButton"
                                    [ngTemplateOutletContext]="{bidAmt: 500, cutOff: 100}"></ng-container>
                      <ng-container [ngTemplateOutlet]="bidButton"
                                    [ngTemplateOutletContext]="{bidAmt: 1000, cutOff: 200}"></ng-container>
                    </div>
                  </ng-template>

                </div>
                <div *ngIf="awaitingBidConfirmation">
                  <i class="fa fa-2x fa-spin fa-spinner"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div *ngIf="info?.saleTypeId == saleTypeEnum.TimedAuction || info?.saleTypeId == saleTypeEnum.Underwrite">

          <div>

            <ng-container [ngTemplateOutlet]="bidBoxTopLine"
                          [ngTemplateOutletContext]="{ b: b, secondsLeft: secondsLeft}"></ng-container>
          </div>

          <div class="row">

            <div class="col-xl-6 bid-state">

              <div class="bid-state-inner" style="flex-grow: 1">

                <div *ngIf="info?.bidCount == 0" class="w-100">
                  <div class="price-label">Starting price:</div>
                  <div class="price mt-2">{{ (info?.startPrice || 0 | customCurrency) || "N/A" }}</div>
                </div>

                <div *ngIf="info?.bidCount > 0" class="w-100">
                  <div class="price-label">Current bid</div>
                  <div class="">
                    <div class="price mt-2">{{ (info?.currentPrice | customCurrency) || "N/A" }}</div>
                    <div class="fees link-style text-center" (click)="openRateCard()">excl. fees</div>
                    <div class="reserve-status mt-1" *ngIf="info?.saleTypeId != saleTypeEnum.Underwrite">
                      <div class="provisional" *ngIf="info?.reserveMet">Reserve Met</div>
                      <div class="reserve-not-met" *ngIf="!info?.reserveMet">Reserve Not Met</div>
                    </div>
                  </div>
                </div>

                <div class="time-left w-100">
                  Time Left:
                  <span class="timer-words">{{ timerLeftWords }}</span>
                </div>

                <div class="timer-date w-100" *ngIf="secondsLeft > 3600">
                  Ends {{ info?.endDateTime | date: "d/M/yy, h:mm a" }}
                </div>

                <div class="mt-3 underwrite w-100"
                     *ngIf="info?.saleTypeId == saleTypeEnum.Underwrite && info?.underwriteReservePrice > 0">
                  <div class="price-label">Underwrite Reserve:</div>
                  <div class="">
                    <div class="reserve-price">{{ (info?.underwriteReservePrice || 0 | customCurrency) || "N/A" }}
                    </div>
                  </div>
                </div>


                <div *ngIf="b.fa && b.mb != null && b.mb.bidAmt > info?.currentPrice" class="mt-1 w-100">
                  <div class="your-max-bid">
                    Your max. bid: <span class="your-max-bid-amount">{{ b.mb.bidAmt | customCurrency }}</span>
                  </div>
                </div>
              </div>

            </div>

            <div class="col-xl-6">

              <div class="bid-entry">

                <div class="w-100">

                  <div *ngIf="secondsLeft >= 0">
                    <div *ngIf="!awaitingBidConfirmation">
                      <form id="bidForm" [formGroup]="timedBidForm" (ngSubmit)="timedBidSubmit()" *ngIf="!yourAdvert">
                        <div class="md-form narrow" id="bid-amount-box">

                          <div style="position: relative">
                            <input mdbInput class="form-control" autocomplete="off" formControlName="bidAmt"
                                   placeholder="Bid Amount"
                                   oninput="this.value = this.value.replace(/[^0-9]/g, '');"
                                   id="bidAmt2"/>
                            <div class="currency-symbol">&pound;</div>
                          </div>

                          <div *ngIf="b.bs == BidStatusEnum.BID_TOOLATE" class="bid-error">
                            Bid was submitted too late
                          </div>
                          <div *ngIf="b.bs == BidStatusEnum.BID_TOOLOW" class="bid-error">
                            Bid amount is too low
                          </div>
                          <div class="minimum-bid text-center mt-1">
                            Enter a minimum of <span class="minimum-bid-amount">{{ minBid() | customCurrency }}</span>
                          </div>


                          <div class="mt-2">

                            <button type="button"
                                    [disabled]="subscribedToAdvert == null || yourAdvert"
                                    class="submit-bid-btn btn-block btn btn-primary" (click)="timedBidSubmit()">
                              <span *ngIf="awaitingSubmit">Submit Bid</span>
                              <span *ngIf="!awaitingSubmit"><i class="fa fa-spin fa-spinner"></i></span>

                            </button>
                          </div>


                        </div>
                      </form>
                    </div>
                  </div>
                  <div *ngIf="secondsLeft < 0 || countdownExpired()" class="pt-2 pb-2 lot-ended">

                    <i class="fa fa-exclamation-triangle text-danger-alt"></i> This lot <span
                    style="font-weight: 500">ended</span> at {{ info?.endDateTime | date: "HH:mm dd/MM/yy" }}

                  </div>
                  <div *ngIf="awaitingBidConfirmation">
                    <i class="fa fa-2x fa-spin fa-spinner"></i>
                  </div>

                  <div>
                    <div class="mt-2">
                      <ng-container [ngTemplateOutlet]="messageSellerButton"></ng-container>
                    </div>
                    <div class="mt-2">
                      <ng-container [ngTemplateOutlet]="watchlistButton"></ng-container>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>

      </div>
    </div>

    <div mdbModal #offerModal="mdbModal" class="modal fade" tabindex="-1" role="dialog">
      <div class="modal-dialog modal-dialog-centered modal-md" role="document">
        <div class="modal-content">
          <div class="modal-header narrow">
            <button type="button" class="close pull-right" aria-label="Close" (click)="offerModal.hide()"><span
              aria-hidden="true">×</span></button>
            <div class="modal-title" id="offerModalLabel">Make An Offer</div>
          </div>
          <div class="modal-body">

            <form id="offerForm" [formGroup]="makeOfferForm"
                  *ngIf="(b.mb?.bidStatus != BidStatusEnum.BID_WITHVENDOR) && info?.soldStatus != soldStatusEnum.Sold"
                  (ngSubmit)="timedBidSubmit()">

              <div style="display: block;">
                <div class="md-form narrow" id="offer-amount-box">
                  <div>Offer Amount</div>
                  <div class="input-group mb-3">
                    <div class="input-group-prepend">
                      <span class="input-group-text">&pound;</span>
                    </div>
                    <div class="input-group-item">
                      <input mdbInput [readOnly]="offersRemaining <= 0" class="form-control pl-3 offer-amount-input"
                             autocomplete="off"
                             placeholder="Amount"
                             oninput="this.value = this.value.replace(/[^0-9]/g, '');"
                             formControlName="bidAmt" id="offerAmt"/>
                    </div>
                  </div>
                </div>

                <div>Offer Expires</div>

                <div id="offer-expires-box" class="select">
                  <mdb-select-2 [outline]="true"
                              style="min-width: 200px;"
                              formControlName="bidExpires"
                              placeholder="Select">
                    <mdb-select-option *ngFor="let option of expiresOptions" [value]="option.value">
                      {{ option.label }}
                    </mdb-select-option>
                  </mdb-select-2>
                </div>
              </div>

              <div *ngIf="b.mb != null && b.mb.bidStatus > 0" class="mb-3 mt-2">
                <div *ngIf="b.mb && b.mb.bidStatus == BidStatusEnum.BID_TOOLOW" class="rejected">
                  <div class="bid-too-low">Your offer of {{ b.mb.bidAmt | customCurrency }} was too low and has been
                    rejected.
                  </div>
                  <div class="bid-too-low">You have {{ b.bl }} bids remaining</div>

                  <!--
                  <div><span *ngIf="offersRemaining > 0">Please make a higher offer.</span> You have {{ offersRemaining }}
                    offers remaining
                  </div>
                  -->
                </div>
                <div *ngIf="b.bs == BidStatusEnum.BID_NOPERMISSION" class="rejected">
                  Your offer has been rejected. You do not have permission to bid
                </div>
                <div *ngIf="b.bs == BidStatusEnum.BID_TOOLATE" class="rejected">
                  Your offer has been rejected. Too late
                </div>

              </div>


              <div class="mb-3 mt-2" *ngIf="canMakeOffer">

                <button type="submit" [disabled]="
               awaitingBidConfirmation ||
               offersRemaining <= 0 ||
               (b.mb?.bidAmt > 0 && o.bidAmt?.value <= b.mb?.bidAmt) || yourAdvert"
                        class="btn btn-primary btn-sm" (click)="submitOffer()">
                  <span *ngIf="!awaitingBidConfirmation">Submit Offer</span>
                  <span *ngIf="awaitingBidConfirmation"><i class="fa fa-spin fa-spinner"></i> Wait</span>
                </button>

              </div>

            </form>

            <div *ngIf="b.mb?.bidStatus == BidStatusEnum.BID_ACCEPTED &&
                    b.mb?.bidGuid == info?.topBidGuid &&
                    info?.soldStatus == soldStatusEnum.Sold" class="accepted">

              <table class="w-100">
                <tr>
                  <td class="shrink-cell tick text-success">
                    <i class="fa fa-check-circle"></i>
                  </td>
                  <td>
                    Your offer of {{ b.mb?.bidAmt | customCurrency }} has been accepted by the seller.
                  </td>

                </tr>
                <tr>
                  <td colspan="2">
                    <div class="mt-3 mb-2">
                      <button (click)="offerModal.hide()" class="btn btn-primary btn-sm mr-2">Continue</button>
                      <button (click)="url.invoiceTab(3, false)" class="btn btn-primary-outline btn-sm mr-2">My
                        Purchases
                      </button>
                    </div>
                  </td>
                </tr>
              </table>
            </div>

            <div *ngIf="b.mb && b.mb.bidStatus > 0" class="mb-2">
              <div *ngIf="b.mb.bidStatus == BidStatusEnum.BID_WITHVENDOR" class="with-vendor">
                <table class="w-100">
                  <tr>
                    <td class="shrink-cell tick text-success">
                      <i class="fa fa-check-circle"></i>
                    </td>
                    <td>
                      Your offer of {{ b.mb.bidAmt | customCurrency }} has been sent to the seller.<br/>They will
                      respond
                      shortly.
                    </td>

                  </tr>
                  <tr>
                    <td colspan="2">
                      <div class="mt-3 mb-2">
                        <button (click)="offerModal.hide()" class="btn btn-primary btn-sm mr-2">Continue</button>
                        <button (click)="url.myBids('1', '2')" class="btn btn-primary-outline btn-sm mr-2">View My
                          Offers
                        </button>
                        <button (click)="cancelOffer()" class="btn btn-primary-outline btn-sm">Cancel Offer</button>
                      </div>
                    </td>
                  </tr>
                </table>
              </div>
            </div>

            <div class="smallprint">
              If your offer is accepted by the seller within the offer period, it will become binding and a contract
              between
              you and the seller will be formed. Payment must be made in full within 2 working days unless agreed
              otherwise
              with the seller. A copy of this offer will be sent to you for your records.
            </div>

          </div>
        </div>
      </div>
    </div>

    <div mdbModal #confirmBuyNowModal="mdbModal" class="modal fade" tabindex="-1" role="dialog"
         aria-labelledby="myBasicModalLabel"
         aria-hidden="true">
      <div class="modal-dialog modal-md modal-dialog-centered" role="document">
        <div class="modal-content">
          <div class="modal-header">

            <button type="button" class="close pull-right" aria-label="Close"
                    (click)="confirmBuyNowModal.hide()"><span
              aria-hidden="true">×</span></button>
            Confirm Purchase
          </div>
          <div class="modal-body" *ngIf="info">

            <div class="px-2">


              <h1 class="page-header padding ghost-waiting">
                {{ info?.makeName }}
                {{ info?.modelName }}
                - {{ info?.plateName }}
              </h1>
              <h2 class="ghost-waiting">
                {{ info?.derivName }}
                - {{ info?.transmissionTypeName }}
                - {{ info?.fuelTypeName }}
              </h2>

              <div style="font-weight: 600; font-size: 1.75rem;">
                {{ info?.buyItNowPrice  | customCurrency }}
              </div>
              <div class="fees link-style" (click)="openRateCard()">+ buyers fee</div>

              <div *ngIf="showBuyersFees">

                Buyers Fees

              </div>

              <div *ngIf="!awaitingBidConfirmation" class="mt-3">
                <button class="btn btn-primary mb-2 mr-2" (click)="confirmPurchase()">Confirm Purchase</button>
                <button class="btn btn-primary-outline mb-2 mr-2" (click)="confirmBuyNowModal.hide()">Cancel
                </button>
              </div>
              <div *ngIf="awaitingBidConfirmation">
                <i class="fa fa-2x fa-spin fa-spinner"></i>
              </div>
              <div class="mt-2">
                <a href="#" routerLink="['/terms']" class="tsandcs">Terms & Conditions</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div mdbModal #rateCardModal="mdbModal" class="modal fade" tabindex="-1"
         role="dialog" aria-labelledby="rateCardModalLabel" aria-hidden="true">

      <div class="modal-dialog modal-dialog-centered modal-sm modal-width" role="document">

        <div class="modal-content mb-5">

          <div class="modal-header" style="font-size: 0.875rem;">

            <button type="button" class="close pull-right" aria-label="Close" (click)="rateCardModal.hide()"><span
              aria-hidden="true">×</span></button>
            <div class="modal-title">Buyers Rate Card</div>
          </div>

          <div class="modal-body">

            <!-- WHEN WE HAVE A RATE CARD
            <table class="table table-striped table-hover table-compressed">
              <thead>
              <tr>
                <th>Vehicle Price</th>
                <th class="text-right">Payable Fee (+VAT)</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let card of buyerRateCards; let i = index">
                <td style="text-align: left">
                  {{ card.priceMin | customCurrency }} {{ card.priceMax > 0 ? '- ' + (card.priceMax | customCurrency) : '+' }}
                </td>
                <td style="text-align: right">{{ card.payableFee | customCurrency }}</td>
              </tr>
              </tbody>
            </table>
            -->
            <table style="width: 100%; border: 1px solid #dfdfdf;">
              <tr>
                <td style="background-color: #dfdfdf; text-align: center">
                  <div>Buyer's fee</div>
                </td>
                <td style="background-color: #dfdfdf; text-align: center">
                  Delivery fee
                </td>
              </tr>
              <tr>
                <td style="text-align: center;">
                  <div>£95<span style="font-size: 10px; color: #999">+VAT</span></div>
                </td>
                <td style="text-align: center;">
                  <div>Optional</div>
                </td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #bidButton let-bidAmt="bidAmt" let-cutOff="cutOff">
  <button *ngIf="info?.bidIncrement <= bidAmt && info?.bidIncrement >= cutOff"
          [disabled]="info?.ggg == gggEnum.Gone || (info?.reserveMet && b.fa) || yourAdvert"
          (click)="managedBidSubmit(info?.currentPrice + bidAmt)"
          type="button" class="ml-1 mr-1 btn btn-primary"><span
    class="next-price">+{{ bidAmt | customCurrency }}</span></button>
</ng-template>

<ng-template #bidBoxTopLine let-b="b" let-secondsLeft="secondsLeft">
  <div class="winning-status">
    <div *ngIf="yourAdvert; else notYourAdvert" class="bidding-position">
      Your Advert
    </div>
    <ng-template #notYourAdvert>
      <div *ngIf="secondsLeft >= 0 && ! countdownExpired(); else endedTemplate">

        <div *ngIf="canBid()">
          <div class="bidding-position" *ngIf="b.fa == null">Bid Now</div>
          <div class="with-you bidding-position" *ngIf="b.fa == true">With you</div>
          <div class="against-you bidding-position" *ngIf="b.fa == false">Against you</div>
        </div>

        <div *ngIf="showDealLimitReached" class="bid-error">
          Bid limit reached, call {{ globals?.supportPhone }} for assistance
        </div>

      </div>
    </ng-template>
    <ng-template #endedTemplate>
      <div *ngIf="info?.soldStatus == soldStatusEnum.Sold && b.fa; else biddingEndedTemplate" class="won"
           (click)="url.myPurchases()">
        <i class="fa fa-info-circle" style="opacity: 0.7;"></i>
        &nbsp;
        You are the winning bidder
      </div>
    </ng-template>

    <ng-template #biddingEndedTemplate>
      <div class="against-you bidding-position">Bidding has ended</div>
    </ng-template>
  </div>
</ng-template>

<ng-template #messageSellerButton>

  <app-advert-contact-seller [advertId]="advertId" [user]="user"
                             [override]="true">
    <button class="btn btn-secondary btn-block text-center message-seller">Message Seller
    </button>
  </app-advert-contact-seller>
</ng-template>

<ng-template #watchlistButton>
  <button
    [class.btn-secondary]="!isWatched"
    [class.btn-primary]="isWatched"
    class="btn btn-block text-center" (click)="toggleWatchList()">
    <div *ngIf="isWatched" class="in-watchlist watchlist-state"
         mdbTooltip="Remove from Watchlist" placement="top">Remove from Watchlist
    </div>

    <div *ngIf="!isWatched" class="not-in-watchlist watchlist-state"
         mdbTooltip="Add to Watchlist" placement="top">Add to Watchlist
    </div>
  </button>
</ng-template>

<app-setup-payment [checkPayment]="true" *ngIf="checkPayment"></app-setup-payment>
