#bidbox-widget {

  .offer-amount-input {
    font-size: 18px;
  }

  .loadingSpacer {
    min-height: 250px;
  }

  .bid-too-low {
    font-weight: 500;
  }

  .submit-bid-btn, .submit-bid-btn:focus {
    background-color: var(--primaryButtonColour) !important;
  }

  .accepted {
    color: var(--successColour);
    font-weight: 600;
  }

  .accepted-2 {
    font-size: 0.875rem;
    font-weight: 500;
  }

  .buy-now-price {

    font-weight: 600;
    font-size: 1.8rem;
    line-height: 1.8rem;
  }

  .underwrite {
    font-size: 0.875rem;

    .reserve-price {
      font-size: 0.9rem;
      font-weight: 600;
    }
  }

  #bidbox {
    padding-bottom: 5px;
    border: 2px solid var(--secondaryButtonColour);
  }

  .gavel {
    font-size: 1.5rem;
  }

  .gavel-1 {
    transform: rotate(-45deg)
  }

  .gavel-2 {
  }

  .gavel-3 {
    transform: rotate(45deg);
  }


  .ggg {
    font-weight: 600;
    font-size: 0.875rem;

    &.ggg-1 {
      color: var(--successColour) !important;
    }

    &.ggg-2 {
      color: darkorange !important;
    }

    &.ggg-3 {
      color: var(--errorColour) !important;
    }
  }

  .align-buttons {

    min-width: 100px;

  }

  .proxy-bid-amt {
    font-size: 1rem;
    font-weight: bold;
    padding-bottom: 7px;
  }

  .smallprint {
    font-size: 11px;
  }

  .sold-status-sold {
    font-size: 2rem;
    font-weight: 600;
    letter-spacing: -1px;
  }

  .your-advert {
    background-color: var(--secondaryButtonColour);
    color: var(--colour3);
  }

  .box-with-you {
    border: 2px solid #0FBD56 !important;
  }

  .box-against-you {
    border: 2px solid #c00 !important;
  }

  .bid-with-vendor {
    background-color: var(--primaryButtonColour) !important;
    color: var(--colour3) !important;
  }

  .bid-too-late {
    background-color: var(--errorColour);
    color: var(--colour3);
  }

  .make-offer-button {
    font-size: 0.8rem;
    font-weight: 500;

  }

  .rejected {
    color: var(--errorColour);
    font-size: 0.875rem;
  }

  .with-vendor table td {
    font-weight: bold;
    line-height: 22px;

    &.tick {
      font-size: 30px;
      padding-right: 10px;
    }
  }

  .accepted table td {
    font-weight: bold;
    line-height: 22px;

    &.tick {
      font-size: 30px;
      padding-right: 10px;
    }
  }

  .lot-ended {
    font-size: 14px;
  }

  .won {
    background-color: var(--successColour);
    color: var(--colour3);
    font-weight: 400;
    cursor: pointer;
    font-size: 0.875rem;
    padding: 4px;

    .info {
      font-size: 0.7rem;
      vertical-align: middle;
    }
  }

  .sold-status-provisional {
    font-size: 1.5rem;
    font-weight: 600;
  }

  .sold-status-withdrawn, .sold-status-unsold {
    font-size: 1.2rem;
    font-weight: 600;
    text-transform: uppercase;
  }

  .sold-status-title {
    font-size: 0.875rem;
    font-weight: 400;
  }

  .confirm-bin-button {
    min-width: 100px;
  }

  .price-label {
    text-transform: uppercase;
    font-weight: 600;
    font-size: 0.875rem;
  }

  .price {

    font-weight: 700;
    font-size: 2.5rem;
    line-height: 2.5rem;
    color: var(--linkColour);

    &.reserve-price {
      font-size: 1rem;
    }
  }

  .delivery-smallprint {

    font-size: 0.8rem;
    font-weight: 400;
  }

  /*
  background-color: #034FA0;
   */

  .reserve-status {

    font-weight: 500;
    font-size: 0.9rem;
    text-align: center;
    background-color: #e9ecf0;
    color: var(--textColour);
    display: inline-block;
    line-height: 1.4rem;
    border-radius: 0.7rem;
    padding: 0 10px;

  }

  .fees {

    font-size: 13px;
    font-weight: 400;
    color: var(--textLabelColour);
  }

  .time-left {
    text-align: center;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    margin-top: 15px;

    .timer-words {
      font-weight: 700;
      font-size: 0.9rem;
      color: var(--linkColour);

    }
  }

  .buy-now-label {

    font-weight: 500;

  }

  .bid-entry {

    padding: 16px !important;

    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;


  }

  .your-max-bid {

    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    color: var(--linkColour);

    .your-max-bid-amount {
      font-weight: 700;
      font-size: 0.9rem;
      color: var(--linkColour);

    }
  }

  .currency-symbol {
    position: absolute;
    left: 11px;
    top: 1px;
    display: inline-block;
    font-size: 24px;
    line-height: 52px;
    font-weight: 400;
    color: var(--linkColour);
  }

  #bidAmt2 {

    font-weight: 700;
    font-size: 24px;
    line-height: 24px;
    padding-left: 35px !important;
    padding-top: 8px;
    padding-bottom: 8px;
    border: 1.5px solid var(--primaryButtonColour) !important;

    &::placeholder {
      color: #808080;
      font-size: 18px;
      line-height: 18px;
      display: inline-block;
      letter-spacing: -1px;
      transform: translateY(-1px);
    }
  }

  .table-label {

    color: var(--textLabelColour);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;

  }

  .bidding-position {
    font-weight: 500;
    padding: 5px;
    color: var(--textColour);
    background-color: #f0f0f0;
    font-size: 0.85rem;
    border-top-right-radius: calc(var(--widgetBorderRadius) - 1px);
    border-top-left-radius: calc(var(--widgetBorderRadius) - 1px);
  }

  .w-40 {
    width: 40%;
  }

  .w-60 {
    width: 60%;
  }

  .bid-state {
    display: flex;
    align-items: center;

    .bid-state-inner {
      padding-top: 15px;
      padding-bottom: 15px;
      flex-grow: 1;
    }
  }

  .timer-date {

    font-size: 11px;
    color: var(--textLabelColour);
    font-weight: 500;
    text-transform: uppercase;

  }


  .with-you {
    background-color: #09B677;
    color: var(--colour3);
  }

  .against-you {
    background-color: var(--dangerColour);
    color: var(--colour3);
  }

  #bidAmt {
    font-weight: 600 !important;
  }

  .bid-error {

    background-color: var(--dangerColour);
    color: #fff;
    padding: 3px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 3px;
    margin-top: 5px;

  }

  .ended {
    font-size: 1rem;
    font-weight: 500;
    background-color: var(--dangerColour);
    color: var(--colour3);
  }

  .tsandcs {
    font-size: 0.75rem;
  }

  #bid-amount-box {
    width: 100%;
    margin: 0 auto;
    text-align: center;
  }

  .minimum-bid {

    font-size: 12px;
    color: #666;

    .minimum-bid-amount {
      font-weight: 500;
    }
  }

  h1 {
    font-size: 24px;
    font-weight: 500;
  }

  h2 {
    font-size: 16px;
    font-weight: 400;
  }

  .watchlist-state, .message-seller {
    font-size: 0.8rem;
    font-weight: 500
  }

  .buy-now-time-left {
    text-align: center;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.75rem;
    margin-top: 15px;
  }
}
