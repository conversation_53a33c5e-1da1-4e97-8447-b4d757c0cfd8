import {Component, Input, OnInit, ViewChild} from '@angular/core';
import {ModalDirective, ToastService} from 'ng-uikit-pro-standard';
import {LoggerService} from '../../../../global/services';
import {AdvertViewContactSellerDTO, InMailDTO, User} from '../../../../global/interfaces';
import {InMailService, VehicleService} from '../../../../services';
import {StatusEnum} from "../../../../global/enums";

@Component({
  selector: 'app-advert-contact-seller',
  templateUrl: './advert-contact-seller.component.html',
  styleUrls: ['./advert-contact-seller.component.scss']
})
export class AdvertContactSellerComponent implements OnInit {

  constructor(
    private toast: ToastService,
    private inMailService: InMailService,
    private logService: LoggerService,
    private vehicleService: VehicleService) {
  }

  @Input('override') override: boolean = false;

  // tslint:disable-next-line:variable-name
  private _advertId: string;
  @Input('advertId') set advertId(value: string) {
    this._advertId = value;
  }

  get advertId(): string {
    return this._advertId;
  }

  sellerMessage: string;
  @Input() user: User;

  @ViewChild("messageSellerModal") messageSellerModal: ModalDirective;

  logger = this.logService.taggedLogger(this.constructor?.name);

  info: AdvertViewContactSellerDTO;

  ngOnInit(): void {
  }

  async loadInfo() {
    return this.vehicleService.getContactSellerDataByAdvert(this.advertId, true).then(res => {
      console.log("RES ", res);
      this.info = res;
    });
  }

  contactSeller() {
    this.logger.debug("Sending message: ", this.sellerMessage, " to seller");

    if (!this.sellerMessage || this.sellerMessage.trim() == "") {
      this.toast.error("Enter a message to send", "Error", {opacity: 0.98});
      return;
    }

    // construct a seller message from the advert/vehicle
    const dto = this.getSellerMessage();
    this.inMailService.sendMail(dto).then(result => {
      if (result) {
        this.toast.info("Message sent to seller", "Success", {opacity: 0.9});
      }
    }).catch(error => {
      this.toast.error("Failed to send message", "Error", {opacity: 0.98});
    });
  }

  getSellerMessage(): InMailDTO {
    const dto = {
      fromContactId: this.user.contactId,
      advertId: this.advertId,
      subject: `Question regarding ${this.info.plateName.trim()}
        ${this.info.makeName} ${this.info.modelName}`,
      body: this.sellerMessage,
      statusId: StatusEnum.Active,
      isPrivate: true
    } as InMailDTO;

    return dto;
  }

  async initMessage() {
    this.messageSellerModal.show();
    this.loadInfo().then(() => {
    });
  }
}
