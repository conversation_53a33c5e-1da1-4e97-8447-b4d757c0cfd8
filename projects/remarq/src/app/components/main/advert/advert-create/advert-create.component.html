<div mdbModal #vehicleInfoModal="mdbModal" class="addVehicleModal modal fade" tabindex="-1"
     id="vehicleEntry"
     role="dialog"
     aria-labelledby="addVehicleModal" aria-hidden="true" [config]="{backdrop: 'static', keyboard: false}">

  <div class="modal-dialog-centered" *ngIf="form">

    <div class="modal-dialog" role="document">

      <div class="modal-content mb-5">

        <div class="modal-header">
          <span *ngIf="enterVrm && !gettingVehicleInfo && !importingVehicle">Create Advert</span>
          <span *ngIf="importingVehicle">Importing vehicle data, please wait...</span>
          <span *ngIf="gettingVehicleInfo && !importingVehicle">Confirm vehicle information</span>
        </div>

        <form [formGroup]="form" #ngForm="ngForm" (ngSubmit)="vrmEntered()">

          <div class="modal-body">

            <div *ngIf="enterVrm">


              <div class="md-form">

                <input mdbInput id="vrm" class="float-label form-control text-uppercase" style="font-weight: 500;"
                       type="text"
                       tabindex="1"
                       formControlName="vrm"/>

                <label for="vrm">Vehicle Registration</label>

                <div *ngIf="ngForm.submitted && formCtrls.vrm.invalid" class="error-message">
                  <div>Please enter a valid registration</div>
                </div>
              </div>

              <div class="md-form">

                <input mdbInput id="odometer" class="float-label form-control text-uppercase" style="font-weight: 500;"
                       type="text"
                       tabindex="1"
                       formControlName="odometer"/>

                <label for="odometer">Vehicle Mileage</label>

                <div *ngIf="ngForm.submitted && formCtrls.odometer.invalid" class="error-message">
                  <div>Please enter a valid mileage</div>
                </div>
              </div>


            </div>
            <div *ngIf="confirmVehicleData">
              <div class="">
                <div class="vrm-style confirm-vrm">{{ newVRM }}</div>
                <div class="make-model-deriv infobar-cell">
                  <div class="make-model">{{ newVehicleLookupData.makeName }} <span
                    *ngIf="newVehicleLookupData.modelName != newVehicleLookupData.makeName">{{ newVehicleLookupData.modelName }}</span>
                  </div>
                  <div class="deriv">{{ newVehicleLookupData.derivName }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <div *ngIf="enterVrm">
              <div class="text-right">
                <button class="btn btn-rounded btn-tertiary-outline ml-2"
                        tabindex="4"
                        type="button" (click)="cancelVRMInput()">Cancel
                </button>
                <button class="btn btn-rounded btn-tertiary ml-2"
                        tabindex="3"
                        style="min-width: 80px;" type="submit" [disabled]="gettingVehicleInfo">
                  <span *ngIf="!gettingVehicleInfo">Continue</span>
                  <span *ngIf="gettingVehicleInfo"><i class="fa fa-spin fa-spinner"></i></span>
                </button>
              </div>
            </div>
            <div *ngIf="confirmVehicleData" class="flex-grow-1">
              <div class="d-flex flex-wrap">
                <div class="flex-grow-1">

                  <button class="btn btn-sm btn-tertiary-outline" type="button" (click)="cancelVRMInput()">Cancel
                  </button>
                  <button class="btn btn-sm btn-tertiary-outline ml-2" tabindex="k" type="button"
                          (click)="vrmPrompt()">
                    Back
                  </button>
                </div>
                <div class="flex-shrink-1 text-right">
                  <button [disabled]="importingVehicle" class="btn btn-sm btn-tertiary" type="submit"
                          (click)="addVehicleAndProceed()">
                    <span *ngIf="importingVehicle"><i
                      class="fa fa-spin fa-spinner"></i> {{ createAdvertProgress }}</span>
                    <span *ngIf="!importingVehicle">Continue</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>


<!-- scan vehicle selection modal -->
<div mdbModal #scanVehicleSelectModal="mdbModal" class="addVehicleModal modal fade" tabindex="-1"
     id="scanVehicleSelect"
     role="dialog"
     aria-labelledby="selectVehicleModal" aria-hidden="true" [config]="{backdrop: 'static', keyboard: false}">

  <div class="modal-dialog-centered">

    <div class="modal-dialog modal-md" role="document">

      <div class="modal-content mb-5">

        <div class="modal-header">
          <span>We found this !</span>
        </div>

        <div class="modal-body pt-2">

          <div class="import-explanation"><i class="fa fa-shipping-fast"></i> We can save you time by importing basic
            information including photos from your website.
          </div>

          <div *ngIf="showScanVehicleList">

            <div class="d-flex justify-content-center flex-nowrap" style="overflow-x: auto; padding-bottom: 10px;">

              <div style="flex-grow: 0; flex-basis: 155px; min-width: 155px; margin-right: 10px;"
                   *ngFor="let vehicle of scanVehicleResults" (click)="selectScanVehicle($event, vehicle)"
                   class="order-1 pb-2 pb-md-0">

                <div class="image-box">
                  <img style="width: 100%; border-radius: 5px; border: 1px solid #eee;"
                       src="{{ imageService.sizedImageUrl(vehicle.primaryImageURL,null, 100) }}" alt="">
                </div>

                <div class="under-image-box">

                  <div class="import-description">
                    {{ vehicle.plateName }}
                    {{ vehicle.makeName }}
                    {{ vehicle.modelName }}
                    {{ vehicle.transmissionTypeName }}
                    {{ vehicle.fuelTypeName }}
                    {{ vehicle.odometer | number }}miles
                  </div>
                  <div class="text-center pt-2">
                    <span class="btn btn-xs btn-primary">&nbsp;Select&nbsp;</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="mt-3 text-right">
              <button class="btn btn-outline-primary" tabindex="k" type="button" (click)="standardVRMlookup()">
                None of the above
              </button>
            </div>
          </div>

          <div *ngIf="showScanVehicleImages">

            <div class="chosen1">
              {{ selectedVehicle.plateName }}
              {{ selectedVehicle.makeName }}
              {{ selectedVehicle.modelName }}
              ({{ selectedVehicle.vrm }})
            </div>
            <div class="chosen2">
              {{ selectedVehicle.derivName }}
              {{ selectedVehicle.transmissionTypeName }}
              {{ selectedVehicle.fuelTypeName }}
              {{ selectedVehicle.bodyTypeName }}
              {{ selectedVehicle.odometer | number }}miles
            </div>

            <div *ngFor="let image of scanVehicleImages">
              <div (click)="toggleStatus(image)"
                   class="scanVehicleImageBlock"
                   style="background-image: url('{{ image.originalUrl }}');">

                <div
                  [class]="image.import ? 'import-image' : 'do-not-import'"
                  style="position: absolute; text-align: center; bottom: 0; right: 0; width: 25px; height: 25px; border-top-left-radius: 4px;">
                  <i class="fa fa-check" *ngIf="image.import"></i>
                  <i class="fa fa-times" *ngIf="!image.import"></i>
                </div>
              </div>
            </div>

            <div style="clear: both;"></div>

            <div class="mt-3 text-right">
              <button class="btn btn-outline-primary mr-2" tabindex="k" type="button" (click)="backToScanSelect()">
                Back
              </button>
              <button [disabled]="importingVehicle" class="btn btn-primary" tabindex="k" type="button"
                      (click)="continueScanImageSelection()">
                <span *ngIf="importingVehicle"><i class="fa fa-spin fa-spinner"></i> {{ createAdvertProgress }}</span>
                <span *ngIf="!importingVehicle">Yes, continue</span>
              </button>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</div>

<!--
    vrm
    make
    model
    derivative
    engine_cc
    transmission_type
    body_type
    date_of_registration
    doors
    vehicle_type
    colour
    co2
    imported
    weight
    euro_status
    fuel_type
    previous_keepers
    bhp
    odometer
    odometerUnit
-->

<!-- end of scan vehicle selection modal -->


<!-- modal for no address prompt -->
<div mdbModal #noAddrModal="mdbModal" class="modal fade" tabindex="-1"
     [config]="{backdrop: false, ignoreBackdropClick: true}"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        No Address Created
      </div>
      <div class="modal-body">
        Address required for listing a vehicle.<br>
        Click the button below to go to the settings screen.
      </div>
      <div class="modal-footer justify-content-center">
        <button type="button" class="btn btn-primary  waves-effect" mdbWavesEffect data-dismiss="modal"
                (click)="goToSettings()">Take me there
        </button>
      </div>
    </div>
  </div>
</div>
