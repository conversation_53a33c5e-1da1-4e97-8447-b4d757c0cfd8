<div class="widget-header-style indent-on-mobile">Location</div>
<div class="widget widget-padding indent-on-mobile" id="location-widget">


  <div class="pb-2" *ngIf="location?.countyName">
    <i class="fa fa-map-marker-alt"></i> <span class="location">{{ location.countyName }}</span>
  </div>

  <div class="delivery-label">We will deliver this vehicle to <span class="postcode">{{ currentUser?.contactPostcode || "you" }}</span> for:
    <span class="cost">&pound;{{ (movementPrice?.standardPrice?.toFixed(0)) || "Call" }}</span>
    <span class="vat"> +VAT</span>
  </div>
  <div class="pt-2" >
    <div style="height: 350px; width: 100%; padding: 0; border: 1px solid rgba(50, 50, 50, 0.2); border-radius: 12px; overflow: hidden">
      <app-google-maps-script
        [zoom]="7"
        [latitude]="location?.countyLat"
        [longitude]="location?.countyLng"
        [streetViewControl]="false"
        [mapTypeControl]="false"
        [fullscreenControl]="false"
        [placeIds]="[location?.countyPlaceId]"
        *ngIf="location"
      >
      </app-google-maps-script>
    </div>
  </div>
  <div class="text-right">
    <img class="engineius-logo mt-1" src="/assets/images/home/<USER>" alt="Engineius" />
  </div>
</div>


