import {Component, Input, OnInit} from '@angular/core';
import {AddressService} from '../../../../services';
import {UserService} from '../../../../global/services';
import {AdvertViewAdvertLocationDTO, MovementPriceDTO, User} from '../../../../global/interfaces';

@Component({
  selector: 'app-advert-location-map',
  templateUrl: './advert-location-map.component.html',
  styleUrls: ['./advert-location-map.component.scss']
})


export class AdvertLocationMapComponent implements OnInit {

  currentUser: User;
  price: number = null;
  movementPrice: MovementPriceDTO;

  constructor(private addressService: AddressService, private userService: UserService) {
  }

  // tslint:disable-next-line:variable-name
  private _advertId: string;
  @Input('advertId') set advertId(value: string) {
    this._advertId = value;
    if (value) {
      this.loadInfo();
    }
  }

  get advertId(): string {
    return this._advertId;
  }

  location: AdvertViewAdvertLocationDTO;
  contactPostcode: string;

  ngOnInit(): void {

    this.userService.loadCurrentUser().then((x) => {
      this.currentUser = this.userService.CurrentUser;
      this.addressService.getMovementPrice(this.advertId, this.currentUser.contactPostcode).then((x: MovementPriceDTO) => {
        this.movementPrice = x;
        // this.movementPrice.standardPrice = 200;
      }).catch((x) => {
        console.log("ERROR ", x);
      });
    });
  }

  ngAfterViewInit() {
  }

  loadInfo() {
    this.addressService.getAdvertLocation(this.advertId, true).then(res => {
      this.location = res;
    });
  }
}
