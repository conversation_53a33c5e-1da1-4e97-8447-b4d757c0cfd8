<div *ngIf="canProxyBid" class="px-3">
  <div>
  </div>
  <div *ngIf="b != null && b.fa && advert && advert.currentPrice < b.mb.bidAmt" class="mb-2 your-proxy-bid">
    Your Proxy Bid: {{ b.mb.bidAmt | customCurrency }}
  </div>
  <div>
    <div style="position: relative">
      <input (keydown.enter)="submitProxyBid()"
             [(ngModel)]="proxyBid"
             id="bidAmt3"
             placeholder="Enter Proxy Bid"
             [disabled]="yourAdvert" class="form-control" mdbInput>
      <div class="currency-symbol">&pound;</div>
    </div>
    <div class="proxy-bid-desc mb-1">
      Enter your maximum bid for this vehicle.
    </div>
    <div class="mt-2">
      <button (click)="submitProxyBid()"
              [disabled]="wait == true || yourAdvert" class="btn btn-primary btn-block submit-bid-button">
        <span *ngIf="!wait">Submit Bid</span>
        <i *ngIf="wait" class="fa fa-spin fa-spinner"></i>
      </button>
    </div>
  </div>
</div>
