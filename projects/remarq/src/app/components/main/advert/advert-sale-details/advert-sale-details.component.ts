import {Component, Input, OnChanges, OnInit, SimpleChanges} from '@angular/core';
import {AdvertService, EventService, URLService} from '../../../../services';
import {AdvertDTO, BaseSearchDTO, BillDTO} from "../../../../global/interfaces";
import {ImageService, LoggerService} from "../../../../global/services/index";
import {CustomerOrderTypeEnum} from "../../../../global/enums/index";

@Component({
  selector: 'app-advert-sale-details',
  templateUrl: './advert-sale-details.component.html',
  styleUrls: ['./advert-sale-details.component.scss']
})
export class AdvertSaleDetailsComponent {

  advertData: AdvertDTO;
  billData: BillDTO;
  hasDeal = false;
  isBuyer = false;
  isSeller = false;

  // tslint:disable-next-line:variable-name
  private _advertId: string;
  @Input('advertId') set advertId(value: string) {
    this._advertId = value;
    if (value) {
      void this.loadAdvertData();
    }
  }
  get advertId(): string {
    return this._advertId;
  }

  constructor(
    private advertService: AdvertService,
    public url: URLService,
    public imageService: ImageService,
    private eventService: EventService,
    private logService: LoggerService) {
  }

  logger = this.logService.taggedLogger(this.constructor?.name);

  @Input() contactId: string;

  async loadAdvertData() {
    if (!this.advertId) {
      this.logger.log("NO ADVERT ID");
      return;
    }

    const searchDTO = {component: "AdvertDeal"} as BaseSearchDTO;

    await this.advertService.getAdvert(this.advertId, searchDTO).then(res => {
        this.logger.log("** RESULT: ", res);
        this.advertData = res.advert;
      }
    );

    if (!this.advertData) {
      this.logger.warn("NO ADVERT DATA");
      return;
    }

    if (this.advertData.activeDeal) {
      this.hasDeal = true;

      if (!this.advertData.activeDeal.buyerContact) {
        this.logger.warn(`Active deal ${this.advertData.activeDeal.id} has no buyer contact`);
        return;
      }

      if (!this.advertData.activeDeal.sellerContact) {
        this.logger.warn(`Active deal ${this.advertData.activeDeal.id} has no seller contact`);
        return;
      }

      this.isBuyer = this.advertData.activeDeal.buyerContact?.id == this.contactId;
      this.isSeller = this.advertData.activeDeal.sellerContact?.id == this.contactId;

      if (!this.advertData.activeDeal.customerOrders || this.advertData.activeDeal.customerOrders?.length == 0) {
        this.logger.error(`ERROR: no customer orders associated with deal ${this.advertData.activeDeal.id}`);
        return;
      }

      // if we're the seller, get the Bill from the sale order
      if (this.isSeller) {
        let sale = this.advertData.activeDeal.customerOrders.filter(x => x.orderType == CustomerOrderTypeEnum.Sale);
        if (sale) {
          this.billData = sale[0].bill;
        } else {
          this.logger.warn("No sale data found");
        }
      } else if (this.isBuyer) {
        let purchase = this.advertData.activeDeal.customerOrders.filter(x => x.orderType == CustomerOrderTypeEnum.Purchase);
        if (purchase) {
          this.billData = purchase[0].bill;
        } else {
          this.logger.warn("No sale data found");
        }
      } else {
        this.logger.error("Current contact is neither the buyer or seller in this advert");
      }

    } else {
    }
  }

  payInvoice(bill: BillDTO) {
    // raise event to show payment modal
    this.eventService.PayInvoice.emit(bill);
  }
}
