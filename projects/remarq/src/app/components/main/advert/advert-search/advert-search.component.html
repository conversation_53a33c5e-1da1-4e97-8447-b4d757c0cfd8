<header id="advert-search-nav">

  <!-- Side bar filter list -->
  <mdb-side-nav #sidenav [fixed]="true" [sidenavBreakpoint]="1200">

    <div class="text-center mt-2" *ngIf="isLoading">
      <i class="fa fa-spinner fa-spin fa-2x"></i>
      <h1 class="mt-2">Loading...</h1>
    </div>

    <div #navLinks>

      <div *ngIf="keywordSearch">
        <ul>
          <li class="nav-item"><a class="nav-link"><span class="nav-label">Keywords</span></a></li>
        </ul>
        <div class="selected-items-container">
          <div class="chosen-items">
            &bull; {{ keywordSearch }}
            <div class="reset-filter-inline">
                <span *ngIf="keywordSearch" (click)="resetKeywordFilter()">
                  <i class="fa fa-times-circle"></i>
                </span>
            </div>
          </div>
        </div>
      </div>

      <links *ngIf="!isLoading">
        <div *ngFor="let filterItem of filterList">
          <ul>
            <li class="nav-item" id="filterItem-{{ filterItem.filterName }}" [ngClass]="{'disabled': !filterItem.enabled}">
              <a class="nav-link" [ngClass]="{'disabled': !filterItem.enabled}" (click)="fetchCounts($event, filterItem)"
                 id="filterItem.filter_label">
                <span class="nav-label">{{ filterItem.filterName }}</span>
                <span class="nav-caret"><i class="fa fa-angle-right"></i></span>
              </a>
              <div class="selected-items-container">

                <div class="chosen-items">

                  <div *ngIf="isCheckbox(filterItem) && filterItem.selectedItems.length > 0">
                    <div *ngFor="let selected of filterItem.selectedItems; last as isLast">
                      <span class="selected-item {{ filterItem.filterName | nospaces | lowercase }}"
                            *ngIf="selected.checked"
                            (click)="resetSelectedItem(filterItem, selected)"> &bull; {{ selected.name }}

                        <span *ngIf="selected.checked" class="reset-filter-inline"><i
                          class="fa fa-times-circle"></i></span>

                      </span>
                    </div>
                  </div>

                  <!--
                  <div *ngIf="!filterItem.enabled">
                    <span *ngIf="filterItem.searchField == advertSearchField.Model">Select Make(s)</span>
                    <span *ngIf="filterItem.searchField == advertSearchField.Deriv">Select Model(s)</span>
                  </div>
                  -->

                  <span *ngIf="isRange(filterItem)">
                    <span
                      class="selected-item">{{advertSearchService.getRangeValuesString(filterItem.searchField, filterItem.min, filterItem.max)}}</span>
                  </span>

                  <span class="reset-filter-inline" *ngIf="filterItem.filterType != searchFilterType.Checkbox">
                  <span *ngIf="hasFilterSelected(filterItem)" (click)="resetFilter(filterItem)" class="selected-item">
                    <i class="fa fa-times-circle"></i>
                  </span>
                  </span>
                </div>

              </div>
            </li>
          </ul>
        </div>
      </links>
    </div>

    <div class="view-results">
      <div class="text-center result-count">
        <button class="btn ungrouped btn-primary filter-button" (click)="hideFilterThenRunSearch()">
          View {{ resultsCount }} Results
        </button>
      </div>
      <div class="text-center clear-filters">
        <button class="btn ungrouped btn-primary filter-button-alt" (click)="clearAllFilters()">
          <mdb-icon fas icon="undo"></mdb-icon>
          Clear all filters
        </button>
      </div>
      <div class="text-center clear-filters">
        <button class="btn ungrouped btn-primary filter-button-alt" (click)="showSaveSearchDialog()">
          <mdb-icon fas icon="save"></mdb-icon>
          Save Search
        </button>
      </div>
    </div>

  </mdb-side-nav>

  <!-- Start of Filter popout -->
  <div ignoreOutsideClasses="nav-label,mdb-select-dropdown,nav-link,nav-caret,fa-angle-right"
       (clickOutside)="hideFilter()" id="filterWrapper">
    <!-- list all items for the currently selected filterItem -->

    <div *ngIf="selectedFilter && selectedFilter.selectedItems"
         #flyOut class="filterValues" [style]="{'top':selectedFilterTop}">

      <!-- CHECKBOX type popout -->
      <div *ngIf="isCheckbox(selectedFilter)">
        <div *ngFor="let item of selectedFilter?.selectedItems;">
          <div *ngIf="showGroupHeader(item)" class="group-header">
            {{item.groupName}}
          </div>

          <div class="form-check form-check-inline pl-1">

            <!-- disable checkboxes if we have max filters selected for make -->
            <mdb-checkbox [(ngModel)]="item.checked" (change)="checkboxChanged()"
                          id="filter-{{ item.entityId}}" [disabled]="selectedFilter.searchField == advertSearchField.Make && selectedMakes && selectedMakes.length == 5 && !item.checked"></mdb-checkbox>

            <label class="form-check-label" [attr.for]="'filter-' + item.entityId">
              <span class="filterLabel">{{ item.name }}</span>
              <span class="filterCount">({{ item.count }})</span>
            </label>
          </div>
        </div>
      </div>

      <!-- RANGE type popout -->
      <div *ngIf="isRange(selectedFilter)">

        <div class="row">

          <div class="col-md-6">
            <div class="filter-label">From {{ selectedFilter.filterName }}:</div>
            <div class="narrow-select">

              <mdb-select [outline]="true"
                          [(ngModel)]="selectedFilter.min" (selected)="rangeMinChanged(selectedFilter)"
                          [options]="selectedFilter.dropdownItemsMin"
                          placeholder="Select min"></mdb-select>
            </div>
          </div>

          <div class="col-md-6">
            <div class="filter-label">To {{ selectedFilter.filterName }}:</div>
            <div class="narrow-select">
              <mdb-select [outline]="true"
                          [(ngModel)]="selectedFilter.max" (selected)="rangeMaxChanged(selectedFilter)"
                          [options]="selectedFilter.dropdownItemsMax"
                          placeholder="Select max"></mdb-select>
            </div>
          </div>
        </div>

      </div>

      <!-- Action buttons -->
      <div class="row pt-3">

        <div class="col-md-6">

          <div class="text-left">
            <span class="btn btn-primary btn-sm action-button" (click)="setQueryURL()">Done</span>
          </div>
        </div>
        <div class="col-md-6">
          <div class="text-right">
            <button class="btn btn-sm btn-default action-button" (click)="resetFilter(selectedFilter)">
              <mdb-icon fas icon="undo"></mdb-icon>
              Clear
            </button>
          </div>
        </div>
      </div>

      <!-- status text -->
      <div *ngIf="selectedFilter.searchField == advertSearchField.Make && selectedMakes && selectedMakes.length == 5">
        * Maximum number of Makes selected
      </div>

    </div>
  </div>

</header>
<!--/.Double navigation-->

<!--Main Layout-->
<main id="advert-search-main-panel">
  <div class="container-fluid mt-3">

    <div *ngIf="filterCustomerId != null" class="only-customer">

      Showing only customer:  {{ filterCustomerId }}

    </div>

    <div *ngIf="watchlistService.watchlistLoaded">

      <div class="">
        <a (click)="sidenav.toggle()"
           class="d-block d-sm-inline-block d-xl-none button-collapse hidden-nav-button-collapse mx-0 mb-1">
          <button class="btn btn-sm btn-primary btn-block">
            <mdb-icon fas icon="bars"></mdb-icon>
            Search Filter
          </button>
        </a>
      </div>

      <div class="d-flex flex-wrap pt-1 mb-2">

        <div class="flex-shrink-1">

          <div class="btn-group select-sale-type">
            <label *ngFor="let option of advertSearchService.saleFilters"
                   class="btn btn-sm"
                   mdbRadio="{{ option.value }}"
                   [(ngModel)]="saleTypeSelected"
                   (click)="saleFilterSelected(option)">
              {{option.label}}
            </label>
          </div>
        </div>
        <div class="flex-grow-1">
          <div class="ml-2 result-count">{{getSearchHeader()}}</div>
        </div>
        <div class="flex-shrink-1 pr-3 top-row-select">
          <div class="input-group">
            <div class="input-group-prepend">
              <div class="input-group-text">
                Sort
              </div>
            </div>
            <div style="display: inline-block; width: 170px;">
              <mdb-select [outline]="true"
                          [(ngModel)]="searchOptions.sorting" (selected)="sortingOptionChanged()"
                          [options]="advertSearchService.sortingTypes"
                          placeholder="Select Sorting"></mdb-select>
            </div>
          </div>
        </div>
        <div class="flex-shrink-1 pr-3 top-row-select" style="white-space: nowrap">
          <div class="input-group">
            <div class="input-group-prepend">
              <div class="input-group-text">
                Filter
              </div>
            </div>
            <div style="width: 140px;">
              <mdb-select [outline]="true"
                          [style]="{'display':'inline-block;'}"
                          [(ngModel)]="searchOptions.sortingFilter" (selected)="sortingFilterOptionChanged()"
                          [options]="advertSearchService.sortingFilters"
                          placeholder="Sorting Filter"></mdb-select>
            </div>
          </div>
        </div>

        <div class="flex-grow-1 flex-sm-grow-0 mb-2">
          <button class="btn btn-sm btn-tertiary btn-block" (click)="showSaveSearchDialog()"><i class="fa fa-save"></i>
            Save Search
          </button>
        </div>
      </div>

      <div *ngIf="!sphAdverts" id="advert-search-waiting-widget">
        <div *ngFor="let x of [1,2,3]" class="ghost">
          <div class="ghost-waiting ghost-result-item widget widget-border">
          </div>
        </div>
      </div>

      <div *ngIf="sphAdverts && sphAdverts.length == 0">

        <div class="widget padding mb-2">

          <div class="d-flex flex-wrap">
            <div class="flex-shrink-1 flex-grow-0">
              <div class="px-3">
                <img src="/assets/images/svg/sad.svg" class="svg-color" height="90" width="90" style="fill: #f00; color: #f00;">
              </div>
            </div>
            <div class="flex-grow-1">
              <div class="pl-3">
                <div class="pt-2">
                  <h1>There are no results for this search</h1>
                </div>
                <div class="mt-3">
                  <h2>Try changing your last filter or resetting the form</h2>
                </div>
              </div>
            </div>
          </div>

        </div>

      </div>

      <div *ngFor="let sphAdvert of sphAdverts | paginate : {
                itemsPerPage: tableSize,
                currentPage: page,
                totalItems: count
              };">
        <app-advert-search-summary [advert]="sphAdvert.advert" [user]="user"></app-advert-search-summary>
      </div>

      <div class="row flex-row-reverse mb-3">
        <div class="col-md-6 text-md-right text-center">
          <div class="pt-1 pagination-wrapper">
            <pagination-controls
              responsive="true"
              class="paginator"
              previousLabel="Prev"
              nextLabel="Next"
              (pageChange)="onTableDataChange($event)">
            </pagination-controls>
          </div>
        </div>
        <div class="col-md-6 text-center text-md-left bottom-row-select">
          <span class="results-per-page" style="padding-right: 5px;">Results Per Page</span>
          <mdb-select
            [style]="{'display': 'inline-block', 'width':'80px'}"
            [options]="tableSizes"
            [ngModel]="pageSize"
            [outline]="true"
            (ngModelChange)="onTableSizeChange($event)"></mdb-select>
        </div>
      </div>
    </div>
  </div>
</main>

<app-saved-search-edit [isNewRecord]="true">
</app-saved-search-edit>
