
links {

  .nav-link {
    display: flex !important;
  }

  .nav-label {
    flex-grow: 1 !important;
  }

  .nav-caret {
    flex-shrink: 1 !important;
    color: #aaa !important;
  }
}

.filter-button-alt {

  background-color: #264653 !important;
  border-color: #264653 !important;

}

.only-customer {
  background-color: #c00;
  color: #fff;
  padding: 10px;
  border-radius: 5px;
}

.filterValues {
  position: fixed !important;
  width: 400px !important;
  background-color: #fff !important;
  padding: 10px !important;
  z-index: 1045 !important;
  top: var(--totalHeaderHeight);
  box-shadow: 800px 800px 0px 800px rgba(0, 0, 0, 0.52);
  -webkit-box-shadow: 800px 800px 0px 800px rgba(0, 0, 0, 0.52);
  -moz-box-shadow: 800px 800px 0px 800px rgba(0, 0, 0, 0.52);
}

.selected-filter-spacing {
  margin-bottom: 10px;
}

#advert-search-main-panel {

  padding-top: 0 !important;

}

.ghost-result-item {

  width: 100%;
  height: 162px;
  margin-bottom: 16px;
}

#advert-search-nav {

  .nav-link {
    padding: 0 1rem;
  }

  .slim.side-nav {
    width: 3rem !important;
  }

  .side-nav {
    margin-top: var(--totalHeaderHeight) !important;
    height: calc(100% - var(--totalHeaderHeight));
    width: var(--searchMenuWidth) !important;
  }
}

.filterCount {
  color: #aaa !important;
  display: inline-block !important;
  overflow-x: hidden !important;
}

.filterLabel {
  max-width: 310px !important;
  padding-right: 5px !important;
  overflow-x: hidden !important;
  text-overflow: ellipsis !important;
  display: inline-block !important;
  white-space: nowrap !important;
}

.view-results {
  position: sticky !important;
  bottom: 0px !important;
  text-align: center !important;
  width: var(--searchMenuWidth) !important;
  z-index: 9999 !important;
  background-color: #f5f5f5;
  padding-top: 1rem;
}

.group-header {

  background-color: #f4f4f4 !important;
  padding-left: 5px !important;
  font-weight: 500 !important;
  font-size: 12px !important;
  margin-top: 5px !important;
  margin-bottom: 5px !important;
  padding-top: 3px !important;
  padding-bottom: 3px !important;

}

.result-count, .results-per-page, .top-row-text {

  display: inline-block !important;
  line-height: 31px;
  font-weight: 400;
  font-size: 0.875rem !important

}

.selected-items-container {
  display: flex !important;
  padding-right: 10px !important;
  padding-left: 16px !important;

  .chosen-items {
    flex-grow: 1 !important;
    font-size: 10px !important;
    padding-right: 5px !important;
    font-weight: 500 !important;
    padding-left: 7px;
    line-height: 18px;
  }

  .reset-filter-box {
    flex-shrink: 1 !important;
    color: #f66 !important;
    opacity: 0.8 !important;
    cursor: pointer !important;
  }

  .reset-filter-inline {
    float: right !important;
    display: inline-block;
    font-size: 12px;
  }

}

.filter-label {
  font-size: 0.875rem;
  font-weight: 500;
  padding-bottom: 5px;
}

.side-nav .nav-item .nav-link {
  border-top: 1px solid #eee;
  padding-top: 5px !important;
  padding-bottom: 5px !important;
}

.selected-item {

  cursor: pointer !important;
  line-height: 20px;

  &:hover {
    background-color: rgba(50, 50, 50, 0.1);
  }

  .fa-times-circle:before {
    background-color: #fff;
    border-radius: 6px;
    box-shadow: inset 0 0 0 2px #c00;
    box-sizing: border-box;
    height: 12px;
    width: 12px;
    font-size: 12px;
    line-height: 12px;
    color: #c00 !important;
  }
}

.view-results {

  button {
    margin-bottom: 10px;
  }

  .result-count button {

    font-size: 16px;
  }

  .clear-filters button {

    font-size: 12px;
    opacity: 0.6;

    &:hover {
      opacity: 1;
    }
  }
}

.paginator {

  ul {
    padding-inline-start: 0 !important;
    margin-bottom: 0 !important;
  }
}

.btn-group .btn {

  border-color: var(--linkColour);
  font-weight: 400;

  &.active {
    background-color: var(--linkColour);
    color: #fff;
  }
}

.top-row-select, .bottom-row-select {

  margin-bottom: 0.5rem;

  .input-group-text {
    padding: 0.15rem 0.5rem !important;
    font-size: 0.875rem !important;
  }

  mdb-select.mdb-select-outline .single {
    padding: 0.15rem 0 !important;

    .value {
      font-size: 0.875rem !important;
    }

    border: 1px solid var(--softInputBorderColour) !important;
  }

  .mdb-select-toggle {
    line-height: 1.7rem;
    width: 26px !important;

    &:before {
      width: 26px !important;
    }
  }

}

.disabled-filter, .disabled {
  pointer-events:none; //This makes it not clickable
  opacity:0.6;         //This grays it out to look disabled
}

@media (min-width: 1200px) {

  #advert-search-main-panel {

    padding-left: calc(var(--searchMenuWidth)) !important;
    margin-left: 1.5rem !important;
    margin-right: 1.5rem !important;

  }

}

@media (max-width: 765px) {
  #advert-search-nav .side-nav { margin-top: var(--headerHeight) !important; }
}

@media (max-width: 575px) {


  .filterValues {
    left: 0px !important;
    width: 100% !important;
    box-shadow: 0px 0px 10px 0px #000000;
  }
}
@media (min-width: 576px) {
  .filterValues {
    left: calc(var(--searchMenuWidth) + 1px);
  }
}

