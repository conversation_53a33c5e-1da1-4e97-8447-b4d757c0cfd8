<div class="widget widget-border pt-3 pb-2 px-3 mb-3" *ngIf="localAdvert && localVehicle">

  <div class="d-flex">
    <div class="flex-grow-1">
      <div class="vehicle-heading" [innerHTML]="getVehicleHeader()"></div>
      <div class="vehicle-details" [innerHTML]="getVehicleData()"></div>
    </div>
    <div class="flex-shrink-1">
      <div class="vrm-wrapper">
        <span class="vrm-under">{{ localVehicle?.vrm }}</span>
      </div>
    </div>
  </div>

  <div class="">

    <div class="d-flex flex-wrap grid-gap-10">

      <div class="flex-grow-1" style="flex-basis: 120px;">
        <div *ngIf="getImageSource()">
          <img class="summary-image w-100" src="{{getImageSource()}}"/>
        </div>

        <div *ngIf="!vehicle?.primaryImageURL && !primaryMedia">

          <div style="background-color: #eee; text-align: center; padding-top: 40px; padding-bottom: 40px;"
               class="summary-image">
            <i class="fa fa-3x fa-camera-retro"></i>
          </div>

        </div>
      </div>

      <div class="flex-grow-1">
        <div class="d-flex flex-wrap vehicle-pricing grid-gap-10">
          <div class="flex-grow-1">
            <table class="ad-summary-table">

              <!-- HEADER -->
              <tr>
                <td colspan="2">Sale Type:</td>
                <td>
                  <div class="sale-type sale-type-{{ saleTypeId }} text-center">
                    <span *ngIf="saleTypeId == saleTypeEnum.ManagedSale">Managed Sale</span>
                    <span *ngIf="saleTypeId == saleTypeEnum.TimedAuction">Timed Listing</span>
                    <span *ngIf="saleTypeId == saleTypeEnum.BuyItNow">Buy It Now</span>
                    <span *ngIf="saleTypeId == saleTypeEnum.Underwrite">Underwrite</span>
                  </div>
                </td>
              </tr>

              <!--- BUY IT NOW -->

              <tr *ngIf="saleTypeId == saleTypeEnum.BuyItNow">
                <td colspan="2">
                  Price:
                </td>
                <td class="summary-value">
                <span *ngIf="localAdvert.buyItNowPrice != null && localAdvert.buyItNowPrice > 0">
                  {{ getCurrencyCode() }}{{ localAdvert.buyItNowPrice | number }}
                </span>
                  <span *ngIf="localAdvert.buyItNowPrice == null || localAdvert.buyItNowPrice == 0">
                  TBA
                </span>
                </td>
              </tr>

              <tr *ngIf="saleTypeId == saleTypeEnum.BuyItNow && localAdvert.acceptBids">
                <td colspan="2">
                  Reject Bids Under:
                </td>
                <td class="summary-value">
                  {{ localAdvert.autoRejectBid ? (getCurrencyCode() + (localAdvert.autoRejectBid | number)) : 'N/A' }}
                </td>
              </tr>

              <tr *ngIf="saleTypeId == saleTypeEnum.BuyItNow && localAdvert.acceptBids">
                <td colspan="2">
                  Accept Bids Over:
                </td>
                <td class="summary-value">
                  {{ localAdvert.autoAcceptBid ? (getCurrencyCode() + (localAdvert.autoAcceptBid | number)) : 'N/A' }}
                </td>
              </tr>

              <!-- TIMED SALE -->

              <tr *ngIf="saleTypeId == saleTypeEnum.TimedAuction">
                <td colspan="2">Starting Price:</td>
                <td
                  class="summary-value">{{ localAdvert.startPrice ? (getCurrencyCode() + localAdvert.startPrice) : 'N/A' }}
                </td>
              </tr>
              <tr *ngIf="saleTypeId == saleTypeEnum.TimedAuction">
                <td colspan="2">Reserve:</td>
                <td class="summary-value">
                  {{ localAdvert.reservePrice ? (getCurrencyCode() + localAdvert.reservePrice) : 'N/A' }}
                </td>
              </tr>
            </table>
          </div>

          <div class="flex-grow-1">
            <table class="ad-summary-table">
              <!-- FOOTER -->
              <tr>
                <td>Starts:</td>
                <td>{{ localAdvert.availableDate | date: "dd/MM/YY" }}</td>
                <td>
                  {{ localAdvert.availableDate | date: "HH:mm" }}
                </td>
              </tr>
              <tr>
                <td>Ends:</td>
                <td>{{ localAdvert.endDateTime | date: "dd/MM/YY" }}</td>
                <td>
                  {{ localAdvert.endDateTime | date: "HH:mm" }}
                </td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="last-updated">Last updated {{ getUpdatedDate(localVehicle.updated) }}</div>
  </div>
</div>
