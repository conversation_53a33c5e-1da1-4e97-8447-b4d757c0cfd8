.option {
  margin-bottom: 5px;
}

.damage-intro {
  font-weight: 600;
}

.appraisal-table td { font-size: 0.875rem; }

.point {
  position: absolute;
  left: 0;
  top: 0;
  height: 32px;
  width: 32px;
  background-color: palegreen;
  border-radius: 50%;
  display: inline-block;
  z-index: 1;
  color: black;
  text-align: center;
  transform: translate(-50%, -50%);
  cursor: pointer;
  font-weight: 600;
  font-size: .875rem;
}

.tooltip-inner { max-width: 500px !important; }

.modal-width { max-width: 600px; width: 100%; }

.select-item { border-radius: 0px !important; }

.body-part-name { font-weight: 500; padding-bottom: 10px; }

.enter-text { font-size: 0.875rem;  font-weight: 400; color: var(--highlightTextColour); }

.btn-primary-outline.active {
  background-color: var(--primaryColour);
  border-color: var(--primaryColour);
  color: var(--colour3) !important;
}

.menu-menu {
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  display: inline-block;
  background-color: rgba(#f4f4f4, 0.8);
  color: #888;
  z-index: 999;
  cursor: pointer;
  border-top-right-radius: 3px;
  border-bottom-left-radius: 3px;
  text-align: center;
}

.bodyPart {

  background-color: rgba(0,0,0,0.1);
  text-align: center;
  font-weight:bold;
  margin-bottom: 3px;
  height: 24px;
}



.splat {
  width: 100%;
  height: 100%;
}

.kipper {
  width: 100%;
  position: relative;
  z-index: 0;
  text-align: center;
  background-size: contain;
  background-repeat: no-repeat;
  padding-left: 0;
  padding-right: 0;
  margin-left: auto;
  margin-right: auto;
  display: block;
  //border: 1px solid black;
  cursor: crosshair;
}

.point-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.point-loc {
  position: absolute;
  left: 0;
  top: 0;
  transform: translate(-50%, -50%);
}

.point, .point-with-photo {
  height: 32px;
  width: 32px;
  line-height: 32px;
  font-size: .75rem;
}
.point-text, .point-with-photo-text {
  height: 26px;
  width: 26px;
  line-height: 26px;
  font-size: 0.7rem;
}

.point, .point-with-photo-text, .point-text, .point-with-photo {
  border-radius: 50%;
  display: inline-block;
  z-index: 1;
  color: #fff;
  text-align: center;
  font-weight: 600;

  span {
    display: inline-block;
    height: 32px;
    width: 32px;
  }
}

.point, .point-text {
  background-color: var(--primaryColour);
}

.point-with-photo, .point-with-photo-text {
  cursor: pointer;
  background-color: var(--primaryButtonColour);
}

.x-point .tooltip-inner {
  width: 500px !important; max-width: 500px !important;
}

.itemdesc { font-size: 0.7rem; font-weight: 500; vertical-align: middle; }

.photo-count { font-size: 0.7rem; line-height: 0.7rem; font-weight: 400; }

.hover-opacity {
  opacity: 0.8;
  &:hover { opacity: 1; }
}

