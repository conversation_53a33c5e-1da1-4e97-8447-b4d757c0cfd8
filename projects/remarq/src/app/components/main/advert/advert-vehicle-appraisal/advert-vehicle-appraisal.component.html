<div class="widget widget-padding">

  <div class="header padding-top">Condition</div>

  <div class="mt-3">

    <mdb-tabset
      [tabsContentClass]="'mt-2 col-md-12'"
      [buttonClass]="'classic-tabs tabs-custom alt-style'"
      class="classic-tabs alt-style">

      <mdb-tab heading="Provenance">
        <app-advert-provenance [vehicleId]="vehicle.id"></app-advert-provenance>
      </mdb-tab>
      <mdb-tab heading="External">
        <div>
          <app-advert-vehicle-appraisal-splat [vehicleType]="vehicle.vehicleTypeId" [readOnly]="readonly"
                                              [internal]="false"
                                              [appraisal]="appraisal"></app-advert-vehicle-appraisal-splat>
        </div>
      </mdb-tab>
      <mdb-tab heading="Internal">
        <div>
          <app-advert-vehicle-appraisal-splat [vehicleType]="vehicle.vehicleTypeId" [readOnly]="readonly"
                                              [internal]="true"
                                              [appraisal]="appraisal"></app-advert-vehicle-appraisal-splat>
        </div>
      </mdb-tab>
      <mdb-tab heading="Mechanical">
        <app-vehicle-mechanical-faults [vehicleId]="vehicle.id"></app-vehicle-mechanical-faults>
      </mdb-tab>
    </mdb-tabset>
  </div>
</div>

<!-- Appraisal modal: todo: move this to component -->
<div mdbModal #appraisalModal="mdbModal" class="modal fade" tabindex="-1"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true" [config]="{backdrop: 'static'}">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close pull-right" aria-label="Close" (click)="closeAppraisalModal()">
          <span aria-hidden="true">×</span>
        </button>
        Appraisal Information
      </div>
      <div class="modal-body">
        <div style="padding-right: 10px;" *ngIf="activeAppraisal">
          <div class="md-form narrow">
            <mdb-date-picker mdbInput [(ngModel)]="activeAppraisal.appraisalDate">
            </mdb-date-picker>
            <label>Appraisal Date</label>
          </div>
          <div class="md-form narrow mt-2">
            <input mdbInput id="appraisalReference" type="text" class="float-label form-control" placeholder="Enter Ref"
                   [(ngModel)]="activeAppraisal.appraisalRef"/>
            <label for="appraisalReference">Appraisal Reference</label>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Confirmation modal: todo: replace this with mdbpopup -->
<app-delete-confirm-modal
  [showDialog]="showDeleteConfirm"
  (modalClosed)="showDeleteConfirm = false"
  (confirmDelete)="confirmDelete()"
  (cancelDelete)="cancelDelete()"></app-delete-confirm-modal>

