<form *ngIf="vehicle" [formGroup]="form">

  <div class="d-flex attention-box dvla-explain">
    <div class="flex-fill">
      <div class="pl-2">
        <div>Below is the data we have retrieved from the DVLA.</div>
        <div>Check the values are correct and then continue.</div>
      </div>
    </div>

  </div>
  <div class="warning-box" *ngIf="vehicle.hasMOTIssues" (click)="viewMOTHistory()">Issues with MOT mileage, click to
    view
  </div>

  <!-- TODO: Don't display a form, display condensed values, which are then editable if incorrect -->

  <div class="left-side mt-2">
    <div class="widget widget-border px-3">

      <div class="header padding-top mb-3">DVLA Information</div>
      <div class="row">
        <div class="col-md-6">
          <div class="select-form select">
            <mdb-select-2 [outline]="true"
                        (valueChange)="makeSelected($event)"
                        formControlName="makeId"
                        placeholder="Select make"
                        [label]="'Make'">
              <mdb-select-option *ngFor="let make of makes" [value]="make.value">{{ make.label }}</mdb-select-option>

            </mdb-select-2>
          </div>
        </div>
        <div class="col-md-6">
          <div class="select-form select">
            <mdb-select-2 [outline]="true"
                        (valueChange)="modelSelected($event)"
                        [label]="'Model'"
                        formControlName="modelId"
                        placeholder="Select model">
            <mdb-select-option *ngFor="let model of models" [value]="model.value">{{ model.label }}</mdb-select-option>
            </mdb-select-2>
          </div>
        </div>
        <div class="col-md-12">
          <div class="select-form select">
            <mdb-select-2 [outline]="true"
                        [label]="'Derivative'"
                        formControlName="derivId"
                        placeholder="Select deriv">
              <mdb-select-option *ngFor="let deriv of derivs" [value]="deriv.value">{{ deriv.label }}</mdb-select-option>
            </mdb-select-2>
          </div>
        </div>
        <div class="col-md-6">
          <div class="select-form select">
            <mdb-select-2 [outline]="true"
                        [label]="'Transmission'"
                        formControlName="transmissionTypeId"
                        placeholder="Select transmission type">
              <mdb-select-option *ngFor="let transmission of transmissionTypes"
                                 [value]="transmission.value">{{ transmission.label }}</mdb-select-option>
            </mdb-select-2>
          </div>
        </div>

        <div class="col-md-6">
          <div class="select-form select">
            <mdb-select-2 [outline]="true"
                        [label]="'Body'"
                        formControlName="bodyTypeId"
                        placeholder="Select body type">
              <mdb-select-option *ngFor="let body of bodyTypes" [value]="body.value">{{ body.label }}</mdb-select-option>
            </mdb-select-2>
          </div>
        </div>
        <div class="col-md-6">
          <div class="select-form select">
            <mdb-select-2 [outline]="true"
                        [label]="'Fuel'"
                        formControlName="fuelTypeId"
                        placeholder="Select fuel type">
              <mdb-select-option *ngFor="let fuel of fuelTypes" [value]="fuel.value">{{ fuel.label }}</mdb-select-option>
            </mdb-select-2>
          </div>
        </div>

        <div class="col-md-6">
          <div class="md-form">
            <input mdbInput type="text" class="float-label form-control"
                   formControlName="colour"
                   id="colour"/>
            <label for="colour">Colour</label>
          </div>
        </div>
        <div class="col-md-6" *ngIf="formCtrls.kerbWeight.value > 0">
          <div class="md-form">
            <input mdbInput class="form-control float-label" id="kerbWeight" formControlName="kerbWeight"/>
            <label for="kerbWeight">Kerb Weight (kg)</label>
            <div *ngIf="(formCtrls.kerbWeight.touched || formCtrls.kerbWeight.dirty) && formCtrls.kerbWeight.invalid"
                 class="error-message">
              <div *ngIf="formCtrls.kerbWeight.errors['pattern']">Please enter a valid number</div>
              <div *ngIf="!formCtrls.kerbWeight.errors['pattern']">Value exceeds upper limit
                of {{maxKerbWeight}}</div>
            </div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="md-form">
            <input mdbInput class="form-control float-label" type="number" formControlName="doors" id="doors"
                   autocomplete="off"/>
            <label for="doors">Doors</label>
            <div *ngIf="(formCtrls.doors.touched || formCtrls.doors.dirty) && formCtrls.doors.invalid"
                 class="error-message">
              <div *ngIf="formCtrls.doors.errors['pattern']">Please enter a valid number</div>
              <div *ngIf="!formCtrls.doors.errors['pattern']">Value exceeds upper limit of {{maxDoors}}</div>
            </div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="md-form">

            <input mdbInput type="date" id="dateOfReg" class="form-control float-label" formControlName="dateOfReg"
                   style="border: 0;">
            <label for="dateOfReg">MOT Expires</label>

            <div *ngIf="(formCtrls.dateOfReg.touched || formCtrls.dateOfReg.dirty) && formCtrls.dateOfReg.invalid"
                 class="error-message">
              <div>Please enter a valid date</div>
            </div>
          </div>
        </div>

        <div class="col-md-6" *ngIf="formCtrls.co2.value > 0">
          <div class="md-form">
            <input mdbInput class="form-control float-label" formControlName="co2" id="co2"/>
            <label for="co2">Co2</label>
          </div>
        </div>

      </div>
    </div>

  </div>
</form>

<div *ngIf="savingVehicle">
  <p>Saving vehicle data... <i class="la la-spinner la-spin"></i></p>
</div>


<div mdbModal #motModal="mdbModal" class="modal fade" tabindex="-1"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md" role="document">
    <div class="modal-content">
      <div class="modal-header">
        MOT History
        <button type="button" class="close pull-right" aria-label="Close" (click)="closeMOTModal()">
          <span aria-hidden="true">×</span>
        </button>
      </div>
      <div class="modal-body">
        <app-vehicle-mot-history [advertId]="advertId"></app-vehicle-mot-history>
      </div>
    </div>
  </div>
</div>
