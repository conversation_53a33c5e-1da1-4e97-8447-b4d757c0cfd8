<form *ngIf="vehicle" [formGroup]="form">
  <div class="widget widget-border px-3" id="vehicle-status-widget">

    <div class="header padding-top mb-3">Vehicle Status</div>

    <div class="row">

      <div class="col-md-6">
        <div class="md-form">
          <input mdbInput class="form-control float-label"
                 formControlName="odometer" id="odometer">
          <label for="odometer">Mileage</label>
          <div class="error-message" *ngIf="f.odometer.dirty && f.odometer.errors">
            <span *ngIf="f.odometer.errors.required">Mileage is required</span>
          </div>
        </div>
      </div>

      <div class="col-md-6 mb-3 select">
        <mdb-select-2 [outline]="true"
                      [label]="'VAT Status'"
                      formControlName="vatStatusId"
                      placeholder="Select VAT Status">

          <mdb-select-option *ngFor="let option of vatStatusOptions"
                             [value]="option.value">{{ option.label }}
          </mdb-select-option>
        </mdb-select-2>
      </div>

      <div class="col-md-6">
        <div class="md-form">
          <input mdbInput type="date" id="motExpires" class="form-control float-label" formControlName="motExpires">
          <label for="motExpires">MOT Expires</label>

          <div *ngIf="(formCtrls.motExpires.touched || formCtrls.motExpires.dirty) && formCtrls.motExpires.invalid"
               class="error-message">
            <div>Enter a valid date</div>
          </div>
        </div>
      </div>

      <!-- Reinstate later for professionally inspected vehicles
      <div class="col-md-6">

        <div class="md-form">
          <input mdbInput class="form-control float-label" formControlName="grade" id="grade"/>
          <label for="grade">Grade</label>
          <div *ngIf="(formCtrls.grade.touched || formCtrls.grade.dirty) && formCtrls.grade.invalid"
               class="error-message">
            <div *ngIf="formCtrls.grade.errors['pattern']">Please enter a valid number</div>
            <div *ngIf="!formCtrls.grade.errors['pattern']">Value exceeds upper limit of {{maxGrade}}</div>
          </div>
        </div>
      </div>
      -->

      <div class="col-md-6">
        <div class="md-form">
          <input mdbInput type="number" class="form-control float-label" formControlName="owners" id="prevOwners">
          <label for="prevOwners">Previous Owners</label>
          <div
            *ngIf="(formCtrls.owners.touched || formCtrls.owners.dirty) && formCtrls.owners.invalid"
            class="error-message">
            <div *ngIf="formCtrls.owners.errors.pattern">Enter a valid number</div>
            <div *ngIf="!formCtrls.owners.errors.pattern">Value exceeds upper limit
              of {{ maxOwners }}
            </div>
          </div>
        </div>
      </div>


      <div class="col-md-6 mb-3 select">
        <mdb-select-2 [outline]="true"
                      [label]="'Location'"
                      formControlName="addressId"
                      placeholder="Select Location">
          <mdb-select-option *ngFor="let option of addresses"
                             [value]="option.value">{{ option.label }}
          </mdb-select-option>
        </mdb-select-2>
      </div>


      <div class="col-md-6">
        <div class="md-form">
          <input mdbInput type="number" class="form-control float-label" formControlName="noOfKeys" id="noOfKeys">
          <label for="noOfKeys">Number of keys</label>
        </div>
      </div>

      <div class="col-md-6">

        <div class="md-form">
          <input mdbInput type="date" id="lastService" class="form-control float-label" formControlName="lastService">
          <label for="lastService">Last Service</label>

          <div *ngIf="(formCtrls.lastService.touched || formCtrls.lastService.dirty) && formCtrls.lastService.invalid"
               class="error-message">
            <div>Enter a valid date</div>
          </div>
        </div>
      </div>

      <div class="col-md-6 mb-4 pt-2">
        <ng-toggle formControlName="runner"></ng-toggle>
        <span class="toggle-label pl-2">
          Does the vehicle run
        </span>
      </div>

    </div>
  </div>

  <div class="widget px-3 mt-3 pb-3" id="create-advert-description">
    <div class="header padding-top mb-3">
      Description
    </div>

    <textarea id="description" class="striped-input md-textarea form-control" formControlName="description"
              [class.disabled]="advert && advert.bidCount > 0" name="desc"
              placeholder="Enter vehicle description.."></textarea>
  </div>

  <div class="row mt-3">

    <div class="col-12">


      <div class="widget px-3">

        <div class="header padding-top">
          Documents
        </div>

        <!-- hidden version of the file uploader -->
        <input type="file" class="file-input" onclick="value = null" multiple
               accept="image/x-png,image/gif,image/jpeg,application/pdf"
               (change)="uploadFiles($event)" #fileUpload>

        <div class="mt-1 mb-1 pb-2">
          <div class="">

            <div class="">

              <div class="pr-3 pt-2 pb-1">

                <div>

                  <ng-toggle formControlName="logBook"></ng-toggle>

                  <span class="toggle-label pl-2">
                    Do you have the V5/Log book
                  </span>

                </div>

                <div class="upload-button mt-2" *ngIf="formCtrls['logBook'].value || logbookPresent">
                  <button [disabled]="uploadingFiles" class="btn btn-xs btn-secondary"
                          (click)="uploadLogbook(); fileUpload.click();">
                      <span
                        *ngIf="uploadMode == uploadModeEnum.V5 && uploadingFiles; then loadingV5; else notLoadingV5"></span>
                    <ng-template #loadingV5><i class="fa fa-spinner fa-spin"></i></ng-template>
                    <ng-template #notLoadingV5><i class="fas fa-upload"></i></ng-template>
                    Upload Photo
                  </button>
                </div>

                <div *ngIf="(formCtrls['logBook'].value || logbookPresent) && vehicle.v5Media ">
                  <app-media-thumbnail-viewer [medias]="vehicle.v5Media"
                                              [imageHeight]="thumbnailHeight"></app-media-thumbnail-viewer>
                </div>
              </div>
            </div>

            <div class="">
              <div class="pr-3 pt-2 pb-1">

                <div>
                  <ng-toggle formControlName="serviceHistory"></ng-toggle>

                  <div class="toggle-label pl-l">
                    Do you have the service book
                  </div>
                </div>

                <div class="upload-button mt-2" *ngIf="formCtrls['serviceHistory'].value || serviceBookPresent">
                  <button [disabled]="uploadingFiles" class="btn btn-xs btn-secondary"
                          (click)="uploadServiceBook(); fileUpload.click();">
                      <span
                        *ngIf="uploadMode == uploadModeEnum.ServiceBook && uploadingFiles; then loadingServiceBook; else notLoadingServiceBook"></span>
                    <ng-template #loadingServiceBook><i class="fa fa-spinner fa-spin"></i></ng-template>
                    <ng-template #notLoadingServiceBook><i class="fas fa-upload"></i></ng-template>
                    Upload Photo
                  </button>
                </div>

                <div *ngIf="formCtrls['serviceHistory'].value || serviceBookPresent" class="mt-1">
                  <app-media-thumbnail-viewer [medias]="vehicle.serviceBookMedia"
                                              [imageHeight]="thumbnailHeight"></app-media-thumbnail-viewer>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-12">

      <div class="widget widget-border px-3 mt-3 mb-3 pb-2">
        <div class="header padding-top">
          Service History
        </div>
        <div class="row">
          <div class="col-12 pt-2">
            <app-vehicle-service-history [vehicle]="vehicle"></app-vehicle-service-history>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-12">

      <div class="pt-2 pb-2">
        <app-vehicle-tyres [vehicle]="vehicle" [readOnly]="false"
                           (dataChanged)="tyreDepthDataChanged()"></app-vehicle-tyres>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-12">

      <div class="pt-2 pb-2">
        <app-vehicle-options [vehicle]="vehicle"></app-vehicle-options>
      </div>

    </div>
  </div>


</form>


<div *ngIf="savingVehicle">
  <p>Saving vehicle data... <i class="la la-spinner la-spin"></i></p>
</div>

<app-fullsize-image-viewer></app-fullsize-image-viewer>
