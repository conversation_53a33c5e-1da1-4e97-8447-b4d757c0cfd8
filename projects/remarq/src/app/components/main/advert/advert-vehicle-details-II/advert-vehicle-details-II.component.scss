.file-input {
  display: none;
}

.upload-button {
  display: inline-block;
}

.switch label { font-weight: 500; color: var(--floatLabelColour); font-size: 0.875rem; }

.file-upload {
  text-align: center;
}

#description {
  height: 146px;
}

.striped-input {

  padding-top: 4px;

  background-image: -webkit-linear-gradient(0deg, transparent 5em, rgba(255, 0, 0, .1) 0, transparent 5.1em),
  -webkit-linear-gradient(rgba(0, 0, 255, .1) 1px, transparent 0);
  background-image: -moz-linear-gradient(0deg, transparent 5em, rgba(255, 0, 0, .1) 0, transparent 5.1em),
  -moz-linear-gradient(rgba(0, 0, 255, .1) 1px, transparent 0);
  background-image: -o-linear-gradient(0deg, transparent 5em, rgba(255, 0, 0, .1) 0, transparent 5.1em),
  -o-linear-gradient(rgba(0, 0, 255, .1) 1px, transparent 0);
  background-image: -ms-linear-gradient(0deg, transparent 5em, rgba(255, 0, 0, .1) 0, transparent 5.1em),
  -ms-linear-gradient(rgba(0, 0, 255, .1) 1px, transparent 0);
  -webkit-background-size: 100% 1.5em;
  -moz-background-size: 100% 1.5em;

  /* In a perfect world... */
  background-image: linear-gradient(0deg, transparent 5em, rgba(255, 0, 0, .1) 0, transparent 5.1em),
  linear-gradient(rgba(0, 0, 255, .1) 1px, transparent 0);
  background-size: 100% 1.5em;
}
