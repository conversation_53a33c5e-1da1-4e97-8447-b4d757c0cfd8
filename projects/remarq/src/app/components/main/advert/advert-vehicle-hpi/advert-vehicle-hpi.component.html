<span *ngIf="provenance">
  <span *ngIf="provenance.stolen
      || provenance.scrapped || provenance.vcaR_Damage">
    <ng-template #tooltipt>
      <div [innerHTML]="tooltip"></div>
    </ng-template>
    <i [mdbTooltip]="tooltipt" class="fa fa-12x fa-exclamation-triangle text-danger"></i>
  </span>
    <span *ngIf="provenance.finance">
    <ng-template #tooltiptf>
      <div [innerHTML]="tooltip"></div>
    </ng-template>
    <i [mdbTooltip]="tooltiptf" class="fa fa-12x fa-exclamation-triangle" style="color: #bbb"></i>
  </span>
</span>
