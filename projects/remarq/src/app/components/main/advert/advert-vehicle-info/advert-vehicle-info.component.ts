import {Component, Input, OnInit, ViewChild} from '@angular/core';
import {AdvertService, VehicleService} from '../../../../services';
import {AdvertDTO, AdvertSearchResultDTO, AdvertViewVehicleInfoDTO} from "../../../../global/interfaces";
import {} from "../../../../global/services";
import {ServiceHistoryTypeEnum} from "../../../../global/enums";
import {ModalDirective} from "ng-uikit-pro-standard";

export enum ViewDetailTypeEnum {
  None,
  MOT,
  Service
}

@Component({
  selector: 'app-advert-vehicle-info',
  templateUrl: './advert-vehicle-info.component.html',
  styleUrls: ['./advert-vehicle-info.component.scss']
})
export class AdvertVehicleInfoComponent implements OnInit {

  constructor(private advertService: AdvertService) {
  }

  public serviceHistoryTypeEnum = ServiceHistoryTypeEnum;
  public viewDetailType = ViewDetailTypeEnum;

  private _advertId: string;
  @Input('advertId') set advertId(value: string) {
    this._advertId = value;
    if (value) {
      this.loadAdvertInfo();
    }
  }
  get advertId(): string {
    return this._advertId;
  }

  @ViewChild("detailsModal") detailsModal: ModalDirective;

  info: AdvertViewVehicleInfoDTO;
  detailType: ViewDetailTypeEnum;

  motWarnDate: Date;
  todayDate: Date;

  async ngOnInit() {

    this.updateDates();
  }

  updateDates() {
    this.todayDate = new Date();
    this.motWarnDate = new Date(this.todayDate);
    this.motWarnDate.setDate(this.motWarnDate.getDate() + 7);
  }

  isMotExpiringSoon(expirationDate: string | Date | null): boolean {
    if (!expirationDate) return false;
    const expDate = new Date(expirationDate);
    return expDate > this.todayDate && expDate <= this.motWarnDate;
  }

  isMotExpired(expirationDate: string | Date | null): boolean {
    if (!expirationDate) return false;
    const expDate = new Date(expirationDate);
    return expDate <= this.todayDate;
  }

  async loadAdvertInfo() {
    return this.advertService.advertViewVehicleInfo({
      component: "AdvertView_VehicleInfo",
      filters: {
        id: this.advertId
      }
    }, true).then((result: AdvertSearchResultDTO) => {
      this.info = result.advertViewVehicleInfo;
    });
  }

  viewMOTDetails() {
    this.detailType = ViewDetailTypeEnum.MOT;
    this.detailsModal.show();
  }

  viewServiceDetails() {
    this.detailType = ViewDetailTypeEnum.Service;
    this.detailsModal.show();
  }
}
