.upload-container {
}

.d-grid {
  display: grid;
  grid-gap: 10px;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
}

.image-container {

}

.image-block {
  background-size: var(--vehicleMediaCoverOrContain);
  border-radius: 3px;
  background-position: center center;
  background-repeat: no-repeat;
}

.image-item {
  width: 100%;
  aspect-ratio: var(--inverseAspectRatio);
}

.vehicle-image, .add-image {

  background-color: #f4f4f4;
  text-align: center;
  border-radius: 5px;
  position: relative;
  width: 100%;
  height: 100%;

  .image-block {
    display: inline-block;
    top: 0;
    left: 0;
    z-index: 12;
    width: 100%;
    height: 100%;
    outline: 1px solid var(--softInputBorderColour);
  }

  .image-menu {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    padding-top: 27%;
    height: 105px;
    display: inline-block;
    background-color: rgba(0, 0, 0, 0.6);
    color: #ddd;
    z-index: 999;
    border-radius: 3px;

    i:hover {

      cursor: pointer;

    }
  }

  .menu-menu {
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    display: inline-block;
    background-color: rgba(#f4f4f4, 0.8);
    color: #888;
    z-index: 999;
    cursor: pointer;
    border-top-right-radius: 3px;
    border-bottom-left-radius: 3px;
  }

  .image-enhanced {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100%;
    display: inline-block;
    background-color: rgba(50, 50, 50, 1);
    color: #eee;
    z-index: 999;
    font-size: 0.7em;
    font-weight: 500;
    line-height: 12px;
  }
}

.crop-btn {
  margin-left: 7px;
}
