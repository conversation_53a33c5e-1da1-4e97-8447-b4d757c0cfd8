<div [class.ghost]="fetchingGallery" style="height: 100%;" [class.loadingAspect]="fetchingGallery">
  <div class="ghost-waiting" style="height: 100%;">
    <div *ngIf="newGalleryImages?.length > 0" class="ghost" style="height: 100%;" >
      <app-gallery
        [thumbnailContainerStyle]="{ padding: '3px', 'background-color': '#f7f7f7', 'border-radius': '4px' }"
        [thumbnailStyle]="{ margin: '3px'}"
        [galleryImages]=" newGalleryImages"
        [showThumbnails]="true"
        [showMainImage]="true"></app-gallery>
    </div>
  </div>
</div>
