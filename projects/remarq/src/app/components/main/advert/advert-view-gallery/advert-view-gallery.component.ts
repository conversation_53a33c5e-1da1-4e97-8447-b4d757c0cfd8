import {Component, Input} from "@angular/core";
import {VehicleService} from "../../../../services";
import {AdvertViewVehicleMediaDTO} from "projects/common/interfaces";

@Component({
  selector: 'app-advert-view-gallery',
  templateUrl: './advert-view-gallery.component.html',
  styleUrls: ['advert-view-gallery.component.scss']
})
export class AdvertViewGalleryComponent {

  fetchingGallery: boolean;

  constructor(private vehicleService: VehicleService) {
  }

  // tslint:disable-next-line:variable-name
  private _advertId: string;
  @Input('advertId') set advertId(value: string) {
    this._advertId = value;
    if (value) {
      this.loadInfo();
    }
  }

  get advertId(): string {
    return this._advertId;
  }

  newGalleryImages: any = [];

  loadInfo() {
    this.fetchingGallery = true;
    this.vehicleService.getVehicleMediaByAdvert(this.advertId, false).then(res => {
      this.fetchingGallery = false;
      this.refreshGallery(res);
    });
  }


  refreshGallery(media: AdvertViewVehicleMediaDTO[]) {

    const images = this.sortImages(media);

    this.newGalleryImages = [];

    images.forEach(x => {
      this.newGalleryImages.push({
        small: x.mediaURL + "?tr=h-150",
        medium: x.mediaURL + "?tr=h-600",
        big: x.mediaURL + "?tr=h-1400",
      });
    });
  }

  sortImages(imgArray: AdvertViewVehicleMediaDTO[]) {
    return imgArray.sort((a, b) => a.sequence > b.sequence ? 1 : a.sequence === b.sequence ? 0 : -1);
  }
}
