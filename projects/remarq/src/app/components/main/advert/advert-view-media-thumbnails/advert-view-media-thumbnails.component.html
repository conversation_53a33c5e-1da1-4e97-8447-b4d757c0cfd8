<!--
@Input() galleryImages: any[] = [];
@Input() showThumbnails = true;
@Input() showMainImage = false;
@Input() disableFullscreen = false;
@Input() mainSlidesToShow = 1;
@Input() thumbnailStyle: { [p: string]: any } | null | undefined;
@Input() mainImageStyle: { [p: string]: any } | null | undefined;
@Input() thumbnailContainerStyle: { [p: string]: any } | null | undefined;
-->

<div style="height: 60px; width: 100%; cursor:pointer; overflow-y: hidden;" *ngIf="galleryImages.length > 0">
  <ngx-slick-carousel
    [config]="{ slidesToShow: 2, autoplay: true, autoplaySpeed: 2000, arrows: false }"
    #slickModal="slick-carousel" id="main-carousel">
    @for (x of galleryImages; track x.id) {
      <div ngxSlickItem class="slide" style="height: 60px;">
        <img (click)="showImageDialog(x)" src="{{ x.small }})" style="height: 100%; width: 100%; object-fit: cover; border-radius: 2px; ">
      </div>
    }
  </ngx-slick-carousel>
</div>


<div mdbModal
     #zoomModalDialog="mdbModal" class="modal fade" tabindex="-1"
     [config]="{backdrop: false, ignoreBackdropClick: false}"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title">Service History</div>
        <button type="button" class="close" aria-label="Close" (click)="zoomModalDialog.hide()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <img src="{{ modalImage }}" style="width: 100%; height: 100%; object-fit: contain">
      </div>
    </div>
  </div>
</div>
