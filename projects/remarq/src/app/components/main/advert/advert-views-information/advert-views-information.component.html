<div mdbModal #viewsModal="mdbModal" class="modal fade"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true" [config]="{backdrop: 'static'}">
  <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">

        <button type="button" class="close pull-right" aria-label="Close" (click)="hideModal()"><span
          aria-hidden="true">×</span></button>

        <div>Advert Views Information</div>
      </div>
      <div class="modal-body" *ngIf="adviewInfo">
        <div class="row" style="margin-bottom: 8px">
          <div class="col-4">
            <h2>Total Views: {{ adviewInfo.totalViews }}</h2>
          </div>
          <div class="col-8" style="text-align: right"><h2>Seller: {{ adviewInfo.sellerEmail }}</h2></div>
        </div>

<!--        <div class="filter">-->
<!--          <div class="md-form">-->
<!--            <input class="w-100 form-control float-label" id="filter" [formControl]="searchControl" mdbInput>-->
<!--            <label for="filter">Company Search</label>-->
<!--          </div>-->
<!--        </div>-->

        <table class="table table-striped table-narrow table-hover">
          <thead>
          <tr>
            <th>Contact</th>
            <th>View Count</th>
            <th>Last View Date</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let data of adviewInfo.customerDetails">
            <td>
              <strong>{{ data.name }}</strong>
              <div>{{ data.email }}</div>
              <div>{{ data.phone }}</div>
            </td>
            <td>{{ data.numViews | number }}</td>
            <td>{{ data.latestViewDate | date: 'dd/MM/yyyy HH:mm' }}</td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
