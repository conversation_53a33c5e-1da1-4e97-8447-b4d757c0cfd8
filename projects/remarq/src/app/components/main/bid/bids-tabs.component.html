<div class="container">
  <div class="mt-2">
    <div class="d-flex flex-wrap" style="grid-gap: 20px;">
      <div>
        <h1 class="page-header">Your bids on live adverts</h1>
      </div>
      <div>
        <h2 class="page-header" style="line-height: 38px;">
          <div class="btn btn-xs btn-outline-primary" (click)="urlService.myBids('1', '0')">View Bid History</div>
        </h2>
      </div>
    </div>
  </div>
</div>
<div class="container">
  <mdb-tabset #tabs [buttonClass]="'classic-tabs tabs-custom'"
              (shownBsTab)="goToTab($event)"
              class="classic-tabs">
    <mdb-tab heading="Active Bids" [id]="'activeBids'">
      <app-bids-view [bidViewType]="BidViewType.Active" (bidSummarySelected)="advertSelected($event)"></app-bids-view>
    </mdb-tab>
    <mdb-tab heading="Lost Bids" [id]="'lostBids'">
      <app-bids-view [bidViewType]="BidViewType.Lost" (bidSummarySelected)="advertSelected($event)"></app-bids-view>
    </mdb-tab>
  </mdb-tabset>
</div>

<div class="container" *ngIf="selectedAdvertId">
  <app-bids-received [advertId]="selectedAdvertId"></app-bids-received>
</div>




