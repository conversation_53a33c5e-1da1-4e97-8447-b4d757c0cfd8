import {Component, Input, OnInit, Output, EventEmitter} from '@angular/core';
import {BidService, URLService} from '../../../../services';
import {AdvertSummaryDTO, BidSummaryDTO, User} from "../../../../global/interfaces";
import {ContactService, ImageService, LoggerService, UserService} from "../../../../global/services";
import {BidStatusEnum, BidStatusName, BidViewTypeEnum} from "../../../../global/enums";

@Component({
  selector: 'app-bids-view',
  templateUrl: './bids-view.component.html',
  styleUrls: ['./bids-view.component.scss']
})
export class BidsViewComponent implements OnInit {

  loadingBids: boolean;

  constructor(
    private bidService: BidService,
    private userService: UserService,
    private contactService: ContactService,
    public urlService: URLService,
    public imageService: ImageService,
    private redirect: URLService,
    private logService: LoggerService) {
  }

  public bidViewTypeEnum = BidViewTypeEnum;
  public bidStatusNames = BidStatusName;
  public bidStatusEnum = BidStatusEnum;

  @Output() bidSummarySelected: EventEmitter<string> = new EventEmitter<string>(); // emit selected advertId
  @Input('bidViewType') bidViewType: BidViewTypeEnum;
  bids: BidSummaryDTO[];

  public user: User;

  logger = this.logService.taggedLogger(this.constructor?.name);

  ngOnInit(): void {

    this.userService.loadCurrentUser().then(() => {

      this.user = this.userService.CurrentUser;

      this.loadingBids = true;

      switch (this.bidViewType) {
        case BidViewTypeEnum.Active:

          this.contactService
            .getActiveBids(this.user.contactId)
            .then((result) => {
              this.bids = result;
              this.loadingBids = false;
            });

          break;
        case BidViewTypeEnum.Lost:
          this.contactService
            .getLostBids(this.user.contactId)
            .then((result) => {
              this.bids = result;
              this.loadingBids = false;
              this.logger.info("LOST BIDS: ", this.bids);
            });
          break;
      }
    });
  }

  async viewAdvert(advertId: string) {
    this.redirect.advertView(advertId);
  }

  advertSelected(advertId: string) {
    this.redirect.advertView(advertId);
//    this.bidSummarySelected.emit(advertId);
  }

  reserveMet(advert: AdvertSummaryDTO) {

    if (advert?.reserveMet) {
      return "fa fa-check fa-15x text-success reserve-met";
    }

    return "fa fa-minus reserve-not-met";
  }
}
