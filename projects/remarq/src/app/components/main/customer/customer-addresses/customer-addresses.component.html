<div class="page-top">
  <div class="d-flex flex-wrap" style="align-items: center">
    <div class="flex-grow-1">
      <h1 class="page-header pl-1">Addresses</h1>
    </div>
    <div class="flex-shrink-1 text-right">
      <span (click)="showCreateAddress()" class="btn btn-sm btn-secondary">
        Add Address</span>
    </div>
  </div>
</div>

<div class="widget mt-2" *ngIf="addresses?.length > 0; else noAddress">

  <div>

    <table mdbTable class="table table-striped table-condensed">
      <thead>
      <tr>
        <th scope="col">Address Name</th>
        <th scope="col" *ngIf="!isMobile">Address</th>
        <th scope="col" *ngIf="!isMobile">Postcode</th>
        <th scope="col">Default Address</th>
        <th scope="col"></th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let address of addresses, let i=index" id="addressrow-{{ address.id}}">
        <td (click)="showEditAddress(address)">{{ address.addressName }}</td>
        <td *ngIf="!isMobile"
            (click)="showEditAddress(address)">{{ address.addressLine1 }} {{ address.addressLine2 }} {{ address.addressCounty }}
        </td>
        <td *ngIf="!isMobile" (click)="showEditAddress(address)">{{ address.addressPostcode }}</td>
        <td>


          <div>
            <ng-toggle
              [(ngModel)]="address.isPrimary"
              (change)="makePrimaryAddress(address.id)"
              [color]="{checked: '#939da2', unchecked: '#ddd', disabled: '#09B677'}"
              [switchColor]="{ checked: '#fff', unchecked: '#eee', disabled: '#fff' }"
              [disabled]="address.isPrimary"></ng-toggle>
          </div>

          <!--

          <div class="custom-control custom-switch">
            <input [disabled]="address.isPrimary" [(ngModel)]="address.isPrimary" type="checkbox"
                   class="custom-control-input" id="customSwitch-{{ address.id }}" checked
                   (change)="makePrimaryAddress(address.id)">
            <label class="custom-control-label" for="customSwitch-{{ address.id }}"></label>
          </div>

          -->


        </td>
        <td class="shrink-cell">
          <i class="fa-15x fa fa-times-circle" (click)="deleteAddress(i, address) "></i>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</div>

<app-delete-confirm-modal
  [showDialog]="showDeleteConfirm"
  (confirmDelete)="confirmDelete()"
  (modalClosed)="showDeleteConfirm = false"
  (cancelDelete)="cancelDelete()"></app-delete-confirm-modal>

<div mdbModal #cannotDeleteModal="mdbModal" class="cannotDelete modal fade" tabindex="-1"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md" role="document">
    <div class="modal-content">
      <div class="modal-header">
        Error
      </div>

      <div class="modal-body">
        You cannot delete the primary address.
      </div>

      <div class="mt-2 mr-2 text-right mb-2">
        <button type="button" class="mr-2 btn btn-primary" (click)="this.cannotDeleteModal.hide();">OK</button>
      </div>
    </div>
  </div>
</div>


<div mdbModal #addressModal="mdbModal" class="modal fade" tabindex="-1"
     data-backdrop="false"
     [config]="{backdrop: 'static'}"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true">

  <div class="modal-dialog modal-md" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <span *ngIf="newAddress">Add Address</span>
        <span *ngIf="!newAddress">Edit Address</span>
      </div>

      <form [formGroup]="addressForm" *ngIf="addressForm" (ngSubmit)="submitForm()">
        <div class="modal-body">

          <div class="pb-3" style="font-size: 13px; font-weight: 500;">Give this address a name to remember it by eg.
            "Head office"
          </div>
          <div class="md-form pb-1">
            <input mdbInput class="form-control float-label mb-0"
                   autocomplete="organization"
                   id="addressName" formControlName="addressName">
            <label for="addressName">Address Name</label>
            <div *ngIf="addressName.invalid && (addressName.dirty || addressName.touched)">
              <small class="text-danger" *ngIf="addressName.errors.required">&nbsp;Address Name Required.</small>
            </div>
          </div>

          <hr>

          <div class="md-form">
            <input mdbInput class="form-control float-label mb-0" id="addressLine1"
                   autocomplete="address-line1"
                   formControlName="addressLine1">
            <label for="addressLine1">Address Line 1</label>
            <div *ngIf="addressLine1.invalid && (addressLine1.dirty || addressLine1.touched)">
              <small class="text-danger" *ngIf="addressLine1.errors.required">&nbsp;Address Line 1 Required.</small>
            </div>
          </div>

          <div class="md-form">
            <input mdbInput class="form-control float-label"
                   autocomplete="address-line2"
                   id="addressLine2" formControlName="addressLine2">
            <label for="addressLine2">Address Line 2</label>
          </div>

          <div class="md-form">
            <input mdbInput
                   autocomplete="address-level2"
                   class="form-control float-label" id="addressLine3" formControlName="addressLine3">
            <label for="addressLine3">Address Line 3</label>
          </div>

          <div class="row">

            <div class="col-md-6">
              <div class="md-form">
                <input mdbInput class="form-control float-label" id="addressCounty"
                       autocomplete="address-level1"
                       formControlName="addressCounty">
                <label for="addressCounty">County</label>
              </div>
            </div>

            <div class="col-md-6">
              <div class="md-form">
                <input mdbInput class="form-control float-label mb-0" id="addressPostcode"
                       formControlName="addressPostcode">
                <label for="addressPostcode">Postcode</label>

                <div *ngIf="postcode.invalid && (postcode.dirty || postcode.touched)">
                  <small class="text-danger" *ngIf="postcode.errors.invalidPostcode">&nbsp;Invalid postcode
                    format.</small>
                </div>
              </div>
            </div>

            <div class="col-md-12 select">
              <mdb-select-2 [outline]="true"
                            formControlName="countryId"
                            label="Country"
                            placeholder="Country">
                <mdb-select-option *ngFor="let country of countrySelect" [value]="country.value">{{ country.label }}
                </mdb-select-option>
              </mdb-select-2>
            </div>

          </div>

        </div>
        <div class="modal-footer">
          <div class="text-right">
            <button type="button" class="mr-2 btn btn-tertiary-outline" (click)="this.addressModal.hide();">Cancel
            </button>
            <button class="btn btn-tertiary" type="submit">Save</button>
          </div>

        </div>
      </form>
    </div>
  </div>
</div>

<ng-template #noAddress>

  <div *ngIf="isLoading">

    <app-loading-spinner></app-loading-spinner>

  </div>

  <div class="text-center attention-box p-3" *ngIf="!isLoading">

    <div class="row">

      <div class="col-sm-3 col-md-2 col-xl-2 text-center d-flex align-items-center">
        <div class="text-center flex-grow-1" style="font-size: 80px; line-height: 80px;">
          <i class="fa fa-exclamation-circle"></i>
        </div>
      </div>
      <div class="col-sm-9 col-md-10">
        <div class="text-center text-sm-left">
          <h2>You haven't added an address</h2>
          <h3 class="mt-2">Before you can list an advert you will need to add an address to your account.</h3>
          <div (click)="showCreateAddress()" class="btn btn-xs btn-primary mt-2">
            Add Address
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-template>

