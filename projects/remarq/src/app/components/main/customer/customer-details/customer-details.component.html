<div class="page-top">
  <h1 class="page-header pl-1">Account Details</h1>
</div>

<!-- Display Existing Payment Method Details -->
<div *ngIf="paymentMethodDetails" class="payment-method-single-line">
  <span><strong>Current Payment Method:</strong></span>
  <span>Card Brand: <b>{{ paymentMethodDetails.brand | titlecase }}</b></span>
  <span>Last 4 Digits: <b>**** **** **** {{ paymentMethodDetails.last4 }}</b></span>
  <span>Expires: <b>{{ paymentMethodDetails.expMonth }}/{{ paymentMethodDetails.expYear }}</b></span>
</div>


<div class="widget widget-padding" *ngIf="detailsForm && customerDetails">

  <form [formGroup]="detailsForm" (ngSubmit)="onSubmit()">

    <div class="md-form">
      <p>
        <input mdbInput type="text" id="customerName" formControlName="customerName" class="float-label form-control">
        <label for="customerName" class="">Customer Name</label>
      </p>
      <div class="error-message" *ngIf="f.customerName.dirty && err?.noCustomerName">
        Please enter a customer name
      </div>
    </div>

    <div class="md-form">
      <p>
        <input mdbInput type="text" id="email" formControlName="email" name="email"
               class="float-label form-control">
        <label for="email">E-mail</label>
      </p>
      <div class="error-message" *ngIf="(f.email.dirty && (err?.noEmail || err?.invalidEmail))">
        Please enter a valid E-mail address
      </div>
    </div>

    <div class="md-form">
      <p>
        <input mdbInput type="text" id="phone1" formControlName="phone1" name="phone1"
               class="form-control float-label">
        <label for="phone1">Phone</label>
      </p>
    </div>

    <div class="md-form">
      <p>
        <input mdbInput type="text" id="websiteUrl" formControlName="websiteUrl" name="websiteUrl"
               class="form-control float-label">
        <label for="websiteUrl">Website URL</label>
      </p>
      <div class="error-message" *ngIf="(f.websiteUrl.dirty && err?.invalidURL)">
        Please enter a valid website URL
      </div>
    </div>

    <div class="md-form">
      <p>
        <input mdbInput type="text" id="vatNumber" formControlName="vatNumber" name="vatNumber"
               class="form-control float-label">
        <label for="vatNumber">VAT Number</label>
      </p>
      <div class="error-message" *ngIf="(f.vatNumber.dirty && err?.invalidVAT)">
        Please enter a valid GB VAT Number
      </div>
    </div>

    <div class="mt-4" style="display: flex; grid-gap: 20px;">
      <div style="flex-grow: 1"> &nbsp; </div>
      <div style="text-align: right; flex-grow: 1">
        <div *ngIf="!hasPaymentMethod">
          <button type="button" class="btn btn-secondary"
                  mdbTooltip="Setup a payment method for a seamless buying experience"
                  (click)="setPaymentMethod(true)">
            Payment Method
          </button>
        </div>
        <div *ngIf="hasPaymentMethod">
          <button type="button" class="btn btn-primary-outline"
                  (click)="setPaymentMethod(true)">Update Payment Method
          </button>
        </div>
      </div>
      <div>
        <button class="btn btn-tertiary" type="submit">Save</button>
      </div>
    </div>
  </form>
</div>

<!-- Could take a post-setup event to show.. but we're hacking it by passing in a date -->
<app-setup-payment [forceShowRequested]="forceShow" *ngIf="setupPaymentMethod"></app-setup-payment>
