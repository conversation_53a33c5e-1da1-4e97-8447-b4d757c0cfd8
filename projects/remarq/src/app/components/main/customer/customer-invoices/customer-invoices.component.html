<div class="widget widget-border">

  <div *ngIf="invoices?.length > 0">

    <table class="table table-striped table-compressed table-hover" style="width: 100%; overflow-x: hidden;">
      <thead>
      <tr>
        <th>Inv#</th>
        <th>Order</th>
        <th>Date</th>
        <th *ngIf="!isMobile">Fee</th>
        <th *ngIf="!isMobile">VAT</th>
        <th>Amount</th>
        <th>Status</th>
        <th>Actions</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let invoice of invoices">
        <td>{{ invoice.invoiceNumber }}</td>
        <td>{{ invoice.customerOrders.length }}</td>
        <td>{{ invoice.issuedDate | date: 'dd/MM/YY' }}</td>
        <td *ngIf="!isMobile">{{ invoice.totalNetAmt | customCurrency }}</td>
        <td *ngIf="!isMobile">{{ invoice.totalTaxAmt | customCurrency }}</td>
        <td>{{ invoice.totalGrossAmt | customCurrency }}</td>

        <td>
          <div [class]="invoice.paidDate ? 'paid-style' : 'due-style'">
            {{ invoice.paidDate ? "Paid" : "Due" }}
          </div>
        </td>
        <td class="shrink-cell">
          <div>
            <a class="btn btn-xs btn-primary-outline btn-block" style="margin-bottom: 7px;"
               *ngIf="invoice.invoiceReference" href="{{ invoice.invoiceURL }}" target="_blank"
               rel="noopener noreferrer">
              <i class="fa fa-file-invoice"></i> View Invoice
            </a>
          </div>
          <div>
            <button *ngIf="!invoice.paidDate" class="btn btn-xs btn-primary btn-block" (click)="payInvoice(invoice)">
              <i class="fa fa-receipt"></i> Pay
            </button>
          </div>
        </td>
      </tr>
      </tbody>
    </table>
  </div>

  <div *ngIf="!invoices || invoices.length == 0" class="text-center pt-5 pb-3">

    <h1>You have no Invoices</h1>
    <p>Invoices will appear here as you buy/sell vehicles</p>

  </div>
</div>


<!--<div mdbModal #payInvoiceModal="mdbModal" class="modal fade" tabindex="-1"-->
<!--     role="dialog" aria-labelledby="Pay Invoice" aria-hidden="true" [config]="{ignoreBackdropClick: true}">-->

<!--  <div class="modal-dialog-centered modal-md modal-dialog" role="document">-->

<!--    <div class="modal-content mb-5">-->
<!--      <div class="modal-header">-->
<!--        Make Payment-->
<!--      </div>-->

<!--      <div class="modal-body" style="background-color: #eee;">-->

<!--        <div *ngIf="!showPaymentForm">-->
<!--          <div class="text-center" *ngIf="paymentSuccess">-->
<!--            <i class="fa fa-2x fa-check-circle text-success"></i> <span class="payment-success">Payment Success</span>-->
<!--            <div class="mt-3">-->
<!--              <span class="btn btn-primary-outline" (click)="closePayment()">Done</span>-->
<!--            </div>-->
<!--          </div>-->
<!--          <div class="text-center" *ngIf="cardDeclined">-->
<!--            <i class="fa fa-2x fa-times-circle"></i> <span class="payment-success">Payment Failed</span>-->

<!--            <div *ngIf="paymentFailReason == 'card_declined'">Card has been declined</div>-->

<!--            <div class="mt-3">-->
<!--              <span class="btn btn-primary-outline" (click)="makePayment(payingInvoiceId)">Try again</span>-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->

<!--        <form (ngSubmit)="pay()" #form="ngForm" *ngIf="showPaymentForm">-->

<!--          <img src="assets/images/3rdparty/stripe-secure-payment2.svg"-->
<!--               style="background-color: #cde; border-radius: 7px; padding: 10px;"-->
<!--               alt="Strip Logo" class="w-100 mb-2">-->

<!--          <div style="margin-bottom: 10px;" *ngIf="paymentIntent">-->

<!--            <div style="margin-bottom: 12px;" class="d-flex flex-wrap">-->
<!--              <div class="flex-shrink-1 text-left">Invoice: <span-->
<!--                style="font-weight: 600">{{ paymentIntent.metadata.invoiceNumber }}</span></div>-->
<!--              <div class="flex-grow-1 text-right">Amount: <span-->
<!--                style="font-weight: 600">{{ paymentIntent.amount / 100.0 | customCurrency }} </span></div>-->
<!--            </div>-->

<!--            <ngx-stripe-payment-->
<!--              [clientSecret]="elementsOptions?.clientSecret"-->
<!--              [options]="paymentOptions"-->
<!--              [elementsOptions]="elementsOptions">-->
<!--              <span style="color: green" *ngxStripeLoadingTemplate>Loading Stripe... </span>-->
<!--            </ngx-stripe-payment>-->

<!--            <div *ngIf="paymentFailReason == 'expired_card'" class="text-danger mt-2">ERROR: Card has expired</div>-->
<!--            <div *ngIf="paymentFailReason == 'incorrect_cvc'" class="text-danger mt-2">ERROR: Incorrect CVC</div>-->
<!--            <div *ngIf="paymentFailReason == 'incorrect_number'" class="text-danger mt-2">ERROR: Incorrect Card Number-->
<!--            </div>-->
<!--          </div>-->

<!--          <div *ngIf="!paymentIntent" class="text-center pt-2 pb-2">-->
<!--            <i class="fa fa-spin fa-spinner"></i> Loading..-->
<!--          </div>-->

<!--          <div style="margin-top: 15px; text-align: right;">-->
<!--            <button class="btn btn-primary-outline" style="margin-right: 10px;" (click)="closePayment()">Cancel</button>-->
<!--            <button class="btn btn-primary" type="submit" [disabled]="!form.valid || processing">-->
<!--                <span *ngIf="processing">-->
<!--                  <i class="fa fa-spin fa-spinner"></i> Processing-->
<!--                </span>-->
<!--              <span *ngIf="!processing">-->
<!--                  Pay <span *ngIf="paymentIntent"> {{ paymentIntent?.amount / 100.0 | customCurrency }} </span>-->
<!--                </span>-->
<!--            </button>-->
<!--          </div>-->
<!--        </form>-->
<!--      </div>-->
<!--    </div>-->
<!--  </div>-->
<!--</div>-->
