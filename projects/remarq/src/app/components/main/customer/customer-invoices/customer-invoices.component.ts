import {Component, HostListener, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {InvoiceService} from '../../../../services';
import {EventService} from '../../../../services';
import {Subscription} from "rxjs";
import {BillDTO} from "../../../../global/interfaces";
import {UserService} from "../../../../global/services";
import {} from "../../../../global/enums";
import {GlobalConstants} from "../../../../global/shared";

@Component({
  selector: 'app-customer-invoices',
  templateUrl: './customer-invoices.component.html',
  styleUrls: ['./customer-invoices.component.scss'],
})
export class CustomerInvoicesComponent implements OnInit, OnDestroy {

  constructor(private invoiceService: InvoiceService
            , private userService: UserService
            , private eventService: EventService,
          ) {
    this.customerId = userService.CurrentUser.customerId;
  }

  // logger = this.logService.taggedLogger(this.constructor?.name);

  isMobile = GlobalConstants.IsMobile;
  customerId: string;
  invoices: BillDTO[];
  invoiceServiceSub: Subscription;

  ngOnInit(): void {
    this.invoiceService.getCustomerInvoices(this.customerId).then(result => {
      this.invoices = result;
    });

    this.invoiceServiceSub = this.eventService.InvoicePaid.subscribe(data => {
      let invoice = this.invoices.find(x => x.invoiceReference == data.reference);
      invoice.paidDate = data.paidDate;

      // this.logger.info("Invoice Paid: ", invoice);
    });
  }

  @HostListener('window:beforeunload')
  ngOnDestroy() {

    if (this.invoiceServiceSub) {
      this.invoiceServiceSub.unsubscribe();
    }
  }

  payInvoice(bill: BillDTO) {
    // raise event to show payment modal
    this.eventService.PayInvoice.emit(bill);
  }

}
