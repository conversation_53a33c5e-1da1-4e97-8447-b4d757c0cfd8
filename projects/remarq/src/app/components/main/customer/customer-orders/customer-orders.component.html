<div class="widget widget-border">

  <div *ngIf="orders.length > 0">

    <!--
    <div class="d-flex flex-wrap p-2">
      <div class="flex-shrink pr-2">
        <input class="form-control" name="keyword" placeholder="Search">
      </div>
      <div class="flex-shrink">
        <input class="form-control" name="dateRange" placeholder="Date Range">
      </div>
    </div>
    -->

    <table class="table table-striped table-condensed table-hover invoice-table" mdbTable>
      <thead>
      <tr>
        <th>&nbsp;</th>
        <th>Description</th>
        <th *ngIf="!isMobile">Date</th>
        <th *ngIf="!isMobile">Fee</th>
        <th>Deliver</th>
        <th>&nbsp;</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let order of orders">
        <td class="shrink-cell order-img" (click)="urlService.advertView(order.deal.advertId)">
          <div class="img"
               style="line-height: 1rem; background-image: url({{ imageService.sizedImageUrl(order.imageURL,null, 50) }});"></div>
          <div><span class="txType txType{{ order.orderType }}">{{ order.orderTypeDesc }}</span></div>
        </td>
        <td (click)="urlService.advertView(order.deal.advertId)">
          <div class="table-line-1">{{ order.description }}</div>
          <div class="table-line-2">
            {{ order?.deal?.advert?.vehicle?.make?.makeName }}
            {{ order?.deal?.advert?.vehicle?.model?.modelName }}
            {{ order?.deal?.advert?.vehicle?.deriv?.derivName }}
          </div>
        </td>
        <td (click)="urlService.advertView(order.deal.advertId)"
            *ngIf="!isMobile"
            style="white-space: nowrap">{{ order.issuedDate | date: 'dd MMM YYYY' }}</td>

        <td *ngIf="!isMobile" (click)="urlService.advertView(order.deal.advertId)">
          {{ order.totalGrossAmt | customCurrency }}</td>
        <td style="white-space: nowrap">
          <button class="btn btn-xs btn-secondary">Deliver</button>
        </td>
        <td class="shrink-cell" style="text-align: center; white-space: nowrap">
          <div>
            <a class="btn btn-xs btn-primary-outline" style="margin-bottom: 7px;"
               *ngIf="order.invoiceReference" href="{{ order.invoiceURL }}" target="_blank"
               rel="noopener noreferrer">
              <i class="fa fa-file-invoice"></i> View
            </a>
          </div>
          <div>
            <div>
              <div *ngIf="order.paidDate">
<!--                <button [disabled]="busy[order.id]" *ngIf="!order.invoiceReference"-->
<!--                        class="btn btn-xs btn-primary-outline"-->
<!--                        style="" (click)="generateInvoice(order)">-->
<!--                  <span *ngIf="busy[order.id]"><i class="la la-spinner la-spin"></i></span>-->
<!--                  <span *ngIf="!busy[order.id]"><i class="fa fa-file-alt"></i> Invoice</span>-->
<!--                </button>-->
              </div>
              <div *ngIf="!order.paidDate">
<!--                <button class="btn btn-xs btn-primary" (click)="generateInvoice(order)">-->
<!--                  <i class="fa fa-receipt"></i> Generate Invoice-->
<!--                </button>-->
              </div>
            </div>
          </div>
        </td>
      </tr>
      </tbody>
    </table>
  </div>

  <div *ngIf="!orders || orders.length == 0" class="text-center pt-5 pb-3">

    <h1>You have no orders</h1>
    <p>Orders will appear here as you buy/sell vehicles</p>

  </div>
</div>

