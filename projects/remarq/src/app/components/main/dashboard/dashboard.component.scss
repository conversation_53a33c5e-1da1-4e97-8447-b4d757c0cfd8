.header-box {
  background-color: var(--attentionBoxBackgroundColour) !important;
  box-shadow: 2px 2px 5px 0px rgba(50, 50, 50, 0.05);
  border-radius: 5px;
  padding: 35px;
  cursor: pointer;
  min-height: 140px;
}

.attention-box {
  padding: 35px;
  min-height: 140px;
}

.attention-border {
  outline: 2px solid var(--errorColour);
  outline-offset: -2px;
}

.error-colour {
  color: var(--errorColour);
}

.widget-inner-box {

  max-height: 120px;
  overflow-y: auto;
  overflow-x: hidden;
}

.stats-wrapper {
  display: grid;
  grid-gap: 10px;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  padding-bottom: 10px;
}

.widget .header.padding-top {
  padding-top: 0.3rem !important
}

.stats-box {

  .top-line {

    min-height: 64px;

  }

  .stats-inner {

    padding: 5px 15px 15px 15px;
    height: 170px;
    color: var(--textColour);
    text-align: left;
    cursor: pointer;

    .fa, .far {

      font-size: 30px;

    }

    .number {
      font-size: 2.5rem;
      text-align: right;
      font-weight: 500;
      color: var(--linkColour);
    }

    .title {
      font-size: 18px;
      letter-spacing: -0.5px;
      font-weight: 600;
    }

    .subtitle {
      color: var(--subtitleColour);
      font-size: 14px;
      margin: 3px 0 7px 0;
      font-weight: 400;
    }
  }
}

.welcome-text {
  font-size: 32px;
  font-weight: 600;
  letter-spacing: -1px;
}

.sub-text {
  font-size: 1rem;
  font-weight: 400;
  color: rgb(50, 50, 50);
}

.advert-scroller-box {
  position: relative;
  width: 100%;
  aspect-ratio: var(--inverseAspectRatio);
  background-color: #fff;

  .scroller-image {
    object-fit: contain;
    height: 100%;
    width: 100%;
  }

  .headline {
    color: var(--linkColour);
    font-size: 0.75rem;
    font-weight: 600;
    white-space: nowrap;
    line-height: 1rem;
    overflow-x: hidden;
    overflow-y: hidden;
  }

  .summary-lines {
    white-space: nowrap;
    width: 100%;
    font-size: 0.7rem;
    font-weight: 400;
    color: #23313D;
    line-height: 0.75rem;
    overflow-x: hidden;
    overflow-y: hidden;
  }
}


.source {
  font-size: 0.75rem;
  color: #888;
  line-height: 1.1rem;
  display: inline-block;
  color: #CD6927;
}

.thumbnail-image {
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;
  height: 100%;
  width: 100%;
  border-radius: 10px;
}

.news-item {

  background-color: #fff;

  &:nth-child(even) {
    background-color: var(--tableAltColour);
  }
}

.news {
  display: grid;
  grid-column-gap: 15px;
  grid-template-columns: 74px 1fr;
  padding: 2px 10px 2px 0;
}

.news-image {

  width: 74px;
  display: inline-block;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  flex-basis: 74px;
  height: 49px;
  max-width: 74px;
  flex-grow: 1;
  border: 1px solid #dfdfdf;
  border-radius: 4px;
}

.news-title {
  display: flex;
  align-items: center;
  flex-grow: 1;
  font-size: 0.75rem;
  font-weight: 400;
  line-height: 1rem;

  a, a:visited {
    color: var(--subtitleColour) !important;
  }

  a:hover {
    color: var(--linkColour) !important;

  }
}


.scroller-footer {
  height: 45px;
  position: absolute;
  bottom: 0px;
  overflow-x: hidden;
  background-color: rgba(232, 243, 255, 0.8);
  width: 100%;
  padding: 0 5px;
  display: flex;
  align-items: center;
}
