<div class="container mt-2">
  <div class="page-top">
    <h1 class="page-header">Searches</h1>
  </div>
  <div *ngIf="isLoading" class="text-center mt-4">
    <i class="fa fa-2x fa-spin fa-spinner"></i>
    <h2 class="mt-2">Loading..</h2>
  </div>
  <div *ngIf="!isLoading">
    <div *ngFor="let search of searches; let i = index" class="mb-4 mt-2">
      <mdb-card style="box-shadow: none; border: 1px solid var(--widgetBorderColour);">
        <mdb-card-header style="padding: 5px 20px 5px 25px; background-color: var(--widgetBorderColour);">
          <div class="row">
            <div class="col-md-8">
              <span style="font-size: 0.875rem;">
                <span style="font-weight: 600">Search: {{ search.searchName }}</span>
              </span>
            </div>
            <div class="col-md-4 text-right">
              <small class="text-muted">Added: {{ search.search.added | date }}</small>
            </div>
          </div>
        </mdb-card-header>
        <mdb-card-body style="padding-bottom: 10px;">

          <app-search-description [description]="search.search.description"></app-search-description>
          <div class="search-info" style="margin-top: 5px;" *ngIf="search.isProfile"> * This is a Search Profile</div>

          <div class="row mt-2">
            <div class="col-md-6">
              <div (click)="editSearch(search)"
                   style="cursor: pointer; display: inline-block; padding-top: 3px;">

                <div *ngIf="search.sendUpdates">
                  <i class="fa fa-check text-success"></i>
                  <span style="font-size: 0.875rem;">
                  Receive <strong>{{ getUpdateFrequencyName(search.updateFrequency) }}</strong> notifications by
                  <span> <strong>{{ getUpdateMethodName(search) }}</strong></span>
                  </span>
                </div>
                <div *ngIf="!search.sendUpdates" class="mb-2">
                  <i class="fa fa-exclamation-triangle"></i> You will not receive Email/SMS updates
                  <button
                    class="btn btn-xs btn-outline-primary" (click)="editSearch(search)">Change
                  </button>
                </div>
              </div>
            </div>
            <div class="col-md-6">

              <div class="search-buttons text-right">
                <button class="btn btn-xs btn-secondary mr-2" (click)="deleteSearch(search)"><i
                  class="fa fa-trash-alt"></i>
                  Delete
                </button>
                <button class="btn btn-xs btn-secondary mr-2" (click)="editSearch(search)"><i
                  class="fas fa-cog"></i>
                  Settings
                </button>
                <button class="btn btn-xs btn-secondary" (click)="viewSearch(search)"><i class="fa fa-search"></i>
                  Run Search
                </button>
              </div>
            </div>
          </div>
        </mdb-card-body>
      </mdb-card>
    </div>

    <div *ngIf="!isLoading && (!searches || searches.length == 0)">


      <div class="d-flex flex-wrap attention-box">

        <div class="text-center flex-grow-1 flex-lg-grow-0 flex-lg-shrink-1">
          <div class="p-4">
            <img src="/assets/images/svg/sad.svg" height="120" width="120" style="fill: #f00; color: #f00"
                 class="svg-color"
                 alt="sadface"/>
          </div>
        </div>

        <div class="flex-grow-1 text-center text-lg-left">

          <div class="px-4 pt-4 pb-2 w-100" id="no-watchlist-widget">
            <h2>No saved searches</h2>
            <div class="mt-4">
              <p>To make things easier you can save the searches you perform regularly.</p>
              <p>Click the <span class="btn btn-secondary btn-xs">Save Search</span> button after you have performed a
                search to save it</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


<app-saved-search-edit [savedSearchDTO]="currentSavedSearch">
</app-saved-search-edit>
