
<div mdbModal #payInvoiceModal="mdbModal" class="modal fade" tabindex="-1"
     role="dialog" aria-labelledby="Pay Invoice" aria-hidden="true" [config]="{ignoreBackdropClick: true}">

  <div class="modal-dialog-centered modal-md modal-dialog" role="document">

    <div class="modal-content mb-5">
      <div class="modal-header">
        Make Payment
      </div>

      <div class="modal-body" style="background-color: #eee;">

        <div *ngIf="!showPaymentForm">
          <div class="text-center" *ngIf="paymentSuccess">
            <i class="fa fa-2x fa-check-circle text-success"></i> <span class="payment-success">Payment Success</span>
            <div class="mt-3">
              <span class="btn btn-primary-outline" (click)="closePayment()">Done</span>
            </div>
          </div>
          <div class="text-center" *ngIf="cardDeclined">
            <i class="fa fa-2x fa-times-circle"></i> <span class="payment-success">Payment Failed</span>

            <div *ngIf="paymentFailReason == 'card_declined'">Card has been declined</div>

            <div class="mt-3">
              <span class="btn btn-primary-outline" (click)="makePayment(payingInvoiceId)">Try again</span>
            </div>
          </div>
        </div>

        <form (ngSubmit)="pay()" #form="ngForm" *ngIf="showPaymentForm">

          <img src="../../../../assets/images/3rdparty/stripe-secure-payment2.svg"
               style="background-color: #cde; border-radius: 7px; padding: 10px;"
               alt="Strip Logo" class="w-100 mb-2">

          <div style="margin-bottom: 10px;" *ngIf="paymentIntent">

            <div style="margin-bottom: 12px;" class="d-flex flex-wrap">
              <div class="flex-shrink-1 text-left">Invoice: <span
                style="font-weight: 600">{{ paymentIntent.metadata.invoiceNumber }}</span></div>
              <div class="flex-grow-1 text-right">Amount: <span
                style="font-weight: 600">{{ paymentIntent.amount / 100.0 | customCurrency }} </span></div>
            </div>

            <ngx-stripe-payment
              [clientSecret]="elementsOptions?.clientSecret"
              [options]="paymentOptions"
              [elementsOptions]="elementsOptions">
              <span style="color: green" *ngxStripeLoadingTemplate>Loading Stripe... </span>
            </ngx-stripe-payment>

            <div *ngIf="paymentFailReason == 'expired_card'" class="text-danger mt-2">ERROR: Card has expired</div>
            <div *ngIf="paymentFailReason == 'incorrect_cvc'" class="text-danger mt-2">ERROR: Incorrect CVC</div>
            <div *ngIf="paymentFailReason == 'incorrect_number'" class="text-danger mt-2">ERROR: Incorrect Card Number
            </div>
          </div>

          <div *ngIf="!paymentIntent" class="text-center pt-2 pb-2">
            <i class="fa fa-spin fa-spinner"></i> Loading..
          </div>

          <div style="margin-top: 15px; text-align: right;">
            <button class="btn btn-primary-outline" style="margin-right: 10px;" (click)="closePayment()">Cancel</button>
            <button class="btn btn-primary" type="submit" [disabled]="!form.valid || processing">
                <span *ngIf="processing">
                  <i class="fa fa-spin fa-spinner"></i> Processing
                </span>
              <span *ngIf="!processing">
                  Pay <span *ngIf="paymentIntent"> {{ paymentIntent?.amount / 100.0 | customCurrency }} </span>
                </span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
