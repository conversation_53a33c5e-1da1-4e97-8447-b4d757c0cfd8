<div class="container mt-2">

  <div *ngIf="sale != null" class="mb-2 mt-2 indent-on-mobile">
    <div class="saleName">{{ sale.saleName}}</div>

    <div *ngIf="sale.liveBiddingStatus == liveBiddingStatusEnum.Paused">
      <div class="sale-status-banner sale-paused-banner">SALE PAUSED</div>
    </div>

    <div *ngIf="sale.liveBiddingStatus == liveBiddingStatusEnum.Ended">
      <div class="sale-status-banner sale-ended-banner">SALE ENDED</div>
    </div>
  </div>


  <div class="d-lg-none sticky-description w-100 pb-2 indent-on-mobile" *ngIf="advert != null">

    <ng-container [ngTemplateOutlet]="vehicleDescription"
                  [ngTemplateOutletContext]="{ advert: advert}"></ng-container>
  </div>

  <div *ngIf="advert != null" [class]="'biddingStatus-' + sale?.liveBiddingStatus">

    <div class="d-flex flex-wrap">

      <div class="order-1 order-md-0 flex-grow-1">

        <div class="row">

          <div class="col-md-4">
            <!-- GALLERY REQUIRED HERE
            <ngx-gallery #gallery [options]="galleryOptions" [images]="galleryImages"></ngx-gallery>
            -->
          </div>

          <div class="col-md-8">

            <div class="d-none d-lg-block">
              <ng-container [ngTemplateOutlet]="vehicleDescription"
                            [ngTemplateOutletContext]="{ advert: advert}"></ng-container>
            </div>

            <div class="mb-2">
              <app-advert-vehicle-info [advertId]="advert.id"></app-advert-vehicle-info>
            </div>

            <div class="mb-2">
              <app-vehicle-valuation [advertId]="advert.id"></app-vehicle-valuation>
            </div>
          </div>
        </div>
      </div>
      <div style="clear: both;"></div>


      <!-- Bidding Section -->
      <div class="order-0 order-md-1 flex-grow-1">
        <div class="row mb-2">
          <div class="col-md-6 col-lg-4 mb-2 order-1 order-md-0 d-none d-md-block">
            <iframe width="100%" height="215" src="http://*************:5080/LiveApp/play.html?name=412722826138310707638723" frameborder="0" allowfullscreen></iframe>
          </div>

          <div class="col-md-6 col-lg-5 mb-2 order-0 order-md-1">

            <div class="non-runner" *ngIf="advert?.vehicle?.runner == false"><i class="fa fa-exclamation-triangle"></i> NOTE: Non-runner</div>

            <app-advert-bid-box [advertId]="advert?.id" [user]="user"></app-advert-bid-box>
          </div>

          <div class="col-md-6 col-lg-3 text-center order-3">
            <div class="widget" *ngIf="canProxyBid">
              <div class="text-center proxy-bid-header">Place Max/Proxy Bid</div>
              <div class="text-center pt-3 pb-2">
                <app-advert-proxy-bid [advert]="advert" [user]="user"></app-advert-proxy-bid>
              </div>
            </div>
            <div class="mt-3">
              <message-rostrum [advert]="advert"></message-rostrum>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


<div mdbModal #appraisalModal="mdbModal" class="modal fade" tabindex="-1"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header narrow">
        <button type="button" class="close pull-right" aria-label="Close" (click)="this.appraisalModal.hide()"><span
          aria-hidden="true">×</span></button>
        Vehicle Condition
      </div>
      <div class="modal-body">


        <mdb-tabset [buttonClass]="'classic-tabs tabs-custom'" class="classic-tabs">
          <mdb-tab heading="External">
            <app-advert-view-splat [advertId]="advert?.id" [internal]="false">
            </app-advert-view-splat>
          </mdb-tab>
          <mdb-tab heading="Internal">
            <app-advert-view-splat [internal]="true" [advertId]="advert.id">
            </app-advert-view-splat>
          </mdb-tab>
        </mdb-tabset>
      </div>
    </div>
  </div>
</div>

<ng-template let-advert="advert" #vehicleDescription>
  <div class="d-flex flex-wrap mb-2">
    <div class="flex-grow-1">
      <span class="lotNo">{{ advert.lotNo }}</span>
      <span class="line-1">

        {{ advert.vehicle?.plate?.plateName }}
        {{ advert.vehicle?.make?.makeName }}
        {{ advert.vehicle?.model?.modelName }}
        {{ advert.vehicle?.deriv?.derivName }}
            </span>

      <div class="line-2">
        {{ advert.vehicle.transmissionType.transmissionTypeName }}
        {{ advert.vehicle.fuelType.fuelTypeName }}
        {{ advert.vehicle.bodyType.bodyTypeName }}
        {{ advert.vehicle.doors }} door

      </div>
    </div>
    <div class="flex-shrink-1 pt-2">
      <button class="btn btn-xs btn-secondary" (click)="appraisalModal.show()">&nbsp;<i
        class="fa fa-clipboard-check"></i> Condition Report&nbsp;
      </button>
    </div>
  </div>
</ng-template>
