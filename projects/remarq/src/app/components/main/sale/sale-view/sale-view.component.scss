.lotNo {
  font-weight: 800;
}

/* Paused + Ended */
.biddingStatus-0, .biddingStatus-2 {
  opacity: 0.5;
  pointer-events: none;
}

.non-runner {

  background-color: var(--errorColour);
  color: #fff;
  font-size: 0.75rem;
  font-weight: bold;
  text-align: center;
  margin-bottom: 7px;
}

.sale-status-banner {

  padding: 5px 10px;
  font-weight: 600;
  border-radius: 5px;
  margin-top: 5px;
  margin-bottom: 5px;

  &.sale-paused-banner {
    background-color: var(--warning);
  }

  &.sale-ended-banner {
    background-color: var(--errorColour);
    color: #fff;
  }
}

.proxy-bid-header {
  font-weight: 500;
  background-color: var(--secondaryButtonColour);
  color: #fff;
  border-top-right-radius: calc(var(--widgetBorderRadius) - 1px);
  border-top-left-radius: calc(var(--widgetBorderRadius) - 1px);
  font-size: 0.85rem;
}


.line-1 {
  font-weight: 500;
}

.line-2 {
  font-size: 0.875rem;
  line-height: 0.875rem;
}

.saleName {
  font-weight: 500;
}

.sticky-description {
  position: sticky;
  top: var(--headerHeight);
  z-index: 999;
  background-color: var(--bgColour);
}

.enter-max-bid {
  font-size: 0.875rem;
  font-weight: 400;
}
