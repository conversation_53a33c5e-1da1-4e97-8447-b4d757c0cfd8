<div *ngIf="vehicleFaultCheck && form" class="widget">
  <form [formGroup]="form">
    <div class="d-flex flex-wrap grid-gap-10">
      <div class="md-form flex-grow-1" style="margin-bottom: 5px !important;">
        <input class="form-control date-input float-label" type="date" id="inspectionDate" mdbInput
               formControlName="inspectDate">
        <label for="inspectionDate">Date of Inspection</label>
      </div>
      <div class="md-form flex-grow-1" style="margin-bottom: 5px !important">
        <input mdbInput type="number" class="form-control float-label" formControlName="odometer"
               id="odometer" #odometer (keydown.enter)="odometer.blur();"/>
        <label for="odometer">Odometer</label>
      </div>
    </div>
  </form>
</div>

<!-- Create a tab panel for each category -->

<div class="d-flex flex-wrap mt-2 mb-2" *ngIf="faultCategories && faultCategories.length > 0">
  <div class="flex-grow-1">
    <ul class="nav">
      <li class="nav-item" *ngFor="let category of sortedCategories(faultCategories); let i = index"
          (click)="activeTab = i; selectedCategory = category"
          [class.active]="i === activeTab" (click)="activeTab = i">
        <a [class.active]="i === activeTab">{{category.name}}</a>
      </li>
    </ul>
  </div>
  <!--
  <div class="flex-shrink-1"><button class="btn btn-xs btn-outline-primary"><i class="fa fa-plus-circle"></i> New Check</button></div>
  -->
</div>

<app-vehicle-fault-category [category]="selectedCategory"
                            *ngIf="selectedCategory != null"

                            [faultStatuses]="faultStatuses"
                            (faultItemStatusChanged)="onFaultItemStatusChanged($event)">
</app-vehicle-fault-category>
