<form [formGroup]="form">


  <div class="btn-group mr-2">

    <label
      *ngFor="let option of serviceHistoryTypeOptions"
      class="btn btn-sm"
      [value]="option.value"
      [ngClass]="{ active: f.serviceHistoryType.value == option.value }"
      formControlName="serviceHistoryType"
      (click)="serviceHistoryTypeSelected($event)"
      mdbRadio="{{ option.value }}"
      mdbWavesEffect
    >
      {{ option.label }}
    </label>
  </div>

  <div *ngIf="f['serviceHistoryType'].value != ServiceHistoryTypeEnum.None" class="mt-3">
    <table class="table table-sm table-striped" *ngIf="histories().length > 0">
      <thead>
      <tr>
        <th>Service Date</th>
        <th style="width: 80px">Odometer</th>
        <th>Serviced By</th>
        <th style="width: 150px">Garage Type</th>
        <th>&nbsp;</th>
      </tr>
      </thead>
      <tbody>

      <tr *ngFor="let history of histories().controls; let i=index" [formGroup]="history">
        <td nowrap class="md-form input-sm pt-3">
          <input mdbInput
                 style="max-width: 140px"
                 placeholder="Select Date" type="date" id="serviceDate"
                 class="form-control" formControlName="serviceDate">
        </td>
        <td class="md-form input-sm pt-3" style="min-width: 90px;">
          <input formControlName="odometer" placeholder="Mileage" class="form-control"/>
        </td>
        <td class="md-form input-sm pt-3">
          <input type="text" formControlName="dealerName" placeholder="Company Name" class="form-control"/>
        </td>
        <td class="pt-3">
          <div class="select select-sm">
            <mdb-select-2
              [outline]="true"
              formControlName="dealerType">
              <mdb-select-option
                *ngFor="let option of dealerTypeOptions"
                [value]="option.value">{{ option.label }}
              </mdb-select-option>
            </mdb-select-2>
          </div>
        </td>
        <td class="shink-cell text-center pt-3">
          <input type="hidden" formControlName="id">
          <button class="btn btn-sm btn-secondary">
            <i class="fa fa-trash-alt delete-row" (click)="removeHistory(history, i)"></i>
          </button>
        </td>
      </tr>

      </tbody>
    </table>
  </div>

  <div *ngIf="f['serviceHistoryType'].value != ServiceHistoryTypeEnum.None" class="pt-1">
    <button type="button" (click)="appendHistory()" class="btn btn-xs btn-secondary mb-2 mt-1">Add Service Date</button>
  </div>
</form>
