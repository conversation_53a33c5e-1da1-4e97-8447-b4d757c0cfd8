import {Component, Input, OnInit} from '@angular/core';
import {UntypedForm<PERSON>rray, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators} from '@angular/forms';
import {IMyOptions} from 'ng-uikit-pro-standard';
import {EventService, VehicleService} from '../../../../services';
import {compare} from 'fast-json-patch';
import {FormsService} from '../../../../services';
import {DatePipe} from '@angular/common';
import {ServiceHistoryDTO, VehicleDTO} from "../../../../global/interfaces";
import {HelpersService, LoggerService} from "../../../../global/services";
import {AdvertEventEnum, DealerTypeEnum, ServiceHistoryTypeEnum} from "../../../../global/enums";

@Component({
  selector: 'app-vehicle-service-history',
  templateUrl: './vehicle-service-history.component.html',
  styleUrls: ['./vehicle-service-history.component.scss'],
  providers: [DatePipe]
})
export class VehicleServiceHistoryComponent implements OnInit {

  public form: UntypedFormGroup;
  public formState: any;
  public ServiceHistoryTypeEnum = ServiceHistoryTypeEnum;
  public serviceHistoryTypeOptions: any[];
  public dealerTypeOptions: any[];
  public myDatePickerOptions: IMyOptions = {
    dateFormat: 'dd/mm/yyyy'
  };

  @Input() vehicle: VehicleDTO;
  private previousHistory: {} = {};

  constructor(
    private helperService: HelpersService,
    private fb: UntypedFormBuilder,
    private logService: LoggerService,
    private formsService: FormsService,
    private eventService: EventService,
    private vehicleService: VehicleService,
    private datePipe: DatePipe) {

    // convert service history type and dealer type enums to selectable options
    this.serviceHistoryTypeOptions = helperService.getLabelsAndValues(ServiceHistoryTypeEnum, true);
    this.dealerTypeOptions = helperService.getLabelsAndValues(DealerTypeEnum);
  }

  logger = this.logService.taggedLogger(this.constructor?.name);

  async ngOnInit() {

    this.logger.info("Service history type: ", this.vehicle.serviceHistoryType);

    this.refreshHistories(this.vehicle.serviceHistories);
  }

  refreshHistories(histories) {

    this.form = this.fb.group({
      serviceHistoryType: new UntypedFormControl(this.vehicle.serviceHistoryType.toString()),
      serviceHistories: this.fb.array([])
    });

    if (histories && histories.length > 0) {
      histories.forEach(history => {
        this.histories().push(this.newHistory(history));
      });
    }

    this.formState = this.form.value;
  }

  get f() {
    return this.form ? this.form.controls : null;
  }

  histories(): UntypedFormArray {
    return this.form.get("serviceHistories") as UntypedFormArray;
  }

  appendHistory() {

    this.vehicleService.addServiceHistory(this.vehicle.id, {}).then((returnedHistory) => {
      this.histories().push(this.newHistory(returnedHistory));
    });
  }

  newHistory(sh?: ServiceHistoryDTO): UntypedFormGroup {

    const x = this.fb.group({
        id: new UntypedFormControl(sh ? sh.id : null, {}),
        vehicleId: new UntypedFormControl(this.vehicle.id, {}),
        serviceDate: new UntypedFormControl(this.datePipe.transform((sh ? sh.serviceDate : Date.now()), 'yyyy-MM-dd'), {
          updateOn: 'blur',
          validators: [Validators.required]
        }),
        odometer: new UntypedFormControl(sh ? sh.odometer : '', {
          updateOn: 'blur',
          validators: [Validators.required, Validators.pattern(this.formsService.numberPattern)]
        }),
        dealerType: new UntypedFormControl(sh ? sh.dealerType : '', {updateOn: 'blur', validators: [Validators.required]}),
        dealerName: new UntypedFormControl(sh ? sh.dealerName : '', {updateOn: 'blur'})
      }
    );

    this.previousHistory[sh.id] = x.value;

    x.valueChanges.subscribe(value => {
      this.updateHistory(x);
    });

    return x;
  }


  updateHistory(form: UntypedFormGroup) {
    const serviceHistoryId = form.value.id;
    const patch = compare(this.previousHistory[serviceHistoryId], form.value);

    this.vehicleService.updateServiceHistory(this.vehicle.id, serviceHistoryId, patch).then(result => {
      this.previousHistory[serviceHistoryId] = form.value;
    });
  }

  removeHistory(history: any, i: number) {

    this.histories().removeAt(i);

    const historyId = history.value.id;

    if (historyId == null) {
      return;
    }

    this.vehicleService.deleteServiceHistory(this.vehicle.id, historyId).then(result => {
    });
  }

  serviceHistoryTypeSelected(option: any) {
    const object = compare({}, {serviceHistoryType: parseInt(this.f.serviceHistoryType.value, 10)});
    this.eventService.AdvertActionEvent.emit({eventType: AdvertEventEnum.VehicleDetailsChangedEvent, object});
  }
}
