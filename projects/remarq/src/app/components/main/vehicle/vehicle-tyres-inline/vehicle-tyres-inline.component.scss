.tyre-depth {
  border-bottom: 1px solid rgba(100, 100, 100, 0.1);
  padding-bottom: 5px;
  margin-bottom: 5px;

  &:last-child {
    border-bottom: 0px !important;
    margin-bottom: 0px !important;
  }
}

.tyre-type {
  display: inline-block;
  padding-right: 5px;
  font-size: 0.6rem;
}

.tyre-list {
  display: flex;
  flex-wrap: wrap;
  grid-gap: 15px;

  .tyre-value {
    background-color: var(--chipBackgroundColour);
    border: 1px solid var(--chipBorderColour);
    padding: 5px 8px;
    border-radius: 15px;
    font-weight: 500;
    color: var(--textColour);
    font-size: 0.8rem;
    display: inline-block;
  }
}

.tyre-depth-img {
  width: 30px;
  height: 18px;
  display: inline-block;
  margin-right: 5px;
  border-radius: 4px;
  background-image: url("/assets/images/svg/Tire-Tracks2.svg");
  background-repeat: no-repeat;
  background-size: cover;
  vertical-align: middle;
}

.tyre-depth-1 {
  filter: opacity(12%)
}

.tyre-depth-1 {
  filter: opacity(25%)
}

.tyre-depth-2 {
  filter: opacity(37%)
}

.tyre-depth-3 {
  filter: opacity(50%)
}

.tyre-depth-4 {
  filter: opacity(62%)
}

.tyre-depth-5 {
  filter: opacity(75%)
}

.tyre-depth-7 {
  filter: opacity(87%)
}

.tyre-depth-8 {
  filter: opacity(100%)
}

.tyre-label {
  font-weight: 500;
  color: var(--textLabelColour);
  font-size: 0.85rem;
}


.spare {
  border: 1px solid #aaa;
  padding: 5px;
  border-radius: 3px;
}
