import { AfterViewInit, Component, Input, OnChanges, SimpleChanges } from "@angular/core";

declare var YT: any;

@Component({
  selector: 'youtube-player-wrapper',
  template: `
    <div class="player-wrapper">
      <div *ngIf="videoId" style="height: 100%">
        <div *ngIf="!playerReady && !errorMessage" class="loading-message">Video loading...</div>
        <div *ngIf="errorMessage" class="error-message">{{ errorMessage }}</div>
        <div [id]="playerElementId" class="player"></div>
      </div>
      <div *ngIf="!videoId" class="no-video-message">
        No video specified.
      </div>
    </div>
  `,
  styleUrls: ['youtube-player-wrapper.component.scss']
})
export class YoutubePlayerWrapperComponent implements AfterViewInit, OnChanges {
  player: any;
  playerElementId: string;
  playerReady: boolean = false;
  errorMessage: string = '';

  @Input() videoId: string;
  @Input() autoplay: boolean = false; // Default to false

  constructor() {
    // Generate a unique ID for the player element
    this.playerElementId = 'player-' + Math.floor(Math.random() * 1000000);
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.videoId) {
      this.playerReady = false;
      this.errorMessage = '';
      if (this.player && this.player.loadVideoById) {
        // If the player exists and is ready, load the new video
        this.player.loadVideoById(this.videoId);
      } else if (this.videoId) {
        // If the player is not yet ready but videoId is set, attempt to create the player
        if (this.playerReady === false && typeof YT !== 'undefined' && YT && typeof YT.Player !== 'undefined') {
          this.createPlayer();
        }
      }
    }
  }

  ngAfterViewInit() {
    // Check if the YT namespace is ready
    if (typeof YT !== 'undefined' && YT && typeof YT.Player !== 'undefined') {
      this.createPlayer();
    } else {
      // Add to the list of callbacks to be executed when the API is ready
      this.addYouTubeAPIReadyCallback(() => this.createPlayer());
    }
  }

  createPlayer() {
    this.player = new YT.Player(this.playerElementId, {
      height: '100%',
      width: '100%',
      videoId: this.videoId,
      playerVars: {
        autoplay: this.autoplay ? 1 : 0,
      },
      events: {
        'onReady': this.onPlayerReady.bind(this),
        'onStateChange': this.onPlayerStateChange.bind(this),
        'onError': this.onPlayerError.bind(this),
      },
    });
  }

  onPlayerReady(event) {
    console.log("YouTube Player ready: ", event);
    this.playerReady = true;
    this.errorMessage = '';
  }

  onPlayerStateChange(event) {
    console.log("Player state changed: ", event);
  }

  onPlayerError(event) {
    console.log("Player error: ", event);
    this.playerReady = false;
    // Determine the error message based on the error code
    switch (event.data) {
      case 2:
        this.errorMessage = 'Invalid video parameter.';
        break;
      case 5:
        this.errorMessage = 'HTML5 player error.';
        break;
      case 100:
        this.errorMessage = 'Video not found.';
        break;
      case 101:
      case 150:
        this.errorMessage = 'Video not playable yet.';
        break;
      default:
        this.errorMessage = 'Video is processing or unavailable. Please check back later.';
        // Implement retry logic for processing videos
        this.retryLoadingVideo();
        break;
    }
  }

  retryLoadingVideo() {
    // Retry loading the video after a delay
    setTimeout(() => {
      if (this.player && this.player.loadVideoById) {
        this.errorMessage = '';
        this.player.loadVideoById(this.videoId);
      }
    }, 10000); // Retry after 10 seconds
  }

  addYouTubeAPIReadyCallback(callback: () => void) {
    // Initialize the array if it doesn't exist
    if (!window['onYouTubeIframeAPIReadyCallbacks']) {
      window['onYouTubeIframeAPIReadyCallbacks'] = [];
    }
    // Add the callback to the array
    window['onYouTubeIframeAPIReadyCallbacks'].push(callback);

    // Assign the global API ready function only once
    if (!window['onYouTubeIframeAPIReady']) {
      window['onYouTubeIframeAPIReady'] = () => {
        window['onYouTubeIframeAPIReadyCallbacks'].forEach((cb: () => void) => cb());
      };
    }
  }
}
