export interface PaymentResultDTO {
  id: string;
  success: boolean;
  message: string;
  // Amount charged, in the smallest currency unit
  amount: number;
  // Currency of the payment (e.g., "usd", "gbp")
  currency: string;
  // Payment method ID used for the payment (only return last four chars)
  paymentMethod: string;
  // Timestamp of when the PaymentIntent was created
  created: Date;
  errorType?: string;
  errorCode?: string;
  status: string;
  requiresAction: boolean;
}
