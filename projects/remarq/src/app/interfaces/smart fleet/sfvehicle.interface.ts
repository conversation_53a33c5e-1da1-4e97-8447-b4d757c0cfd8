export interface SFVehicle {
    vehicle: {
        itemNo: string; // i.e. VEHICLE
        variantCode: string;
        serialNo: string; // i.e. "SER470065"
        brand: string; // i.e. "HONDA"
        carline: string; // i.e. "CIVIC"
        registrationNo: string; // i.e. "CU68ZKM"
        vehicleIdentificationNo: string; // i.e. "NLAFC8570JW002228"
        derivative: string; // i.e. "1.0 VTEC Turbo SR 4dr CVT"
        trim: string;
        bodystyle: string; // i.e. "Saloon"
        engine: string; // i.e. "1.0"
        transmission: string; // i.e. "Automatic"
        fuel: string; // i.e. "Petrol"
        noOfDoors: number; // i.e. 4
        noOfSeats: number; // i.e. 5
        colour: string; // i.e. "Grey"
        mileage: number; // i.e. 10608.0
        dateOfRegistration: Date; // i.e. "2018-09-26T00:00:00+00:00"
        inspectionSet: number; // i.e. 1
        programmeCode: string; // i.e. "PRG00418"
        quantityInStock: number; // i.e. 1.0
        statusChanged: Date; // i.e. "2020-11-04T11:03:27.877+00:00"
        status: {
            status: number; // i.e. 700
            description: string; // i.e. "Available For Sale"
        };
        targetGrade: string; // i.e. "CLEAN"
        actualGrade: string;
        actualGradeDescription: string;
        targetGradeDescription: string; // i.e. "CLEAN"
        currentBinCode: string; // i.e. LL55
        currentLocationCode: string; // i.e. "COLDMEECE"
        globalDimension1Code: string; // i.e. "SFS"
        thirdPartyAuthorisation: boolean;
        blockedReason: string;
        capId: number; // i.e. 86161
        programme: string;
        vendor: string;
        activeSalesChannel: {
            channelNo: string; // i.e. "SCH000010"
            description: string; // i.e. "Arval B2C Retail (AutoTrader)"
            active: boolean
        };
        lastTimeModified: Date; // i.e. "2020-11-12T14:26:52.073+00:00"
        attributes: []
    };
    mainInspection: {
        no: string; // i.e. "INSP1813373"
        inspectionTypeCode: number; // i.e. 0
        inspectionType: string; // i.e. "Main"
        inspector: string; // i.e. "PRAJOWL.ADHIKARI"
        result: string; // i.e. "FAIL"
        resultCode: number; // i.e. 2
        status: string; // i.e. "Complete"
        statusCode: number; // i.e. 2
        startDate: Date; // i.e. "2020-10-01T09:15:28"
        specification: [
            {
                name: string; // i.e. "NSR Tyre Tread"; "OSR Tyre Tread"; "Seats Front & Rear"; "Non Factory Options"
                value: string; // i.e. "5"
            }
        ]
    };
    mainJob: {
        no: string; // i.e. "JOB000437519";
        description: string; // i.e. " AD\\WILLIAMSR";
        billToCustomerNo: string; // i.e. "CUS07242";
        billToName: string; // i.e. "Motability Operations";
        creationDate: Date; // i.e. "2020-11-12T11:48:37.69+00:00";
        startingDate?: Date;
        endingDate?: Date;
        status: {
          id: number; // i.e. 3;
          name: string; // i.e. "Completed"
        };
        approvalStatus: {
          id: number; // i.e. 2;
          name: string; // i.e. "Approved"
        };
        approvedBy: string; // i.e. "AUTO-APPROVED";
        approvedOn: Date; // i.e. "2020-11-12T12:39:50.55+00:00";
        rejectedBy: string;
        inspectionNo: string; // i.e. "INSP1869883";
        assignedUserId: string; // i.e. "AD\\WILLIAMSR";
        assignedUserId_Customer: string;
        targetGradeId: string; // i.e. "MFL_EX";
        quotedOn: Date; // i.e. "2020-11-12T12:39:49.547+00:00";
        workshopStatus: {
          id: number; // i.e. 0
          name: string; // i.e. Completed
        };
        locationCode: string; // i.e. "COLDMEECE";
        createdAsAnUpgrade: boolean;
        qcComplete: boolean;
        qcCompletedOn: Date; // i.e. "2020-11-13T16:53:34.827+00:00";
        completedBy: string; // i.e. "AD\\SVCNAVJOBQUEUEOTHER";
        completedOn: Date;
        adhoc: boolean; 
        targetGrade?: any;
        jobTasks: [
            {
                jobTaskNo: string; // i.e. "00010";
                description: string; // i.e. "Setup Fee";
                approvalStatus: {
                    id: number;
                    name: string; // i.e. "Approved"
                };
                approvalRequired: boolean;
                approvalWasRequired: boolean;
                workshopStatus: {
                    id: number;
                    name: string; // i.e. Completed
                };
                qcInspectionRequired: boolean;
                requiresWork: boolean;
                upgraded: boolean;
                rejectionReason?: any;
                inspectionDefectLocation: {
                    entryNo: number;
                    description: string;
                };
                inspectionDefectType: {
                    entryNo: number;
                    description: string;
                };
                inspectionRepairType: {
                    entryNo: number;
                    description: string;
                };
                isValet: boolean;
                hasAssociatedTasks: boolean;
                images: [];
                thumbnailImages: [];
                estimatedValue: number; 
                value: string;
                comments: []
            }
        ];
        programmeCode: string;
        referenceNo: string;
    }
}

/*
Example thumbnail and image content: 
"https://apis.test.smartfleet.co.uk/vehicles/images/INSP1869883_DL30000_L10000_2020_11_12_11_41.jpg?data=Zdo3xHou0-CUpXS2DoMpG4OLgHlsnOWA-Z2Ey3uUgX9O7QymHRxFSP0hsiBt-RW38n6IHI9KW6sAyyg_ERQE0eO7QKGSnAcK3UDQatmoNbSCzAxaJVQc2-nEpHHujFLe-7N167-Xq0_GX5jfT35iST5D6lrOdQhkCkgISfB7k49li9cTyVKHEYFzaAMJMg7aSL99Ea4AUHLx7dTrOIM-TcZUVlOGtkXaELAOzahYqd40CKk7xv5YdVSSW4ILTwjR";
"https://apis.test.smartfleet.co.uk/vehicles/images/INSP1869883_DL30000_L20000_2020_11_12_11_41.jpg?data=Zdo3xHou0-CUpXS2DoMpG4OLgHlsnOWA-Z2Ey3uUgX9O7QymHRxFSP0hsiBt-RW38n6IHI9KW6sAyyg_ERQE0eO7QKGSnAcK3UDQatmoNbSCzAxaJVQc2-nEpHHujFLeZmwEj-kmvdnA7MrhDrdpA5TwButPzwvJr_vL3rR_Lgda5Il92LYZ5UAO93O55PVZj2XVSmFViCLibK1gFQ-jrCNt4WZLf9kw1CsF48j_-NJqCINMJ8uc3KdZtpop7sTF";
"https://apis.test.smartfleet.co.uk/vehicles/images/INSP1869883_DL30000_L30000_2020_11_12_11_42.jpg?data=Zdo3xHou0-CUpXS2DoMpG4OLgHlsnOWA-Z2Ey3uUgX9O7QymHRxFSP0hsiBt-RW38n6IHI9KW6sAyyg_ERQE0eO7QKGSnAcK3UDQatmoNbSCzAxaJVQc2-nEpHHujFLeedvCpv29ZRtqU1oOpNvAhE3mqsekIqko3GGNMzLt3vkBEXLtm7H5IvpBtYl2NUAvQz80XcAHTgnd46FX2QyV5eDkqIpk_NbljcHjMWSGLNxNcighhtIJQ0S--jC7vtds"
*/