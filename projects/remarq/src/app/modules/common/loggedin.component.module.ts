import {NgModule} from '@angular/core';
import {
  AdvertVehicleInfoComponent
} from "../../components/main/advert/advert-vehicle-info/advert-vehicle-info.component";
import {
  VehicleTyresInlineComponent
} from "../../components/main/vehicle/vehicle-tyres-inline/vehicle-tyres-inline.component";
import {
  AdvertVehicleDvlaComponent
} from "../../components/main/advert/advert-vehicle-dvla/advert-vehicle-dvla.component";
import {AppUnderNavbarComponent, VehicleValuationComponent} from "../../components";
import {MDBBootstrapModulesPro} from "ng-uikit-pro-standard";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {RouterModule} from "@angular/router";
import {HttpClientModule} from "@angular/common/http";
import {ImageCropperComponent} from "ngx-image-cropper";
import {CommonModule} from "@angular/common";
import {NgsgModule} from "ng-sortgrid";
import {YouTubePlayerModule} from "@angular/youtube-player";
import {
  AddressService,
  AdvertSearchService,
  AdvertService,
  AttribService, CountryProductService,
  CountryService,
  DealService,
  FormsService,
  InMailService,
  InvoiceService,
  LookupService,
  MapService,
  OfferService,
  PlatformService,
  ProductService, RateCardService,
  SaleService,
  SaleTypeService,
  SearchService,
  SignalRService,
  SiteService,
  StatusService, TaxService, TermsTemplateService,
  VehicleService,
  WatchlistService,
  LambdaFunctionsService,
  PaymentService
} from "../../services";
import {CustomCurrencyPipe, NoSpacesPipe} from "../../pipes";
import {IndexModule} from "../index.module";
import {} from "../../global/interfaces";
import {
  HandlerService,
  ImageService,
  HelpersService,
  ValuationQuoteService,
  VehicleLookupService
} from "../../global/services";
import {} from "../../global/enums";
import {HttpModule} from "../../global/modules";
import {AppSideMenuComponent, DeleteConfirmModalComponent, LoadingSpinnerComponent} from "../../global/components";
import {PenceCurrencyPipe} from "../../pipes/pence-currency-pipe";
import {SecondsToHumanTimePipe} from "../../pipes/seconds-to-human-time.pipe";

@NgModule({
  declarations: [
    AdvertVehicleDvlaComponent,
    AppUnderNavbarComponent,
    AppSideMenuComponent,
    DeleteConfirmModalComponent,
    NoSpacesPipe,
    CustomCurrencyPipe,
    VehicleValuationComponent,
    VehicleTyresInlineComponent,
    LoadingSpinnerComponent,
    PenceCurrencyPipe,
    SecondsToHumanTimePipe
  ],
  imports: [
    IndexModule,
    MDBBootstrapModulesPro.forRoot(),
    HttpModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule,
    FormsModule,
    HttpClientModule,
    ImageCropperComponent,
    CommonModule,
    NgsgModule,
    YouTubePlayerModule,
    MDBBootstrapModulesPro,
    HttpModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule,
    FormsModule,
    HttpClientModule,
    CommonModule,
    NgsgModule
  ],
  providers: [
    AddressService,
    AdvertSearchService,
    AdvertService,
    AttribService,
    CountryService,
    CountryProductService,
    DealService,
    FormsService,
    HelpersService,
    HandlerService,
    ImageService,
    InMailService,
    InvoiceService,
    LookupService,
    MapService,
    OfferService,
    PlatformService,
    ProductService,
    RateCardService,
    SearchService,
    SignalRService,
    SiteService,
    StatusService,
    SaleService,
    SaleTypeService,
    TaxService,
    VehicleLookupService,
    ValuationQuoteService,
    VehicleService,
    WatchlistService,
    YouTubePlayerModule,
    TermsTemplateService,
    LambdaFunctionsService,
    PaymentService
  ],
  exports: [
    IndexModule,
    DeleteConfirmModalComponent,
    VehicleTyresInlineComponent,
    AdvertVehicleDvlaComponent,
    VehicleValuationComponent,
    CustomCurrencyPipe,
    AppSideMenuComponent,
    AppUnderNavbarComponent,
    NoSpacesPipe,
    LoadingSpinnerComponent,
    PenceCurrencyPipe,
    SecondsToHumanTimePipe
  ]
})
export class LoggedinComponentModule {
}
