import {NgModule} from '@angular/core';
import {CommsTemplateComponent} from '../components/admin/comms/comms-template.component';
import {CommsMaintenanceComponent} from '../components/admin/comms/comms-maintenance.component';
import {CommsEventComponent} from '../components/admin/comms/comms-event.component';
import {CommsHistoryComponent} from '../components/admin/comms/comms-history.component';
import {CommsEventService, CommsHistoryService, CommsTemplateService} from "../services/index";
import {MDBBootstrapModulesPro} from "ng-uikit-pro-standard";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {CommonModule} from "@angular/common";
import {CommsRoutingModule} from "./comms-routing.module";
import {NgxJsonViewerModule} from "ngx-json-viewer";
import {NgxPaginationModule} from "ngx-pagination";

@NgModule({
  imports: [
    CommonModule,
    CommsRoutingModule,
    MDBBootstrapModulesPro,
    FormsModule,
    ReactiveFormsModule,
    NgxJsonViewerModule,
    NgxPaginationModule
  ],
  declarations: [
    CommsMaintenanceComponent,
    CommsTemplateComponent,
    CommsHistoryComponent,
    CommsEventComponent,
  ],
  providers: [
    CommsTemplateService, CommsEventService, CommsHistoryService
  ]
})
export class CommsModule {
}
