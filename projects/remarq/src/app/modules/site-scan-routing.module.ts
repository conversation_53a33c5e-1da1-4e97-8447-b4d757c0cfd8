import {NgModule} from '@angular/core';
import {Routes, RouterModule} from '@angular/router';
import {
  Scan<PERSON>omponent,
  ScanConfigureComponent, Scan<PERSON>ustomerComponent, ScanDashboard<PERSON>omponent,
  <PERSON>an<PERSON><PERSON>Component,
  ScanServiceComponent,
  Scan<PERSON>tyleComponent
} from "../components/admin/site-scan";
import {RoleGuardService as AuthGuard} from "../services";
import {ScanQueueComponent} from "../components/admin/site-scan/scan-queue/scan-queue.component";
import {ScanVehicleComponent} from "../components/admin/site-scan/scan-vehicle/scan-vehicle.component";
import {ScanErrorSummaryComponent} from "../components/admin/site-scan/scan-error-summary/scan-error-summary.component";
import {ScanErrorDetailComponent} from "../components/admin/site-scan/scan-error-detail/scan-error-detail.component";

const routes: Routes = [
  {
    path: '',
    canActivate: [AuthGuard],
    data: {
      roles: ['ADMIN']
    },
    component: ScanComponent,
    children: [
      {path: 'scan-configure/:scanServiceId/:scanStyleId/:scanStageId/:scanCustomerId', component: ScanConfigureComponent},
      {path: 'scan-configure/:scanServiceId/:scanStyleId/:scanStageId', component: ScanConfigureComponent},
      {path: 'scan-configure/:scanServiceId/:scanStyleId', component: ScanConfigureComponent},
      {path: 'scan-configure/:scanServiceId', component: ScanConfigureComponent},
      {path: 'scan-configure', component: ScanConfigureComponent},
      {path: 'scan-field', component: ScanFieldComponent},
      {path: 'scan-service', component: ScanServiceComponent},
      {path: 'scan-style', component: ScanStyleComponent},
      {path: 'scan-customer', component: ScanCustomerComponent},
      {path: 'scan-vehicles', component: ScanVehicleComponent},
      {path: 'scan-dashboard', component: ScanDashboardComponent},
      {path: 'scan-error-summary', component: ScanErrorSummaryComponent},
      {path: 'scan-error-detail/:scanStyleId', component: ScanErrorDetailComponent},
      {path: 'scan-queue', component: ScanQueueComponent},
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
})

export class SiteScanRoutingModule {
}
