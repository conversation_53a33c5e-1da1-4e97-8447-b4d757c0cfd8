import {NgModule} from "@angular/core";
import {ScanComponent} from "../components/admin/site-scan/scan.component";
import {ScanConfigureComponent} from "../components/admin/site-scan/scan-configure/scan-configure.component";
import {
  Scan<PERSON>ustomerComponent, ScanDashboardComponent,
  ScanFieldComponent,
  ScanServiceComponent,
  ScanStyleComponent
} from "../components/admin/site-scan";
import {ScanningService} from "../components/admin/services/site-scan";
import {AdminRoutingModule} from "./admin-routing.module";
import {SiteScanRoutingModule} from "./site-scan-routing.module";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {RouterModule} from "@angular/router";
import {MDBBootstrapModulesPro} from "ng-uikit-pro-standard";
import {ImageCropperComponent} from "ngx-image-cropper";
import {ColorPickerModule} from "ngx-color-picker";
import {NgxGoogleAnalyticsModule, NgxGoogleAnalyticsRouterModule} from "ngx-google-analytics";
import {DndModule} from "ngx-drag-drop";
import {NgsgModule} from "ng-sortgrid";
import {NgxPaginationModule} from "ngx-pagination";
import {DragDropModule} from "@angular/cdk/drag-drop";
import {MainModule} from "./main.module";
import {NgxJsonViewerModule} from "ngx-json-viewer";
import {NgToggleModule} from "ng-toggle-button";
import {CommonModule} from "@angular/common";
import {LoggedinComponentModule} from "./common";
import {HttpModule} from "../global/modules";
import {HttpClientModule} from "@angular/common/http";
import {ScanQueueComponent} from "../components/admin/site-scan/scan-queue/scan-queue.component";
import {ScanVehicleComponent} from "../components/admin/site-scan/scan-vehicle/scan-vehicle.component";
import {ScanErrorSummaryComponent} from "../components/admin/site-scan/scan-error-summary/scan-error-summary.component";
import {ScanErrorDetailComponent} from "../components/admin/site-scan/scan-error-detail/scan-error-detail.component";

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    ReactiveFormsModule,
    MDBBootstrapModulesPro,
    ImageCropperComponent,
    ColorPickerModule,
    NgxGoogleAnalyticsModule,
    NgxGoogleAnalyticsRouterModule,
    LoggedinComponentModule,
    DndModule,
    NgsgModule,
    NgxPaginationModule,
    HttpModule,
    HttpClientModule,
    ReactiveFormsModule,
    AdminRoutingModule,
    DragDropModule,
    MainModule,
    NgxJsonViewerModule,
    NgToggleModule,
    SiteScanRoutingModule,
  ],
  declarations: [
    ScanComponent,
    ScanConfigureComponent,
    ScanCustomerComponent,
    ScanDashboardComponent,
    ScanErrorSummaryComponent,
    ScanErrorDetailComponent,
    ScanVehicleComponent,
    ScanFieldComponent,
    ScanServiceComponent,
    ScanStyleComponent,
    ScanQueueComponent
  ],
  providers: [
    ScanningService,
  ],
  exports: []
})
export class SiteScanModule {
}
