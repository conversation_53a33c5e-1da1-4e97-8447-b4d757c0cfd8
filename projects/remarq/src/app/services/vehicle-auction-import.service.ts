import { Injectable } from '@angular/core';
import {
  AucaAuction,
  AucaAuctionLot,
  AucaAuctionSearchDTO,
  AuctionLotStatus,
  InventUserDTO
} from '../interfaces/auction-import';
import { ApiService } from "../global/services/index";
import { DataService } from "./data.service";
import { SearchResultDTO } from '../global/interfaces';
import { AucaSearchDTO } from "../interfaces/auction-import";

@Injectable({
  providedIn: 'root'
})
export class VehicleAuctionImportService {
  private apiUrl = '/api/auction-import';

  // Component constants for different vehicle lists
  private readonly COMPONENT_AVAILABLE_LOTS = "AvailableLots";
  private readonly COMPONENT_IMPORTED_VEHICLES = "ImportedVehicles";
  private readonly COMPONENT_PROVISIONAL_VEHICLES = "ProvisionalVehicles";
  private readonly COMPONENT_SOLD_VEHICLES = "SoldVehicles";
  private readonly COMPONENT_REMOVED_VEHICLES = "RemovedVehicles";

  constructor(private apiClient: ApiService, private data: DataService) { }

  async importAucaData(inventUserId): Promise<any> {
    return this.apiClient.get({
      url: `${this.data.apiUrl}${this.apiUrl}/start/${inventUserId}`
    });
  }

  async getInventUsers(): Promise<InventUserDTO[]> {
    return this.apiClient.get({
      url: `${this.data.apiUrl}${this.apiUrl}/invent-users`
    });
  }

  async searchAsync(searchDTO: AucaSearchDTO): Promise<SearchResultDTO> {
    try {
      const url = `${this.data.apiUrl}${this.apiUrl}/search`;
      const response = await this.apiClient.post({
        url,
        data: searchDTO,
        headers: { 'Content-Type': 'application/json' }
      }) as SearchResultDTO;

      return response;
    } catch (error) {
      console.error('Error searching vehicles:', error);
      // Fall back to mock data in case of error
      const filteredVehicles = this.filterMockLots(searchDTO)
      return {
        results: filteredVehicles.slice(
          searchDTO.offset || 0,
          (searchDTO.offset || 0) + (searchDTO.limit || filteredVehicles.length)
        ),
        totalItems: filteredVehicles.length
      };
    }
  }

  async getAvailableLots(inventUserId: string, offset: number = 0, limit: number = 10, searchTerm?: string): Promise<SearchResultDTO> {
    const searchDTO: AucaSearchDTO = {
      offset,
      limit,
      component: this.COMPONENT_AVAILABLE_LOTS,
      filters: {
        searchTerm,
        inventUserId
      }
    };

    try {
      return await this.searchAsync(searchDTO);
    } catch (error) {
      console.error('Error fetching available lots:', error);
      const mockItems = this.filterMockLots(searchDTO);
      return {
        results: mockItems.slice(offset, offset + limit),
        totalItems: mockItems.length
      };
    }
  }

  async getImportedVehicles(
    inventUserId: string,
    offset: number = 0,
    limit: number = 10,
    searchTerm?: string,
    dateFrom?: Date,
    dateTo?: Date
  ): Promise<SearchResultDTO> {
    const searchDTO: AucaSearchDTO = {
      offset,
      limit,
      component: this.COMPONENT_IMPORTED_VEHICLES,
      filters: {
        searchTerm,
        dateFrom,
        dateTo,
        inventUserId
      }
    };

    try {
      return await this.searchAsync(searchDTO);
    } catch (error) {
      console.error('Error fetching imported vehicles:', error);
      const mockItems = this.filterMockLots(searchDTO);
      return {
        results: mockItems.slice(offset, offset + limit),
        totalItems: mockItems.length
      };
    }
  }

  /**
   * Get provisional vehicles
   */
  async getProvisionalVehicles(
    inventUserId: string,
    offset: number = 0,
    limit: number = 10,
    searchTerm?: string,
    dateFrom?: Date,
    dateTo?: Date
  ): Promise<SearchResultDTO> {
    const searchDTO: AucaSearchDTO = {
      offset,
      limit,
      component: this.COMPONENT_PROVISIONAL_VEHICLES,
      filters: {
        searchTerm,
        dateFrom,
        dateTo,
        inventUserId
      }
    };

    try {
      return await this.searchAsync(searchDTO);
    } catch (error) {
      console.error('Error fetching provisional vehicles:', error);
      const mockItems = this.filterMockLots(searchDTO);
      return {
        results: mockItems.slice(offset, offset + limit),
        totalItems: mockItems.length
      };
    }
  }

  async getSoldVehicles(
    inventUserId: string,
    recentOnly: boolean = false,
    offset: number = 0,
    limit: number = 10,
    searchTerm?: string,
    dateFrom?: Date,
    dateTo?: Date
  ): Promise<SearchResultDTO> {
    const searchDTO: AucaSearchDTO = {
      offset,
      limit,
      component: this.COMPONENT_SOLD_VEHICLES,
      filters: {
        searchTerm,
        dateFrom,
        dateTo,
        inventUserId
      }
    };

    // Add a custom property to indicate recent only
    // This will be handled by the backend
    searchDTO['recentOnly'] = recentOnly;

    try {
      return await this.searchAsync(searchDTO);
    } catch (error) {
      console.error('Error fetching sold vehicles:', error);
      const mockItems = this.filterMockLots(searchDTO);
      return {
        results: mockItems.slice(offset, offset + limit),
        totalItems: mockItems.length
      };
    }
  }

  async getRemovedVehicles(
    inventUserId: string,
    offset: number = 0,
    limit: number = 10,
    searchTerm?: string
  ): Promise<SearchResultDTO> {
    const searchDTO: AucaSearchDTO = {
      offset,
      limit,
      component: this.COMPONENT_REMOVED_VEHICLES,
      filters: {
        searchTerm,
        inventUserId
      }
    };

    try {
      return await this.searchAsync(searchDTO);
    } catch (error) {
      console.error('Error fetching removed vehicles:', error);
      const mockItems = this.filterMockLots(searchDTO);
      return {
        results: mockItems.slice(offset, offset + limit),
        totalItems: mockItems.length
      };
    }
  }

  // Import operation endpoints
  async importVehicles(inventUserId: string, aucaLotIds: string[]): Promise<any> {
    try {
      return await this.apiClient.post({
        url: `${this.data.apiUrl}${this.apiUrl}/process`,
        data: {
          inventUserId: inventUserId,
          vehicleIds: [],
          aucaLotIds: aucaLotIds
        },
        headers: { 'Content-Type': 'application/json' }
      });
    } catch (error) {
      console.error('Error in importVehicles:', error);
      throw error; // Re-throw to let the caller handle it
    }
  }


  async removeVehicles(vehicleIds: string[]): Promise<any> {
    try {
      return await this.apiClient.post({
        url: `${this.data.apiUrl}${this.apiUrl}/remove`,
        data: { vehicleIds }
      });
    } catch (error) {
      console.error('Error removing vehicles:', error);
      return { success: false, error: error.message };
    }
  }

  async markAsActioned(vehicleId: string): Promise<any> {
    try {
      return await this.apiClient.post({
        url: `${this.data.apiUrl}${this.apiUrl}/mark-actioned/${vehicleId}`,
        data: {}
      });
    } catch (error) {
      console.error('Error marking vehicle as actioned:', error);
      return { success: false, error: error.message };
    }
  }

  async withdrawVehicles(vehicleIds: string[]): Promise<any> {
    try {
      return await this.apiClient.post({
        url: `${this.data.apiUrl}${this.apiUrl}/withdraw`,
        data: { vehicleIds }
      });
    } catch (error) {
      console.error('Error withdrawing vehicles:', error);
      return { success: false, error: error.message };
    }
  }

  async withdrawVehicle(vehicleId: string): Promise<any> {
    try {
      return await this.apiClient.post({
        url: `${this.data.apiUrl}${this.apiUrl}/withdraw/${vehicleId}`,
        data: {}
      });
    } catch (error) {
      console.error('Error withdrawing vehicle:', error);
      return { success: false, error: error.message };
    }
  }

  async restoreVehicle(vehicleId: string): Promise<any> {
    try {
      return await this.apiClient.post({
        url: `${this.data.apiUrl}${this.apiUrl}/restore/${vehicleId}`,
        data: {}
      });
    } catch (error) {
      console.error('Error restoring vehicle:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update the reserve price of a vehicle
   * @param vehicleId The ID of the vehicle to update
   * @param reservePrice The new reserve price
   */
  async updateReservePrice(vehicleId: string, reservePrice: number | null): Promise<any> {
    try {
      console.log("Updating:? ", vehicleId, " - , reserve price: ", reservePrice);

      return await this.apiClient.post({
        url: `${this.data.apiUrl}${this.apiUrl}/${vehicleId}/update-reserve-price`,
        data: { reservePrice }
      });
    } catch (error) {
      console.error('Error updating reserve price:', error);
      // For now, simulate a successful update since the API endpoint might not exist yet
      return { success: true };
    }
  }

  /**
   * Get all AucaAuction records
   * @returns SearchResultDTO containing AucaAuction records
   */
  async getAllAuctions(): Promise<SearchResultDTO> {
    try {
      const searchDTO: AucaAuctionSearchDTO = {
        component: 'AUCTION_LIST'
      };

      const url = `${this.data.apiUrl}${this.apiUrl}/search-auctions`;
      const response = await this.apiClient.post({
        url,
        data: searchDTO,
        headers: { 'Content-Type': 'application/json' }
      }) as SearchResultDTO;

      return response;
    } catch (error) {
      console.error('Error fetching auctions:', error);
      // Return empty result in case of error
      return {
        results: [],
        totalItems: 0
      };
    }
  }


  private filterMockLots(searchDTO: AucaSearchDTO): AucaAuctionLot[] {
    let lots = this.getMockAucaLots();

    // Apply component filter
    if (searchDTO.component) {
      switch (searchDTO.component) {
        case this.COMPONENT_AVAILABLE_LOTS:
          lots = lots.filter(lot => lot.status === AuctionLotStatus.Available);
          break;
        case this.COMPONENT_IMPORTED_VEHICLES:
          lots = lots.filter(lot => lot.status === AuctionLotStatus.Imported);
          break;
        case this.COMPONENT_PROVISIONAL_VEHICLES:
          lots = lots.filter(lot => lot.status === AuctionLotStatus.Provisional);
          break;
        case this.COMPONENT_SOLD_VEHICLES:
          lots = lots.filter(lot => lot.status === AuctionLotStatus.Actioned);
          // Handle recentOnly parameter if present
          if (searchDTO['recentOnly']) {
            lots = lots.filter(lot => lot.recentlySold);
          }
          break;
        case this.COMPONENT_REMOVED_VEHICLES:
          lots = lots.filter(lot =>
            lot.status === AuctionLotStatus.Withdrawn ||
            lot.status === AuctionLotStatus.Error
          );
          break;
      }
    }

    // Apply search term filter
    if (searchDTO.filters?.searchTerm) {
      const searchTerm = searchDTO.filters.searchTerm.toLowerCase();
      lots = lots.filter(lot =>
        lot.vrm.toLowerCase().includes(searchTerm) ||
        lot.manufacturer.toLowerCase().includes(searchTerm) ||
        lot.model.toLowerCase().includes(searchTerm) ||
        (lot.variant && lot.variant.toLowerCase().includes(searchTerm))
      );
    }

    // Apply date filters
    if (searchDTO.filters?.dateFrom) {
      const fromDate = new Date(searchDTO.filters.dateFrom);
      lots = lots.filter(lot => {
        const soldDate = lot.soldDate ? new Date(lot.soldDate) : null;
        return soldDate ? soldDate >= fromDate : true;
      });
    }

    if (searchDTO.filters?.dateTo) {
      const toDate = new Date(searchDTO.filters.dateTo);
      lots = lots.filter(lot => {
        const soldDate = lot.soldDate ? new Date(lot.soldDate) : null;
        return soldDate ? soldDate <= toDate : true;
      });
    }

    return lots;
  }

  /**
   * Generate mock auction lots for development/testing
   */
  private getMockAucaLots(): AucaAuctionLot[] {
    const makes = ['Audi', 'BMW', 'Ford', 'Honda', 'Mercedes', 'Toyota', 'Volkswagen'];
    const models = {
      'Audi': ['A3', 'A4', 'Q5', 'Q7'],
      'BMW': ['3 Series', '5 Series', 'X3', 'X5'],
      'Ford': ['Focus', 'Fiesta', 'Kuga', 'Mondeo'],
      'Honda': ['Civic', 'CR-V', 'Jazz', 'HR-V'],
      'Mercedes': ['A-Class', 'C-Class', 'E-Class', 'GLC'],
      'Toyota': ['Corolla', 'RAV4', 'Yaris', 'Prius'],
      'Volkswagen': ['Golf', 'Polo', 'Tiguan', 'Passat']
    };
    const variants = {
      'A3': ['1.5 TFSI Sport', '2.0 TDI S Line', '1.4 TFSI SE'],
      'A4': ['2.0 TDI Sport', '3.0 TDI Quattro', '2.0 TFSI S Line'],
      '3 Series': ['320d M Sport', '330i xDrive', '318i SE'],
      '5 Series': ['520d M Sport', '530e Hybrid', '540i xDrive'],
      'Focus': ['1.0 EcoBoost Titanium', '1.5 TDCi ST-Line', '1.0 EcoBoost Zetec'],
      'Fiesta': ['1.0 EcoBoost ST-Line', '1.5 TDCi Titanium', '1.1 Zetec'],
      'Civic': ['1.0 VTEC Turbo SR', '1.5 VTEC Sport', '1.6 i-DTEC EX'],
      'CR-V': ['1.5 VTEC Turbo SE', '2.0 Hybrid SR', '1.5 VTEC EX'],
      'A-Class': ['A180d AMG Line', 'A200 Sport', 'A250 AMG'],
      'C-Class': ['C220d AMG Line', 'C300e Hybrid', 'C200 Sport'],
      'Corolla': ['1.8 Hybrid Icon', '2.0 Hybrid Design', '1.2T Icon Tech'],
      'RAV4': ['2.5 Hybrid Dynamic', '2.0 D-4D Icon', '2.5 Hybrid Excel'],
      'Golf': ['1.5 TSI Life', '2.0 TDI R-Line', '1.0 TSI Match'],
      'Polo': ['1.0 TSI SE', '1.6 TDI SEL', '1.0 Match']
    };

    const bodyTypes = ['Hatchback', 'Saloon', 'Estate', 'SUV', 'Coupe', 'Convertible'];
    const colors = ['Black', 'White', 'Silver', 'Grey', 'Blue', 'Red', 'Green'];
    const fuelTypes = ['Petrol', 'Diesel', 'Hybrid', 'Electric'];
    const transmissions = ['Manual', 'Automatic', 'Semi-Automatic'];

    const lots: AucaAuctionLot[] = [];

    // Generate 50 random auction lots
    for (let i = 0; i < 50; i++) {
      const manufacturer = makes[Math.floor(Math.random() * makes.length)];
      const model = models[manufacturer][Math.floor(Math.random() * models[manufacturer].length)];
      const variant = variants[model] ?
        variants[model][Math.floor(Math.random() * variants[model].length)] :
        '1.0 Base Model';

      const year = 2018 + Math.floor(Math.random() * 5); // 2018-2022
      const reservePrice = 10000 + Math.floor(Math.random() * 40000); // £10,000-£50,000
      const currentBid = Math.random() > 0.5 ? reservePrice * 0.9 + Math.floor(Math.random() * 5000) : null;
      const mileage = 1000 + Math.floor(Math.random() * 50000); // 1,000-51,000 miles

      // Generate a random UK VRM
      const letters = 'ABCDEFGHJKLMNOPRSTUVWXYZ';
      const vrm = `${letters.charAt(Math.floor(Math.random() * letters.length))}${letters.charAt(Math.floor(Math.random() * letters.length))}${Math.floor(Math.random() * 10)}${Math.floor(Math.random() * 10)}${letters.charAt(Math.floor(Math.random() * letters.length))}${letters.charAt(Math.floor(Math.random() * letters.length))}${letters.charAt(Math.floor(Math.random() * letters.length))}`;

      // Generate a random VIN
      const vin = `WVWZZZ${Math.floor(Math.random() * 10)}${Math.floor(Math.random() * 10)}${Math.floor(Math.random() * 10)}${Math.floor(Math.random() * 10)}${Math.floor(Math.random() * 10)}${Math.floor(Math.random() * 10)}${Math.floor(Math.random() * 10)}${Math.floor(Math.random() * 10)}${Math.floor(Math.random() * 10)}`;

      // Random selections for additional properties
      const bodyType = bodyTypes[Math.floor(Math.random() * bodyTypes.length)];
      const colour = colors[Math.floor(Math.random() * colors.length)];
      const fuelType = fuelTypes[Math.floor(Math.random() * fuelTypes.length)];
      const transmission = transmissions[Math.floor(Math.random() * transmissions.length)];

      // Generate a random auction ID (GUID format)
      const auctionId = `${Math.random().toString(36).substring(2, 15)}-${Math.random().toString(36).substring(2, 15)}-${Math.random().toString(36).substring(2, 15)}-${Math.random().toString(36).substring(2, 15)}`;
      const auctionTitle = `${manufacturer} ${model} Auction`;
      const auctionDateTime = new Date();
      auctionDateTime.setDate(auctionDateTime.getDate() - Math.floor(Math.random() * 30));

      // Determine status
      let status: number;
      let soldDate: Date | null = null;
      let processedDate: Date | null = null;
      let recentlySold = false;

      const statusRandom = Math.random();
      if (statusRandom < 0.3) {
        status = AuctionLotStatus.Available;
      } else if (statusRandom < 0.6) {
        status = AuctionLotStatus.Imported;
        processedDate = new Date();
        processedDate.setDate(processedDate.getDate() - Math.floor(Math.random() * 14));
      } else if (statusRandom < 0.75) {
        status = AuctionLotStatus.Provisional;
        soldDate = new Date();
        soldDate.setDate(soldDate.getDate() - Math.floor(Math.random() * 7));
        processedDate = soldDate;
        // Recently sold if in the last 3 days
        recentlySold = Math.floor(Math.random() * 7) <= 3;
      } else if (statusRandom < 0.9) {
        status = AuctionLotStatus.Actioned;
        // Generate a random sold date in the last 30 days
        const daysAgo = Math.floor(Math.random() * 30);
        soldDate = new Date();
        soldDate.setDate(soldDate.getDate() - daysAgo);
        processedDate = new Date(soldDate);
        processedDate.setDate(processedDate.getDate() + Math.floor(Math.random() * 3) + 1);
        // If sold in the last 3 days, mark as recently sold
        recentlySold = daysAgo <= 3;
      } else {
        // 50% chance of withdrawn vs deleted
        status = Math.random() > 0.5 ? AuctionLotStatus.Withdrawn : AuctionLotStatus.Error;
      }

      lots.push({
        id: `${Math.random().toString(36).substring(2, 15)}-${Math.random().toString(36).substring(2, 15)}-${Math.random().toString(36).substring(2, 15)}-${Math.random().toString(36).substring(2, 15)}`,
        auctionId,
        vrm,
        vin,
        manufacturer,
        model,
        variant,
        year,
        mileage,
        bodyType,
        colour,
        fuelType,
        transmission,
        reservePrice,
        currentBid,
        status,
        statusText: this.getStatusText(status),
        auctionTitle,
        auctionDateTime,
        defaultImageUrl: `https://source.unsplash.com/featured/800x600?car,${manufacturer},${model}`,
        processedDate,
        soldDate,
        recentlySold,
        imageUrls: []
      });
    }

    // Sort by status and date
    return lots.sort((a, b) => {
      // First by status
      if (a.status !== b.status) {
        if (a.status === AuctionLotStatus.Available) return -1;
        if (b.status === AuctionLotStatus.Available) return 1;
        if (a.status === AuctionLotStatus.Imported) return -1;
        if (b.status === AuctionLotStatus.Imported) return 1;
        if (a.status === AuctionLotStatus.Provisional) return -1;
        if (b.status === AuctionLotStatus.Provisional) return 1;
        if (a.status === AuctionLotStatus.Actioned) return -1;
        if (b.status === AuctionLotStatus.Actioned) return 1;
      }

      // Then by recently sold
      if (a.recentlySold && !b.recentlySold) return -1;
      if (!a.recentlySold && b.recentlySold) return 1;

      // Then by sold date (newest first)
      if (a.soldDate && b.soldDate) {
        return b.soldDate.getTime() - a.soldDate.getTime();
      }

      return 0;
    });
  }

  /**
   * Get status text from status code
   */
  private getStatusText(status: number): string {
    switch (status) {
      case AuctionLotStatus.Available:
        return 'Available';
      case AuctionLotStatus.Imported:
        return 'Imported';
      case AuctionLotStatus.Provisional:
        return 'Provisional';
      case AuctionLotStatus.Actioned:
        return 'Actioned';
      case AuctionLotStatus.Withdrawn:
        return 'Withdrawn';
      case AuctionLotStatus.Error:
        return 'Deleted';
      default:
        return 'Unknown';
    }
  }
}
