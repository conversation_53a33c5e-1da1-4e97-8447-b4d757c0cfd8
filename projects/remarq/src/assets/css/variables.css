:root {

  [data-amplify-authenticator] {
    --amplify-colors-font-primary: #21303D;
    --amplify-components-button-primary-background-color: var(--contrastColor);
    --amplify-components-authenticator-router-background-color: #f7f7f7;
    --amplify-components-tabs-item-active-color: var(--contrastColor);
    --amplify-components-tabs-item-active-border-color: var(--contrastColor);
    --amplify-components-tabs-item-hover-color: var(--contrastColor);
    --amplify-components-authenticator-router-border-width: 0;
    --amplify-components-authenticator-router-box-shadow: none;
    --amplify-components-divider-label-background-color: #f7f7f7;
    --amplify-components-fieldcontrol-border-width: 0px;
    --amplify-colors-background-primary: #ffffff;

    color: #21303D;

    .amplify-label { display: none; }

    .amplify-authenticator__subtitle {
      font-weight: 400;
    }

    .amplify-field {
      .amplify-input:not(button) {
        font-weight: 500;
        background-color: #EBEBF0;
        padding: 15px;
        border-radius: 16px;
      }
    }

    .amplify-field-group {

      button {
        background-color: #EBEBF0;
      }

      input:first-of-type {
        border-radius: 16px 0 0 16px;
      }

      button {
        border-radius: 0px 16px 16px 0;
      }
    }

    .amplify-button.amplify-button--primary {
      font-weight: 500 !important;
      margin-top: 10px;
      padding: 15px;
      border-radius: 28px;

    }

    .amplify-label {
      font-weight: 500;
    }

    .amplify-divider {
      opacity: 100% !important;
      color: var(--textColour);
    }

    .amplify-divider::after {
      font-weight: 500;
      margin-top: 1rem;
    }

    .federated-sign-in-button {
      background-color: #fff;
      border: 0px solid #fff;
      border-radius: 25px;
      padding: 12px;
    }

    .federated-sign-in-container {
      border-bottom: 1px solid #dfdfdf;
      padding-bottom: 1rem;
      margin-bottom: 2rem;

      .amplify-button {

        .amplify-text {
          font-weight: 500;
          color: var(--linkColour);
        }
      }
    }
  }


  /* Pallette */
  --colour1: #191c1f;
  --colour2: #6698c8;
  --colour3: #f7f7f7;
  --colour4: #ffffff;
  --colour5: #4f55f1;
  --colour6: #e9ecef;
  --colour7: #747b86;
  --colour8: #75808A;
  --colour9: #505a63;
  --colour10: #717173;
  --colour11: #dBdBe0;
  --colour12: #e8f0fc;
  --colour12h: #e0e8f2;
  --colour13: #2c64e3;
  --colour14: #256fde; /* Buy Now */
  --colour15: #56bb6f; /* Timed */
  --colour16: #ff9500; /* Managed */
  --colour17: #f25055; /* Managed */
  --colour18: #0d66e7;

  --contrastBoxBackgroundColour: var(--colour18);

  --headerBackgroundColour: var(--colour7);

  --successColour: var(--colour15);
  --warningColour: var(--colour16);
  --errorColour: var(--colour17);
  --dangerColour: var(--colour17);

  --input-radius: 12px;

  --contrastColor: var(--colour5);


  --bgColour: var(--colour3);
  --textColour: var(--colour1);

  --primaryButtonColour: var(--colour5);
  --primaryButtonTextColour: var(--colour4);

  --secondaryButtonBorderColour: var(--colour12);
  --secondaryButtonBackgroundColour: var(--colour12);
  --secondaryButtonTextColour: var(--colour13);
  --secondaryButtonHoverBackgroundColour: var(--colour12h);
  --secondaryButtonHoverTextColour: var(--colour13);

  --tertiaryButtonBackgroundColour: var(--colour1);
  --tertiaryButtonHoverBackgroundColour: var(--colour6);
  --tertiaryButtonTextColour: #fff;

  --smallCheckboxColour: var(--primaryButtonColour);

  --btn-xxs-height: 17px;
  --btn-xxs-radius: calc(var(--btn-xxs-height) / 2);
  --btn-xxs-line-height: calc(var(--btn-xxs-height) - (var(--btn-xxs-vpadding) * 2));
  --btn-xxs-vpadding: 2px;
  --btn-xxs-hpadding: 6px;
  --btn-xxs-padding: var(--btn-xxs-vpadding) var(--btn-xxs-hpadding);
  --btn-xxs-fontSize: 0.7rem;
  --input-xxs-line-height: var(--btn-xxs-line-height);
  --input-xxs-radius: var(--btn-xxs-radius);
  --input-xxs-height: var(--btn-xxs-height);

  --btn-xs-height: 27px;
  --btn-xs-fontSize: 0.875rem;
  --btn-xs-radius: calc(var(--btn-xs-height) / 2);
  --btn-xs-line-height: calc(var(--btn-xs-height) - (var(--btn-xs-vpadding) * 2));
  --btn-xs-vpadding: 3px;
  --btn-xs-hpadding: 10px;
  --btn-xs-padding: var(--btn-xs-vpadding) var(--btn-xs-hpadding);
  --input-xs-line-height: var(--btn-xs-line-height);
  --input-xs-radius: var(--btn-xs-radius);
  --input-xs-height: var(--btn-xs-height);
  --input-xs-padding: var(--btn-xs-vpadding) var(--btn-xs-hpadding);

  --btn-sm-height: 37px;
  --btn-sm-fontSize: 0.875rem;
  --btn-sm-radius: calc(var(--btn-sm-height) / 2);
  --btn-sm-line-height: calc(var(--btn-sm-height) - (var(--btn-sm-vpadding) * 2));
  --btn-sm-vpadding: 6px;
  --btn-sm-hpadding: 12px;
  --btn-sm-padding: var(--btn-sm-vpadding) var(--btn-sm-hpadding);
  --input-sm-radius: var(--btn-sm-radius);
  --input-sm-line-height: var(--btn-sm-line-height);
  --input-sm-height: var(--btn-sm-height);
  --input-sm-padding: var(--btn-sm-vpadding) var(--btn-sm-hpadding);

  --input-vpadding: 6.4px;
  --input-hpadding: 12px;
  --input-padding: var(--input-vpadding) var(--input-hpadding);


  --btn-height: 47px;
  --btn-radius: var(--input-radius);
  --btn-vpadding: 6px;
  --btn-hpadding: 12px;
  --btn-line-height: calc(var(--btn-height) - (var(--btn-vpadding) * 2));
  --input-line-height: var(--btn-line-height);
  --input-height: var(--btn-line-height);

  --input-group-padding: 5px;
  --drop-down-padding: var(--input-group-padding);

  --underNavBgColour: var(--colour4);
  --underNavSelectedBgColour: var(--colour12);
  --backgroundColour: var(--colour3);
  --inputBackgroundColour: var(--colour6);
  --placeholderColour: var(--colour10);
  --textLabelColour: var(--colour10);
  --navSearchBgColour: var(--inputBackgroundColour);
  --navSearchTextColour: var(--textColour);
  --subtitleColour: var(--colour8);
  --footerBackgroundColour: var(--colour1);
  --attentionBoxBackgroundColour: var(--colour6);

  --chipBackgroundColour: var(--backgroundColour);
  --chipBorderColour: #efefef;

  --dropdownDividerColour: var(--colour11);
  --dropdownArrowColour: var(--colour7);
  --dropdownBackgroundColour: var(--colour4);
  --dropdownHoverItemBackgroundColour: var(--colour6);

  --defaultFont: "Poppins";
  --buttonGroupBackgroundColour: var(--colour6);
  --vrmBackgroundColour: #FDC72F;
  --vrmBackgroundColourLight: #FDC72F66;

  /* Modals */
  --modalHeaderBackgroundColour: #fff;
  --modalBackgroundColour: #fff;
  --modalHeaderFontWeight: 500;

  /* Heights */
  --headerHeight: 65px;
  --underNavHeight: 43px;
  --totalHeaderHeight: calc(var(--headerHeight) + var(--underNavHeight));
  --searchMenuWidth: 250px;
  --altDangerColour: #c00;
  --vehicleMediaCoverOrContain: contain;

  /* Widgets */
  --widgetBgColour: var(--colour4);
  --widgetBorderRadius: 12px;
  --widgetPadding: 16px 16px 16px 16px;
  --widgetBorderColour: var(--textColour);

  /* Float Labels */
  --floatLabelBackgroundColour: transparent;
  --floatInputFocusBorderColour: var(--inputBackgroundColour);
  --floatLabelColour: var(--placeholderColour);
  --floatLabelWeight: 400;
  --floatLabelFontSize: 11px;
  --floatLabelPadding: 5px;
  --floatLabelTop: 3px;
  --floatLabelLeft: 7px;
  --floatLabelScale: 0.7;
  --floatInputFocusLabelWeight: 500;
  --floatLabelFocusColour: var(--colour5);
  --floatLabelInputPadding: 19.5px 0.4rem 6.5px 12px;
  --placeholderWeight: 400;
  --placeholderFontSize: 1rem;
  --aspectRatio: 0.75;
  --inverseAspectRatio: calc(1 / 0.75);
  --fontAwesome: "Font Awesome 5 Free";
  --fas: 900;
  --homepageCalendarBackgroundColour: var(--colour1);

  /* Buy Style */
  --buyNowColour: #9c50f2;
  --timedSaleColour: #50a6f2;
  --managedSaleColour: #9c50f2;
  --underwriteColour: #9c50f2;

  /* Switches */
  --switchOnBgColour: var(--colour13);
  --switchOnTextColour: #fff;
  --switchOffBgColour: var(--inputBackgroundColour);
  --switchOffTextColour: var(--textColour);
  --switchColour: var(--colour13);

  /* Tables */
  --tableHoverBackgroundColour: var(--colour3);
  --tableLine2: var(--colour10);


  /* Side Nav */
  --sideNavTextColour: var(--colour9);
  --sideNavFontSize: 13px;
  --sideNavHoverTextColour: var(--colour1);
  --sideNavBackgroundColour: #fff;
  --sideNavHoverBackgroundColour: var(--colour6);

}
