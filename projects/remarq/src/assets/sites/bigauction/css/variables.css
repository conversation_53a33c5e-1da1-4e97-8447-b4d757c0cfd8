:root {

  --font1: "Inter";
  --font2: "roc-grotesk";
  --defaultFont: var(--font1);
  --widgetBorderRadius: 8px;
  --imageOutlineColour: var(--widgetBorderColour);

  /* Palette */
  --colour1: #40697f;
  --colour2: #a2be4f;
  --colour3: rgba(255,255,255,1);
  --colour3a: rgba(255,255,255,0.7);
  --colour3b: rgba(255,255,255,0.35);
  --colour3c: rgba(255,255,255,0.17);
  --colour4: #999999;
  --colour5: #262965;
  --colour6: #f2f2f2;
  --colour7: #2c3849;
  --colour8: #007a5a;
  --colour9: #b7bbc1;
  --colour10: rgba(38, 41, 101, 1);
  --colour10a: rgba(38, 41, 101, 0.7);
  --colour10b: rgba(38, 41, 101, 0.35);
  --colour10c: rgba(38, 41, 101, 0.17);
  --colour10d: rgba(38, 41, 101, 0.08);
  --colour10e: rgba(38, 41, 101, 0.04);
  --colour12: #1264a3;
  --colour13: #f8f8f8;
  --colour14: #e4ebed;
  --colour15: #8c8c8c;
  --colour16: #40697f;
  --colour17: #52D9F2;
  --colour18: #E6F9FD;
  --colour19: #6C6D74;

  --vrmBackgroundColour: #fdfbe9;
  --text-emphasis: var(--colour10);
  --text-plain: var(--colour10a);
  --headerHeight: 66px;
  --headerPaddingTop: 8px;
  --inputBorderColour: var(--colour10b);
  --placeholderColour: var(--colour10b);
  --topbarIconColour: var(--colour17);

  --bgColour: #f5f5f5;
  --primaryButtonColour: var(--colour5);
  --secondaryButtonColour: var(--colour12);
  --tertiaryButtonColour: var(--colour13);

  --sideNavBackgroundColour: var(--colour3);
  --sideNavTextColour: var(--colour5);
  --sideNavHoverBackgroundColour: var(--colour12);
  --sideNavHoverTextColour: var(--colour3);
  --softInputBorderColour: #ced4da;
  --headerColour: var(--colour5);

  --tabFocusColour: var(--colour1);

  --errorColour: #c00;
  --linkColour: var(--colour5);
  --textColour: #292929;
  --textLabelColour: var(--colour10b);

  --dropdownBackgroundColour: var(--colour13);
  --dropdownTextColour: var(--linkColour);
  --dropdownItemBackgroundColour: var(--colour13);
  --dropdownItemTextColour: var(--textColour);

  --primaryColour: var(--colour1);
  --secondaryColour: var(--colour12);
  --tertiaryColour: var(--colour13);

  --underNavBgColour: #fff;
  --underNavTextColour: var(--colour5);

  --dropdownHoverItemBackgroundColour: var(--colour12);
  --dropdownHoverItemTextColour: var(--colour3);

  --navbarTextHoverColour: var(--colour17);
  --navbarTextColour: var(--linkColour);

  --attentionBoxBackgroundColour: var(--colour14);
  --widgetBorderColour: var(--colour10c);
  --navSearchBgColour: var(--colour3);
  --navSearchTextColour: var(--colour10);
  --navbarBgColour: #ffffff;
  --footerBackgroundColour: #fff;
  --successColour: #080;
  --dangerColour: #800;
  --actionColour: var(--colour2);
  --floatLabelColour: #757575;
  --highlightedRowBackgroundColour: lightgoldenrodyellow;

  --buyNowColour: #FFB154;
  --timedSaleColour: #51ADDF;
  --managedSaleColour: #606DE4;
  --underwriteSaleColour: #d97ad5;

  --switchColour: var(--colour12);

  /*

  --vrmBackgroundColour: #eed85b;
  --underNavTextColour:
  --navbarTextHoverColour:
  --navbarTextColour:

  --footerBackgroundColour:

  --feintColour:
  --highlightTextColour:

  --inputTextColour:
  --inputBackgroundColour:
  --textLabelColour:
  --actionColour:
  --actionColourHighlight:
  --inputBorderColour:

  --floatLabelFocusColour:
  --floatLabelBackgroundColour:
  --floatInputFocusBorderColour:
  --floatInputFocusLabelColour:
  --defaultFont:

  --dangerColour:
  --successColour:
  --warningColour:

  --widgetBgColour:
  --smallCheckboxColour:
  --btnGroupBgColour:
  --tabFocusColour:
  --tableHeaderColour:
  --tableHeaderBackgroundColour:
  --tableHeaderDividerColour:
  --dialogCloseIconColour:
  --imageOutlineColour:
  --danger:
  --backgroundHighlight:
   */
}

.device {
  --headerHeight: 96px;
  --headerPaddingTop: 30px;
}


