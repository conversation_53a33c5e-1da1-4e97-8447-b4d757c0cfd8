#site-toyotagb {

  .text-danger {
    color: var(--errorColour) !important;
  }


  #homepage {

    .header-line-3 {
      color: var(--colour20);
    }


    .block-1, .block-3 {
      background-color: #f0f0f0 !important;
    }

    .inner-block-1 {
      background-image: none;
    }

    .btn-sign-up {
      background-color: var(--colour20);

    }

    .btn-login {
      background-color: #ddd;

    }
  }

  .undernav {

    box-shadow: 0 0 5px 0 rgb(0 0 0 / 16%) !important;

  }

  .undernav-item {
    font-weight: 600;
    font-size: 15px;
  }

  #admin-side-nav {
    .nav-link {
      font-weight: 600;
      line-height: 23px !important;
    }
  }

  .mdb-select-toggle::before, .mdb-select-arrow::before {
    color: var(--topbarIconColour) !important;
  }


  .btn-tertiary {
    color: var(--colour3);

    &:hover {
      color: var(--colour3) !important
    }
  }

  .badge {
    color: #fff !important;
  }

  .btn-primary {
    &:hover {
      background-color: var(--colour12) !important;
    }
  }

  .btn-outline-primary, .btn-primary-outline, .btn-secondary-outline {
    border: 1px solid var(--primaryButtonColour) !important;
    color: var(--primaryButtonColour) !important;

    &:hover {
      color: var(--colour3) !important;
      background-color: var(--primaryButtonColour) !important;
    }

    &.active {
      color: var(--colour3) !important;
    }
  }

  .navbar {

    min-height: var(--headerHeight) !important;
    padding-top: var(--headerPaddingTop) !important;
    box-sizing: border-box;

  }

  #mobile {

    nav {
      border-bottom: 0px solid #fff !important;
    }

    #login-component {
      background-color: #fff;
    }
  }

  .navbar-brand {
    background-image: url('/assets/sites/toyotagb/images/Toyota_EU.svg');
    background-repeat: no-repeat;
    background-size: contain;
    height: 45px;
    width: 202px;
    margin-right: 0;
  }

  .sale-table-wrapper {
    background-color: #eee;

    td {
      color: #333;
    }
  }


  #bidbox {

    border-radius: 8px;

  }

  .grade {
    font-weight: 600;
    font-size: 24px;
  }

  .grade-bg {
    border-radius: 8px !important;
  }

  .watchlist-state {
    color: var(--colour20);

    &:hover {
      color: #fff;
      background-color: var(--colour20);
    }
  }

  .sales-calendar-button {
    background-color: var(--colour1);
  }

  .nav-label {
    font-weight: 600;
    font-size: 15px;
  }

  #mobile {
    #advert-search-main-panel {
      background-color: #fff;
    }
  }

  .search-box {

    .search-text-input {

      border: 1px solid #e6e6e6 !important;

      &::placeholder {
        color: var(--colour10a) !important;
      }
    }

    .input-group-append {
      background-color: var(--colour10c) !important;

      .search-submit {
        color: var(--colour10b) !important;
      }
    }
  }

  .chosen-items {
    color: var(--colour16) !important;
    font-size: 12px !important;
    font-weight: 700;
  }

  .btn.btn-rounded {
    border-radius: 19px;
    padding-left: 15px;
    padding-right: 15px;

    &.btn-sm {
      border-radius: 13px;
      padding-left: 10px;
      padding-right: 10px;
    }
  }

  .valuation-container {
  }

  .view-results {
  }

  h2, .sub-header {
    font-weight: 600;
  }

  table td .table-line-1, table.table-compressed td .table-line-1 {

    font-weight: 600;

  }

  footer.page-footer {
    h5 {
      font-size: 1rem !important;
      font-weight: 700 !important;
    }
  }

  .buynow {

    background-color: var(--attentionBoxBackgroundColour) !important;
    color: var(--linkColour) !important;

  }
}
