:root {

  --headerHeight: 64px;
  --underNavHeight: 30px;
  --colour1: #269ECE;
  --colour2: #1AB075;
  --colour3: #ffffff;
  --colour4: #3f8828;
  --colour5: #333;
  --colour6: #f1c40f;
  --colour7: #1F2424;
  --colour8: #1F2424;
  --colour9: #e85d75;

  --dangerColour: #dc3545;

  --inputBorderColour: #dadce0;

  --primaryButtonColour: var(--colour2);
  --secondaryButtonColour: var(--colour1);
  --textColour: var(--colour7);
  --errorColour: var(--dangerColour);
  --successColour: var(--colour2);
  --underNavBgColour: var(--colour1);
  --underNavTextColour: #fff;
  --softInputBorderColour: #ced4da;
  --linkColour: var(--colour1);
  --bgColour: #f1f1f1;
  --widgetBorderColour: #e4e4e4;
  --sideNavTextColour: var(--textColour);
  --vrmBackgroundColour: #FFFFCC;

  --buyNowColour: var(--primaryButtonColour);
  --timedSaleColour: #0e2839;
  --managedSaleColour: #1e3b4e;
  --underwriteSaleColour: #476375;
  --footerBackgroundColour: #ddd;
  --footerTextColour: #888;
  --navbarBgColour: #fff;
  --navSearchBgColour: #f4f4f4;
  --sideNavBackgroundColour: #fff;
  --sideNavHoverBackgroundColour: var(--colour1);
  --attentionBoxBackgroundColour: #F7F9FC;
  --topbarIconColour: var(--secondaryButtonColour);
  --primaryColour: var(--colour2);
  --secondaryColour: var(--colour1);
  --dropdownBackgroundColour: #fff;
  --dropdownHoverItemBackgroundColour: var(--secondaryButtonColour);
  --dropdownHoverItemTextColour: #fff;

}

