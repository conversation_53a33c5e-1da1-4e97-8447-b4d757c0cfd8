.top-grid {
  display: grid;
  grid-gap: 5px;
  grid-template-columns: repeat(auto-fit, minmax(300px, auto));

  .grid-label {
    font-weight: 500;
    text-transform: uppercase;
    color: #bbb;
    font-size: 0.75rem;
  }

  .grid-value {
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 0.75rem;
  }
}

.top-grid-2 {
  display: grid;
  grid-gap: 5px;
  grid-template-columns: repeat(auto-fit, minmax(200px, auto));

  .grid-label {
    font-weight: 500;
    text-transform: uppercase;
    color: #bbb;
    font-size: 0.75rem;
    line-height: 0.7rem;
  }

  .grid-value {
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 0.75rem;
  }
}

.modal-near-full-size {

  max-width: 95% !important;
  max-height: 95% !important;

  .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
}

.zoom-info { font-size: 0.875rem; }

.image-grid {

  display: grid;
  grid-gap: 15px;
  grid-template-columns: repeat(auto-fill, minmax(300px, auto));
}

.image-cell {
  border-radius: 5px;
}

.image-label {
  background-color: #888;
  display: inline-block;
  padding: 0 5px;
  color: #fff;
  font-size: 0.85rem;
  border-radius: 5px 5px 0 0;
}

.image-item {
  width: 100%;
  margin-top: -1px;
  height: calc(300px * var(--aspectRatio));
  background-size: cover;
  background-position: center;
  border-radius: 0 5px 5px 5px;
}

h1.section-title {

  font-size: 25px;
  letter-spacing: -1px;
  font-weight: 500;
  margin-bottom: 0px;
  color: #03536E;

}

h2.section-title {

  font-size: 21px;
  letter-spacing: -0.5px;
  font-weight: 500;
  margin-bottom: 0px;
  color: #03536E;

}



